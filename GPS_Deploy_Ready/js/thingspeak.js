// ThingSpeak API 交互类
class ThingSpeakAPI {
    constructor() {
        this.baseUrl = CONFIG.THINGSPEAK.BASE_URL;
        this.channelId = CONFIG.THINGSPEAK.CHANNEL_ID;
        this.readApiKey = CONFIG.THINGSPEAK.READ_API_KEY;
        this.writeApiKey = CONFIG.THINGSPEAK.WRITE_API_KEY;
    }

    // 获取最新的GPS数据
    async getLatestData() {
        try {
            const url = `${this.baseUrl}/channels/${this.channelId}/feeds/last.json?api_key=${this.readApiKey}`;
            
            log(`正在获取最新数据: ${url}`);
            
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (!data || !data.created_at) {
                throw new Error('没有可用数据');
            }
            
            // 解析GPS数据
            const gpsData = this.parseGPSData(data);
            log('✅ ThingSpeak数据获取成功:', 'success');
            log(`📍 位置: ${gpsData.latitude}°N, ${gpsData.longitude}°E`);
            log(`⏰ 时间: ${gpsData.timestamp}`);
            log(gpsData);
            
            return gpsData;
            
        } catch (error) {
            log(`获取数据失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 获取历史数据(用于轨迹显示)
    async getHistoryData(results = 20) {
        try {
            const url = `${this.baseUrl}/channels/${this.channelId}/feeds.json?api_key=${this.readApiKey}&results=${results}`;
            
            log(`正在获取历史数据: ${results}条记录`);
            
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (!data.feeds || data.feeds.length === 0) {
                log('没有历史数据', 'warn');
                return [];
            }
            
            // 解析所有GPS数据点
            const trackData = data.feeds
                .map(feed => this.parseGPSData(feed))
                .filter(point => point.latitude && point.longitude); // 过滤无效数据
            
            log(`获取到${trackData.length}个有效轨迹点`, 'success');
            return trackData;
            
        } catch (error) {
            log(`获取历史数据失败: ${error.message}`, 'error');
            return [];
        }
    }

    // 解析GPS数据
    parseGPSData(feed) {
        const fields = CONFIG.THINGSPEAK.FIELDS;

        const gpsData = {
            timestamp: new Date(feed.created_at),
            latitude: parseFloat(feed[fields.LATITUDE]) || null,
            longitude: parseFloat(feed[fields.LONGITUDE]) || null,
            altitude: parseFloat(feed[fields.ALTITUDE]) || null,
            entry_id: feed.entry_id || null,
            navigation: null,
            distance_to_wangda: null,
            raw: feed
        };

        // 检查field3是否包含到王达广场的距离数据
        if (feed.field3 && parseFloat(feed.field3) > 100) {
            gpsData.distance_to_wangda = parseFloat(feed.field3);
            // 创建导航数据对象
            gpsData.navigation = {
                type: 'navigation',
                destination: 'Wangda Plaza',
                distance: gpsData.distance_to_wangda,
                start: { lat: gpsData.latitude, lng: gpsData.longitude },
                end: { lat: 26.8869, lng: 112.6758 }, // 王达广场坐标
                status: 'active'
            };
            log(`检测到导航数据: 距离王达广场 ${gpsData.distance_to_wangda}米`, 'info');
        }

        // 解析导航数据（如果存在field4）
        if (feed.field4) {
            try {
                // 尝试直接解析JSON
                gpsData.navigation = JSON.parse(feed.field4);
                log('检测到标准JSON导航数据:', 'info');
                log(gpsData.navigation);
            } catch (error) {
                // 尝试解析紧凑格式: R:start_lat,start_lon,end_lat,end_lon 或 N:... 或 NAV:...
                const navStr = feed.field4.trim();
                if (navStr.startsWith('R:')) {
                    try {
                        // 超紧凑格式: R:start_lat,start_lon,end_lat,end_lon
                        const coords = navStr.substring(2).split(',').map(parseFloat);
                        if (coords.length >= 4) {
                            const [startLat, startLon, endLat, endLon] = coords;
                            gpsData.navigation = {
                                start: { lat: startLat, lng: startLon },
                                end: { lat: endLat, lng: endLon },
                                waypoints: [
                                    { lat: startLat, lng: startLon, instruction: "从当前位置出发" },
                                    { lat: endLat, lng: endLon, instruction: "到达目的地：酃湖万达广场" }
                                ],
                                format: 'ultra-compact'
                            };
                            log('检测到超紧凑导航数据 (R:格式):', 'info');
                            log(gpsData.navigation);
                        }
                    } catch (error) {
                        log('解析超紧凑导航数据失败:', 'error');
                        log(error);
                    }
                } else if (navStr.startsWith('N:') || navStr.startsWith('NAV:')) {
                    try {
                        const prefix = navStr.startsWith('N:') ? 'N:' : 'NAV:';
                        const parts = navStr.substring(prefix.length).split('-'); // 移除前缀
                        if (parts.length >= 5) {
                            const [startCoords, endCoords, ...waypointCoords] = parts;
                            const [startLat, startLon] = startCoords.split(',').map(parseFloat);
                            const [endLat, endLon] = endCoords.split(',').map(parseFloat);

                            const waypoints = waypointCoords.map((coords, index) => {
                                const [lat, lon] = coords.split(',').map(parseFloat);
                                return {
                                    lat: lat,
                                    lon: lon,
                                    instruction: `路径点 ${index + 1}`
                                };
                            });

                            gpsData.navigation = {
                                type: 'navigation',
                                start: { lat: startLat, lon: startLon },
                                end: { lat: endLat, lon: endLon },
                                destination: 'Wangda Plaza',
                                waypoints: waypoints
                            };

                            log('成功解析紧凑导航数据:', 'info');
                            log(gpsData.navigation);
                        }
                    } catch (parseError) {
                        log('紧凑导航数据解析失败:', 'warning');
                        log(`原始数据: ${navStr}`);
                    }
                } else {
                    log('未识别的导航数据格式:', 'warning');
                    log(`原始数据: ${navStr}`);
                }
            }
        }

        return gpsData;
    }

    // 发送GPS数据到ThingSpeak (如果需要从前端发送)
    async sendGPSData(latitude, longitude, altitude = null) {
        try {
            const fields = CONFIG.THINGSPEAK.FIELDS;
            const url = `${this.baseUrl}/update.json`;
            
            const data = {
                api_key: this.writeApiKey,
                [fields.LATITUDE]: latitude,
                [fields.LONGITUDE]: longitude
            };
            
            if (altitude !== null) {
                data[fields.ALTITUDE] = altitude;
            }
            
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            
            const result = await response.json();
            log(`数据发送成功, Entry ID: ${result}`, 'success');
            return result;
            
        } catch (error) {
            log(`发送数据失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 获取频道信息
    async getChannelInfo() {
        try {
            const url = `${this.baseUrl}/channels/${this.channelId}.json?api_key=${this.readApiKey}`;
            
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            
            const data = await response.json();
            log('频道信息获取成功', 'success');
            return data;
            
        } catch (error) {
            log(`获取频道信息失败: ${error.message}`, 'error');
            throw error;
        }
    }

    // 检查API连接状态和数据新鲜度
    async checkConnection() {
        try {
            // 首先检查API是否可访问
            await this.getChannelInfo();

            // 然后检查最新数据的时间戳
            const latestData = await this.getLatestData();
            if (!latestData || !latestData.created_at) {
                log('没有找到有效的GPS数据', 'warning');
                return false;
            }

            // 检查数据新鲜度（5分钟内的数据认为是在线）
            const dataTime = new Date(latestData.created_at);
            const now = new Date();
            const timeDiff = (now - dataTime) / 1000 / 60; // 分钟差

            if (timeDiff > 5) {
                log(`数据过期：${timeDiff.toFixed(1)}分钟前的数据`, 'warning');
                return false;
            }

            log(`数据新鲜：${timeDiff.toFixed(1)}分钟前更新`, 'success');
            return true;

        } catch (error) {
            log(`连接检查失败: ${error.message}`, 'error');
            return false;
        }
    }

    // 格式化ThingSpeak时间
    formatThingSpeakTime(timestamp) {
        return Utils.formatTime(new Date(timestamp));
    }
}

// 模拟数据生成器 (用于测试)
class MockDataGenerator {
    constructor() {
        this.baseLatitude = 26.88693;   // 衡阳师范学院纬度 (真正准确位置)
        this.baseLongitude = 112.675813; // 衡阳师范学院经度 (真正准确位置)
        this.baseAltitude = 50;         // 基础海拔
        this.currentIndex = 0;
    }

    // 生成固定GPS数据 (不移动)
    generateMockData() {
        // 固定在衡阳师范学院位置，不进行移动模拟
        // 确保与ThingSpeak数据一致
        const latitude = this.baseLatitude;   // 固定纬度
        const longitude = this.baseLongitude; // 固定经度
        const altitude = this.baseAltitude;   // 固定海拔

        this.currentIndex++;

        // 使用当前时间作为时间戳，确保时间实时更新
        const currentTime = new Date();

        return {
            timestamp: currentTime,
            latitude: parseFloat(latitude.toFixed(6)),
            longitude: parseFloat(longitude.toFixed(6)),
            altitude: parseFloat(altitude.toFixed(1)),
            entry_id: this.currentIndex,
            raw: {
                created_at: currentTime.toISOString(),
                field1: latitude.toFixed(6),
                field2: longitude.toFixed(6),
                field3: altitude.toFixed(1)
            }
        };
    }

    // 生成历史轨迹数据
    generateHistoryData(count = 20) {
        const history = [];
        const originalIndex = this.currentIndex;
        this.currentIndex = 0;

        for (let i = 0; i < count; i++) {
            history.push(this.generateMockData());
        }

        this.currentIndex = originalIndex;
        return history.reverse(); // 按时间顺序排列
    }
}

// 创建全局实例
window.thingSpeakAPI = new ThingSpeakAPI();
window.mockDataGenerator = new MockDataGenerator();

// 导出类
window.ThingSpeakAPI = ThingSpeakAPI;
window.MockDataGenerator = MockDataGenerator;
