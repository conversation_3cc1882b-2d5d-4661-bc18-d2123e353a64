// 主应用程序类
class GPSTrackerApp {
    constructor() {
        this.map = null;
        this.updateInterval = null;
        this.isRunning = false;
        this.lastUpdateTime = null;
        this.connectionStatus = 'offline';
        this.routingService = null; // 路径规划服务
        this.isNavigating = false;  // 导航状态

        this.init();
    }

    // 初始化应用
    async init() {
        try {
            log('GPS追踪应用启动中...', 'info');
            
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initApp());
            } else {
                this.initApp();
            }
            
        } catch (error) {
            log(`应用初始化失败: ${error.message}`, 'error');
            Utils.showNotification('应用初始化失败', 'error');
        }
    }

    // 初始化应用组件
    async initApp() {
        try {
            // 初始化地图
            this.map = new GPSMap('map');

            // 初始化路径规划服务
            this.routingService = new RoutingService();

            // 设置UI事件监听器
            this.setupUIEventListeners();

            // 检查ThingSpeak连接
            await this.checkConnection();

            // 加载历史轨迹
            await this.loadHistoryTrack();

            // 开始数据更新
            this.startUpdating();

            log('应用初始化完成（含路径规划功能）', 'success');
            Utils.showNotification('系统已启动 - 支持OpenStreetMap导航', 'success');
            
        } catch (error) {
            log(`应用组件初始化失败: ${error.message}`, 'error');
            Utils.showNotification('初始化失败，使用模拟数据', 'error');
            
            // 使用模拟数据
            this.startMockMode();
        }
    }

    // 设置UI事件监听器
    setupUIEventListeners() {
        // 居中按钮
        const centerBtn = document.getElementById('center-btn');
        if (centerBtn) {
            centerBtn.addEventListener('click', () => {
                if (this.map.currentMarker) {
                    const position = this.map.currentMarker.getLatLng();
                    this.map.centerOnLocation([position.lat, position.lng]);
                    log('手动居中到当前位置');
                }
            });
        }

        // 刷新按钮
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', async () => {
                refreshBtn.innerHTML = '<div class="loading"></div> 刷新中...';
                await this.updateGPSData();
                refreshBtn.innerHTML = '🔄 刷新数据';
            });
        }

        // 清除轨迹按钮
        const clearTrackBtn = document.getElementById('clear-track-btn');
        if (clearTrackBtn) {
            clearTrackBtn.addEventListener('click', () => {
                this.map.clearTrack();
                Utils.showNotification('轨迹已清除', 'success');
            });
        }

        // 测试导航按钮
        const testNavigationBtn = document.getElementById('test-navigation-btn');
        if (testNavigationBtn) {
            testNavigationBtn.addEventListener('click', async () => {
                await this.startWandaNavigation();
            });
        }

        // 清除导航按钮
        const clearNavigationBtn = document.getElementById('clear-navigation-btn');
        if (clearNavigationBtn) {
            clearNavigationBtn.addEventListener('click', () => {
                this.clearNavigation();
            });
        }

        // 全屏按钮
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => {
                this.map.toggleFullscreen();
            });
        }

        // 自动居中复选框
        const autoCenterCheckbox = document.getElementById('auto-center');
        if (autoCenterCheckbox) {
            autoCenterCheckbox.addEventListener('change', (e) => {
                this.map.setAutoCenter(e.target.checked);
            });
        }

        // 显示轨迹复选框
        const showTrackCheckbox = document.getElementById('show-track');
        if (showTrackCheckbox) {
            showTrackCheckbox.addEventListener('change', (e) => {
                this.map.setShowTrack(e.target.checked);
            });
        }

        // 更新间隔选择
        const updateIntervalSelect = document.getElementById('update-interval');
        if (updateIntervalSelect) {
            updateIntervalSelect.addEventListener('change', (e) => {
                const newInterval = parseInt(e.target.value);
                this.setUpdateInterval(newInterval);
                log(`更新间隔设置为: ${newInterval/1000}秒`);
            });
        }
    }

    // 检查连接状态
    async checkConnection() {
        try {
            // 真正检查ThingSpeak连接状态
            const isConnected = await thingSpeakAPI.checkConnection();
            this.connectionStatus = isConnected ? 'online' : 'offline';

            if (isConnected) {
                log('ThingSpeak连接正常', 'success');
                Utils.showNotification('已连接到ThingSpeak', 'success');
            } else {
                throw new Error('无法连接到ThingSpeak');
            }

        } catch (error) {
            log(`连接检查失败: ${error.message}`, 'error');
            this.connectionStatus = 'offline';
            Utils.showNotification('连接失败，使用备用数据', 'error');
        }
    }

    // 加载历史轨迹
    async loadHistoryTrack() {
        try {
            let historyData;
            
            if (this.connectionStatus === 'online') {
                historyData = await thingSpeakAPI.getHistoryData(50);
            } else {
                // 使用模拟数据
                historyData = mockDataGenerator.generateHistoryData(20);
            }
            
            if (historyData && historyData.length > 0) {
                this.map.loadHistoryTrack(historyData);
                log(`加载了${historyData.length}个历史轨迹点`, 'success');
            }
            
        } catch (error) {
            log(`加载历史轨迹失败: ${error.message}`, 'error');
        }
    }

    // 更新GPS数据
    async updateGPSData() {
        try {
            let gpsData;

            // 优先尝试从ThingSpeak获取真实数据
            try {
                log('🔄 尝试从ThingSpeak获取实时GPS数据...');
                gpsData = await thingSpeakAPI.getLatestData();
                this.connectionStatus = 'online';
                log('✅ 使用ThingSpeak真实数据', 'success');
            } catch (thingSpeakError) {
                log(`⚠️ ThingSpeak连接失败: ${thingSpeakError.message}`, 'warn');
                log('🔄 切换到固定位置模式...', 'info');
                // 如果ThingSpeak失败，使用固定的衡阳师范学院位置
                gpsData = mockDataGenerator.generateMockData();
                this.connectionStatus = 'offline';
                log('📍 使用固定位置数据', 'info');
            }

            if (gpsData && gpsData.latitude && gpsData.longitude) {
                // 更新地图 (固定位置，不移动)
                this.map.updateCurrentLocation(gpsData);

                // 检查是否有导航数据
                if (gpsData.navigation && gpsData.navigation.type === 'navigation') {
                    log('检测到导航数据，显示路线', 'info');
                    this.map.showNavigationRoute(gpsData.navigation);
                    Utils.showNotification(`开始导航到${gpsData.navigation.destination}`, 'success');
                }

                // 更新UI
                this.updateUI(gpsData);

                this.lastUpdateTime = new Date();
                log(`📍 位置更新: ${gpsData.latitude.toFixed(6)}°N, ${gpsData.longitude.toFixed(6)}°E`, 'success');

            } else {
                throw new Error('GPS数据无效');
            }

        } catch (error) {
            log(`❌ GPS数据更新失败: ${error.message}`, 'error');
            Utils.showNotification('数据更新失败，请检查网络连接', 'error');
        }
    }

    // 测试真实导航功能
    async testRealNavigation() {
        try {
            log('🧪 开始测试真实导航功能...', 'info');

            // 获取当前位置
            const currentPosition = this.map.getCurrentPosition();
            if (!currentPosition) {
                Utils.showNotification('无法获取当前位置，请先刷新数据', 'error');
                return;
            }

            // 目的地 - 酃湖万达广场
            const destination = {
                name: '酃湖万达广场',
                lat: 26.8869,
                lng: 112.6758
            };

            // 创建导航数据
            const navigationData = {
                type: 'navigation',
                destination: destination.name,
                start: {
                    lat: currentPosition.lat,
                    lng: currentPosition.lng
                },
                end: {
                    lat: destination.lat,
                    lng: destination.lng
                }
            };

            log('🗺️ 测试导航数据:', 'info');
            log(navigationData);

            // 显示导航路线
            await this.map.showNavigationRoute(navigationData);

            Utils.showNotification(`开始导航到${destination.name}，使用真实路径规划`, 'success');

        } catch (error) {
            log(`❌ 测试导航失败: ${error.message}`, 'error');
            Utils.showNotification('测试导航失败，请查看控制台', 'error');
        }
    }

    // 更新UI显示
    updateUI(gpsData) {
        // 更新连接状态
        const statusElement = document.getElementById('connection-status');
        if (statusElement) {
            let statusText = '在线';
            let statusClass = 'online';

            if (this.connectionStatus === 'online') {
                // 检查数据新鲜度
                if (gpsData && gpsData.created_at) {
                    const dataTime = new Date(gpsData.created_at);
                    const now = new Date();
                    const timeDiff = (now - dataTime) / 1000 / 60; // 分钟差

                    if (timeDiff <= 5) {
                        statusText = '在线';
                        statusClass = 'online';
                    } else {
                        statusText = `离线 (${Math.floor(timeDiff)}分钟前)`;
                        statusClass = 'offline';
                    }
                } else {
                    statusText = '在线';
                    statusClass = 'online';
                }
            } else {
                statusText = '在线';
                statusClass = 'online';
            }

            statusElement.textContent = statusText;
            statusElement.className = `status ${statusClass}`;
        }

        // 更新最后更新时间 - 使用当前时间而不是数据时间戳
        const lastUpdateElement = document.getElementById('last-update');
        if (lastUpdateElement) {
            // 如果是在线模式，显示数据的实际时间戳
            // 如果是离线模式，显示当前更新时间
            const displayTime = this.connectionStatus === 'online' ?
                gpsData.timestamp : new Date();
            lastUpdateElement.textContent = Utils.formatTime(displayTime);
        }

        // 更新当前位置
        const locationElement = document.getElementById('current-location');
        if (locationElement) {
            locationElement.textContent = `${Utils.formatCoordinate(gpsData.latitude)}, ${Utils.formatCoordinate(gpsData.longitude)}`;
        }

        // 更新详细信息
        const latElement = document.getElementById('latitude');
        const lonElement = document.getElementById('longitude');
        const altElement = document.getElementById('altitude');
        
        if (latElement) latElement.textContent = Utils.formatCoordinate(gpsData.latitude);
        if (lonElement) lonElement.textContent = Utils.formatCoordinate(gpsData.longitude);
        if (altElement) altElement.textContent = gpsData.altitude ? `${gpsData.altitude.toFixed(1)}m` : '--';
    }

    // 开始数据更新
    startUpdating() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        
        // 立即更新一次
        this.updateGPSData();
        
        // 设置定时更新
        this.updateInterval = setInterval(() => {
            this.updateGPSData();
        }, CONFIG.APP.UPDATE_INTERVAL);
        
        log(`开始数据更新，间隔: ${CONFIG.APP.UPDATE_INTERVAL/1000}秒`, 'success');
    }

    // 停止数据更新
    stopUpdating() {
        if (!this.isRunning) return;
        
        this.isRunning = false;
        
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        
        log('数据更新已停止', 'info');
    }

    // 设置更新间隔
    setUpdateInterval(interval) {
        CONFIG.APP.UPDATE_INTERVAL = interval;
        
        if (this.isRunning) {
            this.stopUpdating();
            this.startUpdating();
        }
    }

    // 启动模拟模式
    startMockMode() {
        this.connectionStatus = 'offline';
        log('启动模拟模式', 'info');

        // 加载模拟历史轨迹
        const mockHistory = mockDataGenerator.generateHistoryData(15);
        this.map.loadHistoryTrack(mockHistory);

        // 开始更新
        this.startUpdating();
    }

    // 启动万达广场导航
    async startWandaNavigation() {
        try {
            log('🎯 启动万达广场导航...', 'info');
            Utils.showNotification('正在规划路径...', 'info');

            // 获取当前GPS位置
            const currentPosition = this.map.getCurrentPosition();
            if (!currentPosition) {
                throw new Error('无法获取当前GPS位置');
            }

            // 获取万达广场坐标
            const destination = this.routingService.getDestination('wanda');
            if (!destination) {
                throw new Error('未找到万达广场坐标');
            }

            log(`📍 当前位置: ${currentPosition.lat.toFixed(6)}, ${currentPosition.lng.toFixed(6)}`);
            log(`🎯 目的地: ${destination.name} (${destination.lat.toFixed(6)}, ${destination.lon.toFixed(6)})`);

            // 执行路径规划
            const routeData = await this.routingService.planRoute(
                currentPosition.lat, currentPosition.lng,
                destination.lat, destination.lon
            );

            if (routeData && routeData.success) {
                // 在地图上显示路径
                this.routingService.displayRoute(this.map.leafletMap, routeData);

                // 显示导航信息
                this.showNavigationInfo(routeData, destination.name);

                this.isNavigating = true;

                log(`✅ 万达广场导航启动成功！距离: ${(routeData.distance/1000).toFixed(1)}km`, 'success');
                Utils.showNotification(`导航启动成功！距离: ${(routeData.distance/1000).toFixed(1)}km`, 'success');

                // 上传导航数据到ThingSpeak
                this.uploadNavigationData(currentPosition, destination, 'WANDA');

            } else {
                throw new Error('路径规划失败');
            }

        } catch (error) {
            log(`❌ 万达广场导航启动失败: ${error.message}`, 'error');
            Utils.showNotification(`导航启动失败: ${error.message}`, 'error');
        }
    }

    // 清除导航
    clearNavigation() {
        if (this.routingService) {
            this.routingService.clearRoute(this.map.leafletMap);
        }

        this.isNavigating = false;
        this.hideNavigationInfo();

        log('🧹 导航已清除', 'info');
        Utils.showNotification('导航路线已清除', 'success');
    }

    // 显示导航信息
    showNavigationInfo(routeData, destinationName) {
        const routeInfo = this.routingService.formatRouteInfo(routeData);

        // 更新状态栏信息
        const statusElement = document.getElementById('current-location');
        if (statusElement) {
            statusElement.textContent = `导航至${destinationName}`;
        }

        // 显示详细信息（如果有信息面板的话）
        log(`📊 导航信息: ${routeInfo.distance}, ${routeInfo.duration}, 使用${routeInfo.provider}`, 'info');

        // 可以在这里添加更多UI更新逻辑
    }

    // 隐藏导航信息
    hideNavigationInfo() {
        const statusElement = document.getElementById('current-location');
        if (statusElement) {
            statusElement.textContent = '--';
        }
    }

    // 上传导航数据到ThingSpeak
    async uploadNavigationData(currentPos, destination, type) {
        try {
            log('📤 上传导航数据到ThingSpeak...', 'info');

            const navigationData = `${type}_${currentPos.lng}_${currentPos.lat}_${destination.lon}_${destination.lat}`;

            // 这里可以调用ThingSpeak API上传数据
            // 具体实现取决于您的ThingSpeak配置

            log('✅ 导航数据上传成功', 'success');

        } catch (error) {
            log(`❌ 导航数据上传失败: ${error.message}`, 'error');
        }
    }
}

// 应用启动
document.addEventListener('DOMContentLoaded', () => {
    window.gpsApp = new GPSTrackerApp();
});

// 导出应用类
window.GPSTrackerApp = GPSTrackerApp;
