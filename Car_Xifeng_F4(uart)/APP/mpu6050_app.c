#include "mpu6050_app.h"

#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

/* 全局MPU6050应用实例 */
MPU6050_App_t mpu6050_app;

/* 私有函数声明 */
static void MPU6050_App_InitKalmanFilter(KalmanFilter_t *kalman);

/**
 * @brief 初始化MPU6050应用层
 * @return 0:成功, 1:失败
 */
uint8_t MPU6050_App_Init(void)
{
    // 清零应用结构体
    memset(&mpu6050_app, 0, sizeof(MPU6050_App_t));
    
    // 初始化MPU6050设备
    if (MPU6050_Init(&mpu6050_app.mpu_device, &hi2c2) != 0) {
        return 1;
    }
    
    // 初始化卡尔曼滤波器
    MPU6050_App_InitKalmanFilter(&mpu6050_app.kalman_roll);
    MPU6050_App_InitKalmanFilter(&mpu6050_app.kalman_pitch);
    
    return 0;
}

/**
 * @brief 设置MPU6050配置
 * @param gyro_range 陀螺仪量程
 * @param accel_range 加速度计量程
 * @return 0:成功, 1:失败
 */
uint8_t MPU6050_App_SetConfig(MPU6050_GyroRange_t gyro_range, MPU6050_AccelRange_t accel_range)
{
    MPU6050_Config_t config = mpu6050_app.mpu_device.config;
    config.gyro_range = gyro_range;
    config.accel_range = accel_range;
    
    return MPU6050_Configure(&mpu6050_app.mpu_device, &config);
}

/**
 * @brief 读取MPU6050数据
 * @return 0:成功, 1:失败
 */
uint8_t MPU6050_App_ReadData(void)
{
    // 读取原始数据
    if (MPU6050_ReadRawData(&mpu6050_app.mpu_device) != 0) {
        mpu6050_app.error_count++;
        mpu6050_app.data_ready = 0;
        return 1;
    }
    
    // 处理数据
    MPU6050_ProcessData(&mpu6050_app.mpu_device);
    
    // 应用校准
    if (mpu6050_app.is_calibrated) {
        MPU6050_App_ApplyCalibration();
    }
    
    // 计算姿态角
    MPU6050_App_CalculateAttitude();
    
    mpu6050_app.read_count++;
    mpu6050_app.data_ready = 1;
    
    return 0;
}

/**
 * @brief MPU6050应用层任务 (应在调度器中周期性调用)
 */
void MPU6050_App_Task(void)
{
    // 读取数据
    MPU6050_App_ReadData();
    MPU6050_App_PrintData();
	MPU6050_App_PrintAttitude();
    // 可以在这里添加数据处理逻辑
    // 例如：数据滤波、异常检测、数据记录等
    
    // 示例：打印数据 (可根据需要启用)
    // MPU6050_App_PrintData();
}

/**
 * @brief 校准MPU6050 (静止状态下调用)
 * @param samples 校准样本数
 * @return 0:成功, 1:失败
 */
uint8_t MPU6050_App_Calibrate(uint16_t samples)
{
    if (samples == 0) {
        samples = 1000;  // 默认1000个样本
    }
    
    float accel_sum_x = 0, accel_sum_y = 0, accel_sum_z = 0;
    float gyro_sum_x = 0, gyro_sum_y = 0, gyro_sum_z = 0;
    uint16_t valid_samples = 0;
    
    // 收集样本
    for (uint16_t i = 0; i < samples; i++) {
        if (MPU6050_ReadRawData(&mpu6050_app.mpu_device) == 0) {
            MPU6050_ProcessData(&mpu6050_app.mpu_device);
            
            accel_sum_x += mpu6050_app.mpu_device.data.accel_x;
            accel_sum_y += mpu6050_app.mpu_device.data.accel_y;
            accel_sum_z += mpu6050_app.mpu_device.data.accel_z;
            
            gyro_sum_x += mpu6050_app.mpu_device.data.gyro_x;
            gyro_sum_y += mpu6050_app.mpu_device.data.gyro_y;
            gyro_sum_z += mpu6050_app.mpu_device.data.gyro_z;
            
            valid_samples++;
        }
        HAL_Delay(2);  // 2ms间隔
    }
    
    if (valid_samples < samples / 2) {
        return 1;  // 有效样本太少
    }
    
    // 计算偏移量
    mpu6050_app.accel_offset_x = accel_sum_x / valid_samples;
    mpu6050_app.accel_offset_y = accel_sum_y / valid_samples;
    mpu6050_app.accel_offset_z = accel_sum_z / valid_samples - 1.0f;  // Z轴应该是1g
    
    mpu6050_app.gyro_offset_x = gyro_sum_x / valid_samples;
    mpu6050_app.gyro_offset_y = gyro_sum_y / valid_samples;
    mpu6050_app.gyro_offset_z = gyro_sum_z / valid_samples;
    
    mpu6050_app.is_calibrated = 1;

    return 0;
}

/**
 * @brief 应用校准参数
 */
void MPU6050_App_ApplyCalibration(void)
{
    if (!mpu6050_app.is_calibrated) {
        return;
    }

    mpu6050_app.mpu_device.data.accel_x -= mpu6050_app.accel_offset_x;
    mpu6050_app.mpu_device.data.accel_y -= mpu6050_app.accel_offset_y;
    mpu6050_app.mpu_device.data.accel_z -= mpu6050_app.accel_offset_z;

    mpu6050_app.mpu_device.data.gyro_x -= mpu6050_app.gyro_offset_x;
    mpu6050_app.mpu_device.data.gyro_y -= mpu6050_app.gyro_offset_y;
    mpu6050_app.mpu_device.data.gyro_z -= mpu6050_app.gyro_offset_z;
}

/**
 * @brief 计算姿态角
 */
void MPU6050_App_CalculateAttitude(void)
{
    static uint32_t last_time = 0;
    uint32_t current_time = HAL_GetTick();
    float dt = (current_time - last_time) / 1000.0f;  // 转换为秒

    if (last_time == 0) {
        last_time = current_time;
        return;
    }

    // 从加速度计计算角度
    float accel_angle_x = atan2f(mpu6050_app.mpu_device.data.accel_y,
                                 sqrtf(mpu6050_app.mpu_device.data.accel_x * mpu6050_app.mpu_device.data.accel_x +
                                       mpu6050_app.mpu_device.data.accel_z * mpu6050_app.mpu_device.data.accel_z)) * 180.0f / M_PI;

    float accel_angle_y = atan2f(-mpu6050_app.mpu_device.data.accel_x,
                                 sqrtf(mpu6050_app.mpu_device.data.accel_y * mpu6050_app.mpu_device.data.accel_y +
                                       mpu6050_app.mpu_device.data.accel_z * mpu6050_app.mpu_device.data.accel_z)) * 180.0f / M_PI;

    // 使用卡尔曼滤波器融合数据
    mpu6050_app.attitude.roll = MPU6050_App_KalmanFilter(&mpu6050_app.kalman_roll,
                                                         accel_angle_x,
                                                         mpu6050_app.mpu_device.data.gyro_x,
                                                         dt);

    mpu6050_app.attitude.pitch = MPU6050_App_KalmanFilter(&mpu6050_app.kalman_pitch,
                                                          accel_angle_y,
                                                          mpu6050_app.mpu_device.data.gyro_y,
                                                          dt);

    // 简单的偏航角积分 (需要磁力计才能获得绝对偏航角)
    mpu6050_app.attitude.yaw += mpu6050_app.mpu_device.data.gyro_z * dt;

    last_time = current_time;
}

/**
 * @brief 卡尔曼滤波器
 * @param kalman 卡尔曼滤波器结构体指针
 * @param new_angle 新的角度测量值
 * @param new_rate 新的角速度测量值
 * @param dt 时间间隔
 * @return 滤波后的角度
 */
float MPU6050_App_KalmanFilter(KalmanFilter_t *kalman, float new_angle, float new_rate, float dt)
{
    // 预测步骤
    kalman->rate = new_rate - kalman->bias;
    kalman->angle += dt * kalman->rate;

    // 更新误差协方差矩阵
    kalman->P[0][0] += dt * (dt * kalman->P[1][1] - kalman->P[0][1] - kalman->P[1][0] + kalman->Q_angle);
    kalman->P[0][1] -= dt * kalman->P[1][1];
    kalman->P[1][0] -= dt * kalman->P[1][1];
    kalman->P[1][1] += kalman->Q_bias * dt;

    // 计算卡尔曼增益
    float S = kalman->P[0][0] + kalman->R_measure;
    float K[2];
    K[0] = kalman->P[0][0] / S;
    K[1] = kalman->P[1][0] / S;

    // 更新估计
    float y = new_angle - kalman->angle;
    kalman->angle += K[0] * y;
    kalman->bias += K[1] * y;

    // 更新误差协方差矩阵
    float P00_temp = kalman->P[0][0];
    float P01_temp = kalman->P[0][1];

    kalman->P[0][0] -= K[0] * P00_temp;
    kalman->P[0][1] -= K[0] * P01_temp;
    kalman->P[1][0] -= K[1] * P00_temp;
    kalman->P[1][1] -= K[1] * P01_temp;

    return kalman->angle;
}

/* 数据获取接口 */
MPU6050_Data_t* MPU6050_App_GetData(void) {
    return &mpu6050_app.mpu_device.data;
}

MPU6050_RawData_t* MPU6050_App_GetRawData(void) {
    return &mpu6050_app.mpu_device.raw_data;
}

MPU6050_Attitude_t* MPU6050_App_GetAttitude(void) {
    return &mpu6050_app.attitude;
}

/* 状态查询 */
uint8_t MPU6050_App_IsReady(void) {
    return mpu6050_app.data_ready;
}

uint8_t MPU6050_App_IsCalibrated(void) {
    return mpu6050_app.is_calibrated;
}

uint8_t MPU6050_App_IsConnected(void) {
    return mpu6050_app.mpu_device.is_connected;
}

/**
 * @brief 打印MPU6050数据
 */
void MPU6050_App_PrintData(void)
{
    if (!mpu6050_app.data_ready) {
        return;
    }

    my_printf(&huart1, "MPU6050 Data:\r\n");
    my_printf(&huart1, "Accel: X=%.3f Y=%.3f Z=%.3f (g)\r\n",
              mpu6050_app.mpu_device.data.accel_x,
              mpu6050_app.mpu_device.data.accel_y,
              mpu6050_app.mpu_device.data.accel_z);
    my_printf(&huart1, "Gyro:  X=%.3f Y=%.3f Z=%.3f (°/s)\r\n",
              mpu6050_app.mpu_device.data.gyro_x,
              mpu6050_app.mpu_device.data.gyro_y,
              mpu6050_app.mpu_device.data.gyro_z);
    my_printf(&huart1, "Temp:  %.2f °C\r\n", mpu6050_app.mpu_device.data.temperature);
}

/**
 * @brief 打印姿态角数据
 */
void MPU6050_App_PrintAttitude(void)
{
    if (!mpu6050_app.data_ready) {
        return;
    }

    my_printf(&huart1, "Attitude: Roll=%.2f Pitch=%.2f Yaw=%.2f (°)\r\n",
              mpu6050_app.attitude.roll,
              mpu6050_app.attitude.pitch,
              mpu6050_app.attitude.yaw);
}

/**
 * @brief 重置MPU6050应用
 */
void MPU6050_App_Reset(void)
{
    mpu6050_app.attitude.roll = 0;
    mpu6050_app.attitude.pitch = 0;
    mpu6050_app.attitude.yaw = 0;

    mpu6050_app.read_count = 0;
    mpu6050_app.error_count = 0;
    mpu6050_app.data_ready = 0;

    // 重新初始化卡尔曼滤波器
    MPU6050_App_InitKalmanFilter(&mpu6050_app.kalman_roll);
    MPU6050_App_InitKalmanFilter(&mpu6050_app.kalman_pitch);
}

/**
 * @brief 初始化卡尔曼滤波器
 * @param kalman 卡尔曼滤波器结构体指针
 */
static void MPU6050_App_InitKalmanFilter(KalmanFilter_t *kalman)
{
    kalman->Q_angle = 0.001f;
    kalman->Q_bias = 0.003f;
    kalman->R_measure = 0.03f;

    kalman->angle = 0.0f;
    kalman->bias = 0.0f;
    kalman->rate = 0.0f;

    kalman->P[0][0] = 0.0f;
    kalman->P[0][1] = 0.0f;
    kalman->P[1][0] = 0.0f;
    kalman->P[1][1] = 0.0f;
}
