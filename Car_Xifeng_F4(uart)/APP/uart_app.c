#include "uart_app.h"
#include "navigation_app.h"
#include "esp01_app.h"

#define GPS_UPLOAD_INTERVAL 10000  // GPS上传间隔（毫秒）

extern uint8_t uart_rx_dma_buffer[BUFFER_SIZE];
extern uint8_t ring_buffer_input[BUFFER_SIZE];
extern struct rt_ringbuffer ring_buffer;
extern uint8_t uart_data_buffer[BUFFER_SIZE];

void Uart_Init(void)
{
  // System startup info (English)
  my_printf(&huart1, "\r\n=== Navigation System Started ===\r\n");

  // Initialize ring buffer
  rt_ringbuffer_init(&ring_buffer, ring_buffer_input, BUFFER_SIZE);

  // Start DMA reception
  HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, BUFFER_SIZE);
  __HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);

  my_printf(&huart1, "System ready. Enter command: ");
}


void Uart_Task(void)
{
  static uint32_t last_heartbeat = 0;
  static uint32_t heartbeat_count = 0;
  uint32_t current_time = HAL_GetTick();

  // 每5秒发送一次心跳，证明系统在运行
  if (current_time - last_heartbeat > 5000) {
    last_heartbeat = current_time;
    heartbeat_count++;
    HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_2);  // 切换LED状态

    // Send heartbeat every 60 seconds
    if (heartbeat_count % 12 == 1) {
      my_printf(&huart1, "\r\nSystem running normally\r\n");
    }
  }

  uint16_t uart_data_len = rt_ringbuffer_data_len(&ring_buffer);

  if(uart_data_len > 0)
  {
    // 调试：显示接收到的数据长度
    HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_2);  // LED闪烁表示处理命令

    // 获取数据
    rt_ringbuffer_get(&ring_buffer, uart_data_buffer, uart_data_len);
    uart_data_buffer[uart_data_len] = '\0';

    // 清理输入数据，移除换行符
    char clean_command[64];
    strncpy(clean_command, (char*)uart_data_buffer, sizeof(clean_command) - 1);
    clean_command[sizeof(clean_command) - 1] = '\0';

    // 移除所有可能的换行符和回车符
    char* newline = strchr(clean_command, '\r');
    if (newline) *newline = '\0';
    newline = strchr(clean_command, '\n');
    if (newline) *newline = '\0';

    // 忽略空命令
    if (strlen(clean_command) == 0) {
      return;
    }

    my_printf(&huart1, "\r\n> %s\r\n", clean_command);

    // 只处理nav_test1到nav_test5导航命令
    if (strcmp(clean_command, "nav_test1") == 0)
    {
        my_printf(&huart1, "Navigation Test 1: Wanda Plaza\r\n");
        Navigation_StartNavigation("wanda");
        return;
    }
    else if (strcmp(clean_command, "nav_test2") == 0)
    {
        my_printf(&huart1, "Navigation Test 2: Linghu Academy\r\n");
        Navigation_StartNavigation("shuyuan");
        return;
    }
    else if (strcmp(clean_command, "nav_test3") == 0)
    {
        my_printf(&huart1, "Navigation Test 3: Sports Center\r\n");
        Navigation_StartNavigation("tiyuzhonxin");
        return;
    }
    else if (strcmp(clean_command, "nav_test4") == 0)
    {
        my_printf(&huart1, "Navigation Test 4: Train Station\r\n");
        Navigation_StartNavigation("huochezhan");
        return;
    }
    else if (strcmp(clean_command, "nav_test5") == 0)
    {
        my_printf(&huart1, "Navigation Test 5: Hospital\r\n");
        Navigation_StartNavigation("yiyuan");
        return;
    }
    else if (strcmp(clean_command, "nav_stop") == 0)
    {
        my_printf(&huart1, "Navigation stopped\r\n");
        Navigation_StopNavigation();
        return;
    }
    else if (strcmp(clean_command, "help") == 0)
    {
        my_printf(&huart1, "\r\nAvailable Commands:\r\n");
        my_printf(&huart1, "  nav_test1 - Navigate to Wanda Plaza\r\n");
        my_printf(&huart1, "  nav_test2 - Navigate to Linghu Academy\r\n");
        my_printf(&huart1, "  nav_test3 - Navigate to Sports Center\r\n");
        my_printf(&huart1, "  nav_test4 - Navigate to Train Station\r\n");
        my_printf(&huart1, "  nav_test5 - Navigate to Hospital\r\n");
        my_printf(&huart1, "  nav_stop  - Stop navigation\r\n");
        my_printf(&huart1, "  help      - Show this help\r\n");
    }
    else
    {
        my_printf(&huart1, "Unknown command: %s\r\n", clean_command);
        my_printf(&huart1, "Type 'help' for available commands\r\n");
    }

    // 清空缓冲区
    memset(uart_data_buffer, 0, uart_data_len);

    // 强制清空环形缓冲区，确保没有残留数据
    rt_ringbuffer_reset(&ring_buffer);
  }
}


