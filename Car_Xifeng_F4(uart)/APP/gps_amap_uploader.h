/**
 * @file gps_amap_uploader.h
 * @brief GPS数据上传到高德地图模块头文件
 */

#ifndef __GPS_AMAP_UPLOADER_H__
#define __GPS_AMAP_UPLOADER_H__

#include "MyDefine.h"

/* GPS数据格式枚举 */
typedef enum {
    GPS_FORMAT_CUSTOM = 0,      // GPS_MAP:纬度,经度,高度
    GPS_FORMAT_LAT_LON,         // LAT:纬度,LON:经度,ALT:高度
    GPS_FORMAT_JSON,            // JSON格式
    GPS_FORMAT_SIMPLE           // 简单格式：纬度,经度,高度
} GPS_DataFormat_t;

/* GPS高德地图配置结构体 */
typedef struct {
    uint32_t upload_interval_ms;    // 上传间隔（毫秒）
    uint8_t enable_thingspeak;      // 是否启用ThingSpeak上传
    uint8_t enable_serial_output;   // 是否启用串口输出
    GPS_DataFormat_t data_format;   // 数据格式
    float default_lat;              // 默认纬度
    float default_lon;              // 默认经度
    float default_alt;              // 默认高度
} GPS_AMap_Config_t;

/* 函数声明 */
void GPS_AMap_Init(void);
void GPS_AMap_SetConfig(uint32_t interval_ms, uint8_t enable_thingspeak, GPS_DataFormat_t format);
void GPS_AMap_Task(void);
void GPS_AMap_ManualUpload(void);
void GPS_AMap_SetCustomLocation(float lat, float lon, float alt);
void GPS_AMap_GetStats(uint32_t* upload_count, uint32_t* last_upload_time);
void GPS_AMap_PrintStatus(void);
void GPS_AMap_ProcessCommand(const char* command);

#endif /* __GPS_AMAP_UPLOADER_H__ */
