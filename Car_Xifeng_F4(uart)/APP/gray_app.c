#include "gray_app.h"

unsigned char Digtal; // ������

float g_line_position_error; // ѭ�����ֵ

void Gray_Init(void)
{

}

void Gray_Task(void)
{
    //��ȡ���������������
    Digtal=~IIC_Get_Digtal();
    my_printf(&huart1, "Digtal %d-%d-%d-%d-%d-%d-%d-%d\r\n",(Digtal>>0)&0x01,(Digtal>>1)&0x01,(Digtal>>2)&0x01,(Digtal>>3)&0x01,(Digtal>>4)&0x01,(Digtal>>5)&0x01,(Digtal>>6)&0x01,(Digtal>>7)&0x01);
    IIC_Anolog_Normalize(0x00); //Ϊ����һ��ѭ���Ƿǹ�һ������������
}



