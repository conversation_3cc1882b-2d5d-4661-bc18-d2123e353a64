# LCD白屏问题诊断指南

## 问题描述
LCD显示白屏，从STM32F407ZG例程移植到STM32F407VET6时出现的问题。

## 根本原因分析

### 1. 芯片差异
- **STM32F407ZG**: 144引脚，有GPIOG端口
- **STM32F407VET6**: 100引脚，**没有GPIOG端口**

### 2. 原始例程引脚配置
```c
// STM32F407ZG原始配置
#define LCD_SCLK_Clr() GPIO_ResetBits(GPIOG,GPIO_Pin_12)  // SCL=PG12
#define LCD_MOSI_Clr() GPIO_ResetBits(GPIOD,GPIO_Pin_5)   // SDA=PD5
#define LCD_RES_Clr()  GPIO_ResetBits(GPIOD,GPIO_Pin_4)   // RES=PD4
#define LCD_DC_Clr()   GPIO_ResetBits(GPIOD,GPIO_Pin_15)  // DC=PD15
#define LCD_BLK_Clr()  GPIO_ResetBits(GPIOE,GPIO_Pin_8)   // BLK=PE8
```

### 3. 当前移植版本配置
```c
// STM32F407VET6移植配置
#define LCD_SCLK_Pin    GPIO_PIN_13  // PB13 (SPI2_SCK)
#define LCD_MOSI_Pin    GPIO_PIN_15  // PB15 (SPI2_MOSI)
#define LCD_RES_Pin     GPIO_PIN_4   // PD4
#define LCD_DC_Pin      GPIO_PIN_0   // PD0 (改了！原来是PD15)
#define LCD_BLK_Pin     GPIO_PIN_1   // PD1 (改了！原来是PE8)
```

## 可能的问题

### 问题1：硬件连接与软件配置不匹配
如果你的LCD硬件连接还是按照原始例程的方式连接：
- SCL连接到PG12位置（但VET6没有PG12）
- SDA连接到PD5
- DC连接到PD15
- BLK连接到PE8

但软件配置改成了新的引脚，就会导致通信失败。

### 问题2：引脚冲突
- PD15可能与AF_KEY冲突
- PE8可能被其他功能占用

## 解决方案

### 方案1：确认硬件连接（推荐）
1. 检查你的LCD模块实际连接到哪些引脚
2. 根据实际连接修改`lcd_init_hal.h`中的引脚定义
3. 在`lcd_init_hal.h`中启用`USE_ORIGINAL_PINS`宏如果使用原始连接

### 方案2：重新连接硬件
按照新的引脚配置重新连接LCD：
- SCL → PB13
- SDA → PB15  
- RES → PD4
- DC → PD0
- BLK → PD1

## 测试步骤

### 第一步：运行快速测试
编译并运行程序，观察串口输出和LCD变化：

```c
// 在tft_Init()中会自动调用
LCD_QuickPinTest();  // 测试背光和基本引脚
LCD_SimpleSPITest(); // 测试SPI通信
```

### 第二步：观察现象
1. **背光测试**：观察LCD背光是否有闪烁
   - 有闪烁 → 背光引脚连接正确
   - 无变化 → 背光引脚连接错误

2. **复位测试**：观察LCD是否有任何变化
   - 有变化 → 复位引脚连接正确
   - 无变化 → 复位引脚连接错误

### 第三步：调整配置
根据测试结果修改`lcd_init_hal.h`：

如果使用原始连接，取消注释：
```c
#define USE_ORIGINAL_PINS 1
```

如果使用新连接，保持注释状态。

## 常见问题排查

### 1. 完全白屏，背光正常
- 可能是SPI通信引脚错误
- 检查SCL和SDA引脚连接

### 2. 背光无法控制
- 检查BLK引脚连接
- 可能连接到PE8而不是PD1

### 3. 有背光但无显示
- 可能是DC引脚错误
- 检查DC引脚是否连接到PD15或PD0

### 4. 显示颜色异常
- 可能是像素格式问题
- 检查RGB565/RGB666设置

## 调试建议

1. **使用串口监控**：观察调试信息确定执行到哪一步
2. **逐步测试**：先测试背光，再测试复位，最后测试SPI通信
3. **对比原始例程**：确认硬件连接是否与原始例程一致
4. **检查电源**：确认LCD模块供电正常（3.3V或5V）

## 下一步行动

1. 运行当前程序，观察串口输出和LCD变化
2. 根据观察结果确定正确的引脚配置
3. 修改`lcd_init_hal.h`中的引脚定义
4. 重新编译测试
