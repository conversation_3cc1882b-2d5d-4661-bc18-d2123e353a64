<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 单片机导航命令测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .test-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
        }
        .command-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .command-card {
            background: rgba(255,255,255,0.15);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #00ff88;
        }
        .command-code {
            font-family: 'Courier New', monospace;
            background: rgba(0,0,0,0.3);
            padding: 8px;
            border-radius: 5px;
            font-weight: bold;
            color: #00ff88;
        }
        .destination {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .field3-value {
            color: #ffaa00;
            font-weight: bold;
        }
        .instructions {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
        }
        .step {
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
        }
        .step::before {
            content: "▶";
            position: absolute;
            left: 0;
            color: #00ff88;
        }
        .code-changes {
            background: rgba(0,0,0,0.3);
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin-top: 15px;
        }
        .highlight {
            background: rgba(255,255,0,0.3);
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 单片机导航命令测试指南</h1>
        
        <div class="instructions">
            <h3>📋 测试步骤</h3>
            <div class="step">编译并烧录修改后的单片机代码</div>
            <div class="step">打开串口调试助手，连接单片机</div>
            <div class="step">在串口中输入测试命令</div>
            <div class="step">观察ThingSpeak数据和网页检测器响应</div>
        </div>

        <div class="test-section">
            <h3>🎯 支持的导航命令</h3>
            <p>现在单片机支持以下导航命令，每个命令会发送不同的field3值：</p>
            
            <div class="command-grid">
                <div class="command-card">
                    <div class="destination">🛍️ 万达广场(衡阳酃湖店)</div>
                    <div class="command-code">nav_test1</div>
                    <div>发送 <span class="field3-value">field3=1111</span></div>
                </div>
                
                <div class="command-card">
                    <div class="destination">📚 酃湖书院</div>
                    <div class="command-code">nav_test2</div>
                    <div>发送 <span class="field3-value">field3=2222</span></div>
                </div>
                
                <div class="command-card">
                    <div class="destination">🏟️ 衡阳市体育中心</div>
                    <div class="command-code">nav_test3</div>
                    <div>发送 <span class="field3-value">field3=3333</span></div>
                </div>
                
                <div class="command-card">
                    <div class="destination">🚄 衡阳火车站</div>
                    <div class="command-code">nav_test4</div>
                    <div>发送 <span class="field3-value">field3=4444</span></div>
                </div>
                
                <div class="command-card">
                    <div class="destination">🏥 南华大学附属第一医院</div>
                    <div class="command-code">nav_test5</div>
                    <div>发送 <span class="field3-value">field3=5555</span></div>
                </div>
                
                <div class="command-card">
                    <div class="destination">🛍️ 万达广场(兼容模式)</div>
                    <div class="command-code">wangda 或 wanda</div>
                    <div>发送 <span class="field3-value">field3=9999</span></div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 代码修改说明</h3>
            <p>已对以下文件进行了修改：</p>
            
            <div class="code-changes">
                <strong>📁 MDK-ARM/esp01_app.c</strong><br>
                ✅ 修改 <span class="highlight">esp01_SendNavigationData()</span> 函数<br>
                ✅ 添加 <span class="highlight">esp01_GetDestinationCode()</span> 函数<br>
                ✅ 添加 <span class="highlight">esp01_GetDestinationName()</span> 函数<br><br>
                
                <strong>📁 MDK-ARM/esp01_app.h</strong><br>
                ✅ 添加新函数声明<br><br>
                
                <strong>📁 APP/navigation_app.c</strong><br>
                ✅ 添加测试命令处理 (nav_test1-5)<br>
                ✅ 更新目的地坐标<br>
                ✅ 修改路径数据格式
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 测试流程</h3>
            <div class="step">在串口调试助手中输入：<code>nav_test1</code></div>
            <div class="step">单片机会启动到万达广场的导航</div>
            <div class="step">ESP01会发送包含field3=1111的GPS数据到ThingSpeak</div>
            <div class="step">网页检测器会识别field3=1111并显示万达广场导航</div>
            <div class="step">重复测试其他命令 (nav_test2 到 nav_test5)</div>
        </div>

        <div class="test-section">
            <h3>📊 验证方法</h3>
            <div class="step">打开 <strong>WANDA命令检测器.html</strong></div>
            <div class="step">点击"开始检测"</div>
            <div class="step">在单片机串口输入测试命令</div>
            <div class="step">观察网页是否正确识别不同的目的地</div>
            <div class="step">检查导航指令是否显示正确的目的地信息</div>
        </div>

        <div class="instructions">
            <h3>⚠️ 注意事项</h3>
            <div class="step">确保ESP01模块已连接WiFi</div>
            <div class="step">确保ThingSpeak API密钥正确</div>
            <div class="step">GPS数据需要有效才能触发导航</div>
            <div class="step">每次测试间隔至少10秒（ThingSpeak限制）</div>
        </div>
    </div>
</body>
</html>
