<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <title>STM32F407VET6 GPS追踪导航系统</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <style type="text/css">
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .header {
            background-color: #2E8B57;
            color: white;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .info-panel {
            background: white;
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .status-item {
            text-align: center;
            flex: 1;
        }
        
        .status-value {
            font-size: 18px;
            font-weight: bold;
            color: #2E8B57;
        }
        
        .status-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        #map {
            width: calc(100% - 20px);
            height: 500px;
            margin: 10px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .controls {
            background: white;
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .btn {
            background-color: #2E8B57;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover {
            background-color: #228B22;
        }
        
        .btn-secondary {
            background-color: #6c757d;
        }
        
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        
        .btn-danger {
            background-color: #dc3545;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .navigation-info {
            background: #e8f5e8;
            border: 1px solid #2E8B57;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            display: none;
        }
        
        .navigation-info h3 {
            margin: 0 0 10px 0;
            color: #2E8B57;
        }
        
        .route-instructions {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            display: none;
        }
        
        .route-instructions h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .instruction-item {
            padding: 8px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        .instruction-item:last-child {
            border-bottom: none;
        }
        
        .instruction-icon {
            width: 24px;
            height: 24px;
            margin-right: 10px;
            background-color: #2E8B57;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        
        .log-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #17a2b8; }
        .log-warning { color: #ffc107; }

        /* 当前导航步骤高亮 */
        .instruction-item.current-step {
            background: #FF4444 !important;
            color: white !important;
            border-left: 5px solid #FF0000;
            animation: pulse 1s infinite;
        }

        .instruction-item.current-step .instruction-icon {
            background: white !important;
            color: #FF4444 !important;
        }

        /* 导航提示动画 */
        @keyframes slideDown {
            from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
            to { transform: translateX(-50%) translateY(0); opacity: 1; }
        }

        @keyframes slideUp {
            from { transform: translateX(-50%) translateY(0); opacity: 1; }
            to { transform: translateX(-50%) translateY(-100%); opacity: 0; }
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 68, 68, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 68, 68, 0); }
        }

        /* 目的地选择面板样式 */
        .destination-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            padding: 20px;
            z-index: 1000;
            max-width: 400px;
            width: 90%;
        }

        .destination-options {
            display: grid;
            gap: 10px;
            margin: 15px 0;
        }

        .destination-option {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f9f9f9;
        }

        .destination-option:hover {
            border-color: #2E8B57;
            background: #e8f5e8;
            transform: translateY(-2px);
        }

        .destination-icon {
            font-size: 24px;
            margin-right: 15px;
        }

        .destination-info {
            flex: 1;
        }

        .destination-name {
            font-weight: bold;
            font-size: 16px;
            color: #333;
            margin-bottom: 5px;
        }

        .destination-description {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🗺️ 湖南工学院 GPS智能导航系统</h1>
        <p>校园智能导航 | OpenStreetMap路径规划 | 实时位置追踪</p>
    </div>

    <div class="info-panel">
        <div class="status">
            <div class="status-item">
                <div class="status-value" id="currentLat">--</div>
                <div class="status-label">纬度</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="currentLon">--</div>
                <div class="status-label">经度</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="currentAlt">--</div>
                <div class="status-label">海拔(m)</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="updateTime">--</div>
                <div class="status-label">更新时间</div>
            </div>
        </div>
    </div>

    <div id="map"></div>

    <div class="navigation-info" id="navigationInfo">
        <h3>🧭 导航信息</h3>
        <p><strong>距离:</strong> <span id="routeDistance">--</span></p>
        <p><strong>预计时间:</strong> <span id="routeTime">--</span></p>
        <p><strong>路径规划:</strong> <span id="routeProvider">--</span></p>
    </div>

    <div class="route-instructions" id="routeInstructions">
        <h3>📋 导航指令</h3>
        <div id="instructionsList"></div>
    </div>

    <div class="controls">
        <button class="btn" onclick="refreshGPSData()">🔄 刷新GPS数据</button>
        <button class="btn" onclick="centerMap()">📍 回到中心</button>
        <button class="btn" onclick="showDestinationOptions()">🎯 自定义路线</button>
        <button class="btn" onclick="planCustomRoute()">🗺️ 手动路径</button>
        <button class="btn btn-secondary" onclick="clearRoute()">🧹 清除路径</button>
        <button class="btn btn-secondary" onclick="toggleInstructions()">📋 显示/隐藏指令</button>
        <button class="btn btn-secondary" onclick="toggleDebugMode()">🔧 调试模式</button>
        <button class="btn btn-secondary" onclick="testSerialConnection()">📡 测试串口</button>
    </div>

    <!-- 目的地选择面板 -->
    <div id="destinationPanel" class="destination-panel" style="display: none;">
        <h3>🎯 选择目的地</h3>
        <div id="destinationOptions" class="destination-options">
            <!-- 动态生成目的地选项 -->
        </div>
        <button onclick="hideDestinationOptions()" class="btn btn-secondary">❌ 取消</button>
    </div>

    <div class="log-panel" id="logPanel">
        <div class="log-entry log-info">🚀 系统初始化中...</div>
    </div>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <script>
        // 全局变量
        let map;
        let currentMarker;
        let routeLayer;
        let isNavigating = false;
        let currentDestination = null;
        let lastRouteUpdate = 0;
        const ROUTE_UPDATE_INTERVAL = 30000; // 30秒更新一次路线
        let currentRouteSteps = [];
        let currentStepIndex = 0;
        let lastStepCheckTime = 0;

        // 调试模式开关
        window.DEBUG_MODE = false;

        // 串口相关变量
        let serialPort = null;
        let reader = null;
        let isSerialConnected = false;

        // ThingSpeak配置 - 只读取，不上传
        const THINGSPEAK_CHANNEL = '3014831';
        const THINGSPEAK_READ_KEY = 'LU22ZUP42TFK4IY9';  // 读取API密钥
        const THINGSPEAK_WRITE_KEY = 'V64RR7CZJ9Z4O7ED'; // 写入API密钥（用于测试）
        
        // 湖南工学院默认坐标（基于OpenStreetMap数据）
        const DEFAULT_LAT = 26.8848140;
        const DEFAULT_LON = 112.6796025;

        // 预设目的地选项（基于OpenStreetMap官方数据）
        const DESTINATION_OPTIONS = [
            {
                name: '万达广场(衡阳酃湖店)',
                lat: 26.8892785,
                lon: 112.6609182,
                description: '万达广场（衡阳酃湖店）购物中心 - 最近的万达',
                icon: '🛍️'
            },
            {
                name: '酃湖书院',
                lat: 26.8850,
                lon: 112.6700,
                description: '酃湖书院文化景点（位置待确认）',
                icon: '📚'
            },
            {
                name: '衡阳市体育中心',
                lat: 26.8900796,
                lon: 112.6741752,
                description: '衡阳市体育中心体育场馆（酃湖大学城）',
                icon: '🏟️'
            },
            {
                name: '衡阳火车站',
                lat: 26.8934986,
                lon: 112.6260051,
                description: '衡阳火车站交通枢纽',
                icon: '🚄'
            },
            {
                name: '南华大学附属第一医院',
                lat: 26.9043654,
                lon: 112.5962734,
                description: '南华大学附属第一医院医疗中心',
                icon: '🏥'
            }
        ];

        // 湖南工学院校园出入口坐标（基于OpenStreetMap边界数据）
        const CAMPUS_EXITS = [
            {
                name: '东门(主门)',
                lat: 26.8848,
                lon: 112.6834,
                type: 'main',
                description: '学校主要出入口，连接衡州大道，车辆和行人主要通道'
            },
            {
                name: '南门',
                lat: 26.8796,
                lon: 112.6796,
                type: 'main',
                description: '南侧主要出入口，连接求实路'
            },
            {
                name: '北门',
                lat: 26.8908,
                lon: 112.6796,
                type: 'side',
                description: '北侧出入口，靠近教学区'
            },
            {
                name: '西门',
                lat: 26.8848,
                lon: 112.6761,
                type: 'side',
                description: '西侧出入口，靠近生活区和宿舍'
            }
        ];

        // 学校围栏边界定义 - 防止路径穿越围栏
        const SCHOOL_BOUNDARY = {
            north: 26.8920,   // 北边界
            south: 26.8780,   // 南边界
            east: 112.6850,   // 东边界
            west: 112.6740    // 西边界
        };

        // 检查坐标是否在学校围栏内
        function isInsideSchoolBoundary(lat, lon) {
            return lat >= SCHOOL_BOUNDARY.south && lat <= SCHOOL_BOUNDARY.north &&
                   lon >= SCHOOL_BOUNDARY.west && lon <= SCHOOL_BOUNDARY.east;
        }

        // 万达广场坐标（根据OpenStreetMap官方API精确坐标）
        const WANDA_LAT = 26.8892785;
        const WANDA_LON = 112.6609182;
        
        // 预设目的地
        const DESTINATIONS = {
            'wanda': { lat: WANDA_LAT, lon: WANDA_LON, name: '酃湖万达广场' },
            'gaotie': { lat: 26.8945, lon: 112.6123, name: '衡阳东高铁站' },
            'yiyuan': { lat: 26.8756, lon: 112.6234, name: '南华大学附属第一医院' },
            'huochezhan': { lat: 26.8834, lon: 112.6167, name: '衡阳火车站' }
        };
        
        // 路径规划服务配置（免费API）
        const ROUTING_SERVICES = [
            {
                name: 'OSRM',
                url: 'https://router.project-osrm.org/route/v1/driving/',
                description: '开源路径规划服务'
            },
            {
                name: 'GraphHopper',
                url: 'https://graphhopper.com/api/1/route',
                description: 'GraphHopper免费API',
                key: '' // 可选：如果有API key可以填入
            }
        ];

        // GPS数据滤波器 - 防止定位乱飘
        class GPSFilter {
            constructor() {
                this.history = [];
                this.maxHistory = 3;  // 减少历史记录，提高响应速度
                this.maxJumpDistance = 0.002; // 约200米，更严格的过滤
                this.lastValidPosition = null;
                this.consecutiveValidCount = 0;
            }

            filter(lat, lon) {
                // 基本范围检查
                if (lat < -90 || lat > 90 || lon < -180 || lon > 180 || lat === 0 || lon === 0) {
                    log(`⚠️ GPS坐标无效: ${lat}, ${lon}`, 'warning');
                    return this.lastValidPosition;
                }

                // 初始化
                if (!this.lastValidPosition) {
                    this.lastValidPosition = { lat, lon };
                    this.history.push({ lat, lon });
                    this.consecutiveValidCount = 1;
                    log(`📍 GPS滤波器初始化: ${lat.toFixed(6)}, ${lon.toFixed(6)}`, 'info');
                    return { lat, lon };
                }

                // 计算距离
                const latDiff = lat - this.lastValidPosition.lat;
                const lonDiff = lon - this.lastValidPosition.lon;
                const distance = Math.sqrt(latDiff * latDiff + lonDiff * lonDiff);

                // 检查异常跳跃
                if (distance > this.maxJumpDistance) {
                    log(`⚠️ GPS异常跳跃被过滤: 距离 ${(distance * 111000).toFixed(0)}米`, 'warning');
                    this.consecutiveValidCount = 0;
                    return this.lastValidPosition; // 返回上次有效位置
                }

                // 连续有效计数
                this.consecutiveValidCount++;

                // 添加到历史记录
                this.history.push({ lat, lon });
                if (this.history.length > this.maxHistory) {
                    this.history.shift();
                }

                // 只有连续收到有效数据才进行平滑处理
                if (this.consecutiveValidCount >= 2) {
                    // 计算加权平均（最新数据权重更大）
                    let totalWeight = 0;
                    let weightedLat = 0;
                    let weightedLon = 0;

                    for (let i = 0; i < this.history.length; i++) {
                        const weight = i + 1; // 越新的数据权重越大
                        weightedLat += this.history[i].lat * weight;
                        weightedLon += this.history[i].lon * weight;
                        totalWeight += weight;
                    }

                    const avgLat = weightedLat / totalWeight;
                    const avgLon = weightedLon / totalWeight;

                    this.lastValidPosition = { lat: avgLat, lon: avgLon };
                } else {
                    // 前几个数据直接使用
                    this.lastValidPosition = { lat, lon };
                }

                return this.lastValidPosition;
            }
        }

        const gpsFilter = new GPSFilter();

        // 日志函数
        function log(message, type = 'info') {
            const logPanel = document.getElementById('logPanel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        // 初始化地图
        function initMap() {
            log('🗺️ 初始化OpenStreetMap...', 'info');
            
            map = L.map('map').setView([DEFAULT_LAT, DEFAULT_LON], 16);
            
            // 添加多个地图源，提高加载成功率
            const mapSources = [
                {
                    name: 'OpenStreetMap',
                    url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                },
                {
                    name: 'OpenStreetMap DE',
                    url: 'https://{s}.tile.openstreetmap.de/{z}/{x}/{y}.png',
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                },
                {
                    name: 'CartoDB Positron',
                    url: 'https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png',
                    attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>'
                }
            ];

            // 尝试加载地图源
            let mapLoaded = false;
            for (let i = 0; i < mapSources.length && !mapLoaded; i++) {
                try {
                    const tileLayer = L.tileLayer(mapSources[i].url, {
                        attribution: mapSources[i].attribution,
                        maxZoom: 19,
                        timeout: 10000,  // 10秒超时
                        errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
                    });

                    tileLayer.addTo(map);
                    log(`✅ 地图源加载成功: ${mapSources[i].name}`, 'success');
                    mapLoaded = true;
                } catch (error) {
                    log(`⚠️ 地图源加载失败: ${mapSources[i].name} - ${error.message}`, 'warning');
                }
            }

            if (!mapLoaded) {
                log('❌ 所有地图源加载失败，使用离线模式', 'error');
            }
            
            // 创建当前位置标记
            currentMarker = L.marker([DEFAULT_LAT, DEFAULT_LON])
                .addTo(map)
                .bindPopup('📍 当前GPS位置<br>衡阳师范学院')
                .openPopup();
            
            log('✅ OpenStreetMap初始化完成', 'success');
        }
        
        // 从ThingSpeak获取GPS数据
        async function loadGPSFromThingSpeak() {
            try {
                log('📡 正在从ThingSpeak获取GPS数据...', 'info');

                // 尝试多种API方式获取数据
                let data = null;
                let success = false;

                // 方式1: 使用读取API密钥
                try {
                    const url1 = `https://api.thingspeak.com/channels/${THINGSPEAK_CHANNEL}/feeds.json?api_key=${THINGSPEAK_READ_KEY}&results=1`;
                    log(`🌐 尝试API密钥方式: ${url1}`, 'info');
                    const response1 = await fetch(url1);
                    data = await response1.json();
                    log(`📊 API密钥响应: ${JSON.stringify(data)}`, 'info');
                    if (data && data.feeds && data.feeds.length > 0) {
                        success = true;
                        log('✅ 使用读取API密钥成功获取数据', 'success');
                    } else {
                        log('⚠️ API密钥方式返回空数据', 'warning');
                    }
                } catch (e) {
                    log(`⚠️ 读取API密钥方式失败: ${e.message}，尝试公开API`, 'warning');
                }

                // 方式2: 使用公开API（如果方式1失败）
                if (!success) {
                    try {
                        const url2 = `https://api.thingspeak.com/channels/${THINGSPEAK_CHANNEL}/feeds.json?results=1`;
                        const response2 = await fetch(url2);
                        data = await response2.json();
                        if (data && data.feeds && data.feeds.length > 0) {
                            success = true;
                            log('✅ 使用公开API成功获取数据', 'success');
                        }
                    } catch (e) {
                        log('❌ 公开API也失败', 'error');
                    }
                }

                if (success && data.feeds && data.feeds.length > 0) {
                    const feed = data.feeds[0];
                    const rawLat = parseFloat(feed.field1);
                    const rawLon = parseFloat(feed.field2);
                    const alt = parseFloat(feed.field3) || 0;

                    log(`📍 ThingSpeak数据: 纬度=${rawLat}, 经度=${rawLon}, 海拔=${alt}, 时间=${feed.created_at}`, 'info');

                    // 检查数据有效性
                    if (!isNaN(rawLat) && !isNaN(rawLon) && rawLat !== 0 && rawLon !== 0) {
                        updateGPSDisplay(rawLat, rawLon, alt, feed.created_at);
                        log(`✅ GPS数据已更新: ${rawLat.toFixed(6)}, ${rawLon.toFixed(6)}`, 'success');
                    } else {
                        log('⚠️ ThingSpeak数据无效，使用默认位置', 'warning');
                        updateGPSDisplay(DEFAULT_LAT, DEFAULT_LON, alt, feed.created_at || new Date().toISOString());
                    }
                } else {
                    log('⚠️ 未获取到有效ThingSpeak数据，使用默认位置', 'warning');
                    updateGPSDisplay(DEFAULT_LAT, DEFAULT_LON, 0, new Date().toISOString());
                }
            } catch (error) {
                log(`❌ 获取ThingSpeak数据失败: ${error.message}`, 'error');
                updateGPSDisplay(DEFAULT_LAT, DEFAULT_LON, 0, new Date().toISOString());
            }
        }
        
        // 更新GPS显示
        function updateGPSDisplay(lat, lon, alt, timestamp) {
            document.getElementById('currentLat').textContent = lat.toFixed(6);
            document.getElementById('currentLon').textContent = lon.toFixed(6);
            document.getElementById('currentAlt').textContent = alt.toFixed(1);
            document.getElementById('updateTime').textContent = new Date(timestamp).toLocaleTimeString();

            // 更新地图标记
            currentMarker.setLatLng([lat, lon]);
            currentMarker.setPopupContent(`📍 当前GPS位置<br>纬度: ${lat.toFixed(6)}<br>经度: ${lon.toFixed(6)}<br>海拔: ${alt.toFixed(1)}m`);

            // 更新当前位置
            currentPosition = { lat: lat, lng: lon };

            // 如果正在导航，检查是否需要更新路线和导航指令
            updateRouteIfNeeded();
            updateNavigationInstructions();
        }

        // 计算两点间距离（米）
        function calculateDistance(lat1, lon1, lat2, lon2) {
            const R = 6371000; // 地球半径（米）
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLon = (lon2 - lon1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                    Math.sin(dLon/2) * Math.sin(dLon/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        // 智能选择最佳出入口
        function findBestExit(currentLat, currentLon, destinationLat, destinationLon) {
            let bestExit = null;
            let bestScore = Infinity;

            log('🔍 分析各出入口到目的地的距离...', 'info');

            for (const exit of CAMPUS_EXITS) {
                // 计算从出入口到目的地的距离
                const distanceToDestination = calculateDistance(exit.lat, exit.lon, destinationLat, destinationLon);

                // 计算从当前位置到出入口的距离
                const distanceToExit = calculateDistance(currentLat, currentLon, exit.lat, exit.lon);

                // 综合评分：出入口到目的地的距离 + 当前位置到出入口距离的权重
                let score = distanceToDestination + (distanceToExit * 0.3);

                // 主门优先（降低评分）
                if (exit.type === 'main') {
                    score *= 0.9;
                }

                log(`📍 ${exit.name}: 到目的地${(distanceToDestination/1000).toFixed(2)}km, 到出入口${(distanceToExit/1000).toFixed(2)}km, 评分${score.toFixed(0)}`, 'info');

                if (score < bestScore) {
                    bestScore = score;
                    bestExit = exit;
                }
            }

            const selectedExit = bestExit || CAMPUS_EXITS[0];
            log(`✅ 选择最佳出入口: ${selectedExit.name} - ${selectedExit.description}`, 'success');

            return selectedExit;
        }

        // 使用OSRM进行真实道路路径规划
        async function planRouteWithOSRM(startLat, startLon, endLat, endLon, profile = 'foot') {
            try {
                log(`🗺️ 使用OSRM ${profile}模式进行路径规划: (${startLat.toFixed(6)}, ${startLon.toFixed(6)}) → (${endLat.toFixed(6)}, ${endLon.toFixed(6)})`, 'info');

                // OSRM官方API - 支持多种交通模式
                const url = `https://router.project-osrm.org/route/v1/${profile}/${startLon},${startLat};${endLon},${endLat}?overview=full&geometries=geojson&steps=true&alternatives=true&continue_straight=default&annotations=true`;

                log(`🌐 OSRM API请求: ${url}`, 'info');

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'User-Agent': 'HunanInstituteOfTechnology-Navigation/1.0'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                // 只在调试模式下显示完整响应数据
                if (window.DEBUG_MODE) {
                    log(`📊 OSRM响应数据: ${JSON.stringify(data, null, 2)}`, 'info');
                }

                if (data.code === 'Ok' && data.routes && data.routes.length > 0) {
                    const route = data.routes[0]; // 选择第一条路径（通常是最优的）

                    log(`✅ OSRM ${profile}模式路径规划成功!`, 'success');
                    log(`📏 距离: ${(route.distance/1000).toFixed(2)}km`, 'success');
                    log(`⏱️ 预计时间: ${Math.round(route.duration/60)}分钟`, 'success');
                    log(`📍 路径点数: ${route.geometry.coordinates.length}个`, 'info');
                    log(`🧭 导航步骤: ${route.legs[0].steps.length}步`, 'info');

                    // 检查几何数据
                    if (!route.geometry || !route.geometry.coordinates || route.geometry.coordinates.length === 0) {
                        log('❌ 路径几何数据为空', 'error');
                        return null;
                    }

                    // 验证坐标数据
                    const validCoords = route.geometry.coordinates.filter(coord =>
                        coord && coord.length >= 2 && !isNaN(coord[0]) && !isNaN(coord[1])
                    );

                    if (validCoords.length !== route.geometry.coordinates.length) {
                        log(`⚠️ 发现 ${route.geometry.coordinates.length - validCoords.length} 个无效坐标点`, 'warn');
                    }

                    log(`🔍 坐标范围检查:`, 'info');
                    log(`   起点: [${route.geometry.coordinates[0][1]}, ${route.geometry.coordinates[0][0]}]`, 'info');
                    log(`   终点: [${route.geometry.coordinates[validCoords.length-1][1]}, ${route.geometry.coordinates[validCoords.length-1][0]}]`, 'info');

                    // 处理导航步骤
                    const processedSteps = route.legs[0].steps.map((step, index) => {
                        const instruction = translateOSRMInstruction(step);
                        log(`步骤${index + 1}: ${instruction}`, 'info');
                        return {
                            ...step,
                            translatedInstruction: instruction
                        };
                    });

                    return {
                        coordinates: route.geometry.coordinates,
                        distance: route.distance,
                        duration: route.duration,
                        steps: processedSteps,
                        provider: `OSRM-${profile}`,
                        waypoints: data.waypoints,
                        alternatives: data.routes.length > 1 ? data.routes.slice(1) : []
                    };
                } else {
                    const errorMsg = data.message || data.code || '未知错误';
                    log(`❌ OSRM返回错误: ${errorMsg}`, 'error');
                    return null;
                }

            } catch (error) {
                log(`❌ OSRM路径规划失败: ${error.message}`, 'error');
                return null;
            }
        }

        // 翻译OSRM导航指令为中文
        function translateOSRMInstruction(step) {
            const maneuver = step.maneuver;
            const roadName = step.name || '道路';
            const distance = step.distance;

            let instruction = '';

            switch (maneuver.type) {
                case 'depart':
                    instruction = `从${roadName}出发`;
                    break;
                case 'arrive':
                    instruction = `到达目的地`;
                    break;
                case 'turn':
                    const direction = maneuver.modifier;
                    if (direction === 'left') instruction = `左转进入${roadName}`;
                    else if (direction === 'right') instruction = `右转进入${roadName}`;
                    else if (direction === 'sharp left') instruction = `急左转进入${roadName}`;
                    else if (direction === 'sharp right') instruction = `急右转进入${roadName}`;
                    else if (direction === 'slight left') instruction = `稍向左转进入${roadName}`;
                    else if (direction === 'slight right') instruction = `稍向右转进入${roadName}`;
                    else instruction = `转弯进入${roadName}`;
                    break;
                case 'continue':
                    instruction = `继续沿${roadName}直行`;
                    break;
                case 'merge':
                    instruction = `并入${roadName}`;
                    break;
                case 'roundabout':
                    instruction = `进入环岛，第${maneuver.exit || 1}个出口驶出到${roadName}`;
                    break;
                default:
                    instruction = `沿${roadName}行驶`;
            }

            if (distance > 0) {
                if (distance >= 1000) {
                    instruction += ` (${(distance/1000).toFixed(1)}公里)`;
                } else {
                    instruction += ` (${Math.round(distance)}米)`;
                }
            }

            return instruction;
        }

        // 使用GraphHopper进行路径规划（备选方案）
        async function planRouteWithGraphHopper(startLat, startLon, endLat, endLon) {
            try {
                log('🗺️ 使用GraphHopper进行路径规划...', 'info');

                // GraphHopper免费API（有限制）
                const url = `https://graphhopper.com/api/1/route?point=${startLat},${startLon}&point=${endLat},${endLon}&vehicle=car&locale=zh&calc_points=true&debug=true&elevation=false&points_encoded=false`;

                const response = await fetch(url);
                const data = await response.json();

                if (data.paths && data.paths.length > 0) {
                    const path = data.paths[0];
                    log(`✅ GraphHopper路径规划成功: ${(path.distance/1000).toFixed(1)}km, ${Math.round(path.time/60000)}分钟`, 'success');

                    return {
                        coordinates: path.points.coordinates.map(coord => [coord[0], coord[1]]),
                        distance: path.distance,
                        duration: path.time / 1000,
                        instructions: path.instructions,
                        provider: 'GraphHopper'
                    };
                } else {
                    throw new Error('GraphHopper返回无效路径');
                }
            } catch (error) {
                log(`❌ GraphHopper路径规划失败: ${error.message}`, 'error');
                return null;
            }
        }

        // 使用OpenRouteService进行路径规划（备用方案）
        async function planRouteWithOpenRouteService(startLat, startLon, endLat, endLon) {
            try {
                log('🌍 使用OpenRouteService进行路径规划...', 'info');

                // OpenRouteService免费API
                const url = `https://api.openrouteservice.org/v2/directions/driving-car?start=${startLon},${startLat}&end=${endLon},${endLat}`;

                const response = await fetch(url, {
                    headers: {
                        'Accept': 'application/json, application/geo+json, application/gpx+xml, img/png; charset=utf-8'
                    }
                });

                const data = await response.json();

                if (data.features && data.features.length > 0) {
                    const route = data.features[0];
                    const properties = route.properties;

                    log(`✅ OpenRouteService路径规划成功: ${(properties.summary.distance/1000).toFixed(2)}km`, 'success');

                    return {
                        coordinates: route.geometry.coordinates,
                        distance: properties.summary.distance,
                        duration: properties.summary.duration,
                        steps: properties.segments[0].steps || [],
                        provider: 'OpenRouteService'
                    };
                } else {
                    log('❌ OpenRouteService返回空结果', 'error');
                    return null;
                }
            } catch (error) {
                log(`❌ OpenRouteService路径规划失败: ${error.message}`, 'error');
                return null;
            }
        }

        // 智能校园导航路径规划（考虑围栏和出入口）
        async function planRoute(startLat, startLon, endLat, endLon) {
            log(`🎯 开始校园智能导航规划: (${startLat.toFixed(6)}, ${startLon.toFixed(6)}) → (${endLat.toFixed(6)}, ${endLon.toFixed(6)})`, 'info');

            // 检查起点是否在校园内
            const isStartInCampus = isPointInCampus(startLat, startLon);
            const isEndInCampus = isPointInCampus(endLat, endLon);

            log(`📍 起点${isStartInCampus ? '在校园内' : '在校园外'}，终点${isEndInCampus ? '在校园内' : '在校园外'}`, 'info');

            let routeData = null;

            if (isStartInCampus && !isEndInCampus) {
                // 从校园内到校园外：需要经过出入口
                log('🏫➡️🌍 校园内到校园外，规划经过出入口的路径', 'info');
                routeData = await planCampusToExternalRoute(startLat, startLon, endLat, endLon);
            } else if (!isStartInCampus && isEndInCampus) {
                // 从校园外到校园内：需要经过出入口
                log('🌍➡️🏫 校园外到校园内，规划经过出入口的路径', 'info');
                routeData = await planExternalToCampusRoute(startLat, startLon, endLat, endLon);
            } else if (isStartInCampus && isEndInCampus) {
                // 校园内部导航
                log('🏫➡️🏫 校园内部导航', 'info');
                routeData = await planCampusInternalRoute(startLat, startLon, endLat, endLon);
            } else {
                // 校园外部导航，直接使用OSRM
                log('🌍➡️🌍 校园外部导航', 'info');
                routeData = await planExternalRoute(startLat, startLon, endLat, endLon);
            }

            // 如果智能规划失败，使用备用方案
            if (!routeData) {
                log('⚠️ 智能规划失败，使用备用方案...', 'warning');
                routeData = await planFallbackRoute(startLat, startLon, endLat, endLon);
            }

            return routeData;
        }

        // 检查点是否在校园内
        function isPointInCampus(lat, lon) {
            // 湖南工学院边界（基于OpenStreetMap边界框数据）
            const campusBounds = {
                north: 26.8907690,   // 北边界
                south: 26.8796444,   // 南边界
                east: 112.6834018,   // 东边界
                west: 112.6761284    // 西边界
            };

            return lat >= campusBounds.south && lat <= campusBounds.north &&
                   lon >= campusBounds.west && lon <= campusBounds.east;
        }

        // 从校园内到校园外的路径规划
        async function planCampusToExternalRoute(startLat, startLon, endLat, endLon) {
            log('🏫 规划校园内到外部的路径...', 'info');

            // 找到最佳出入口
            const bestExit = findBestExit(startLat, startLon, endLat, endLon);
            log(`🚪 选择最佳出入口: ${bestExit.name} (${bestExit.lat.toFixed(6)}, ${bestExit.lon.toFixed(6)})`, 'info');

            // 分段规划路径
            // 第一段：校园内到出入口
            const segment1 = await planRouteWithOSRM(startLat, startLon, bestExit.lat, bestExit.lon, 'foot');

            // 第二段：出入口到目的地
            const segment2 = await planRouteWithOSRM(bestExit.lat, bestExit.lon, endLat, endLon, 'driving');

            if (segment1 && segment2) {
                // 合并两段路径
                return combineRouteSegments(segment1, segment2, bestExit);
            }

            return null;
        }

        // 从校园外到校园内的路径规划
        async function planExternalToCampusRoute(startLat, startLon, endLat, endLon) {
            log('🌍 规划外部到校园内的路径...', 'info');

            const bestExit = findBestExit(endLat, endLon, startLat, startLon);
            log(`🚪 选择最佳入口: ${bestExit.name}`, 'info');

            // 第一段：起点到入口
            const segment1 = await planRouteWithOSRM(startLat, startLon, bestExit.lat, bestExit.lon, 'driving');

            // 第二段：入口到校园内目的地
            const segment2 = await planRouteWithOSRM(bestExit.lat, bestExit.lon, endLat, endLon, 'foot');

            if (segment1 && segment2) {
                return combineRouteSegments(segment1, segment2, bestExit);
            }

            return null;
        }

        // 校园内部路径规划
        async function planCampusInternalRoute(startLat, startLon, endLat, endLon) {
            log('🏫 规划校园内部路径...', 'info');

            // 校园内优先使用步行模式
            let routeData = await planRouteWithOSRM(startLat, startLon, endLat, endLon, 'foot');

            // 如果步行失败，尝试驾车模式（可能有更好的道路数据）
            if (!routeData) {
                log('⚠️ 步行路径规划失败，尝试驾车模式...', 'warn');
                routeData = await planRouteWithOSRM(startLat, startLon, endLat, endLon, 'driving');
            }

            // 如果OSRM路径规划失败，创建直线路径（校园内部）
            if (!routeData) {
                log('⚠️ OSRM路径规划失败，创建校园内直线路径...', 'warn');
                routeData = createDirectRoute(startLat, startLon, endLat, endLon, '校园内步行');
            }

            return routeData;
        }

        // 创建直线路径（用于校园内部或紧急情况）
        function createDirectRoute(startLat, startLon, endLat, endLon, description) {
            const distance = calculateDistance(startLat, startLon, endLat, endLon) * 1000; // 转换为米
            const duration = distance / 1.4; // 假设步行速度1.4m/s

            return {
                coordinates: [
                    [startLon, startLat],
                    [endLon, endLat]
                ],
                distance: distance,
                duration: duration,
                provider: `Direct-${description}`,
                steps: [{
                    translatedInstruction: `直接前往目的地 (${description})`
                }]
            };
        }

        // 校园外部路径规划
        async function planExternalRoute(startLat, startLon, endLat, endLon) {
            log('🌍 规划校园外部路径...', 'info');

            const distance = calculateDistance(startLat, startLon, endLat, endLon);

            // 根据距离选择交通模式
            const mode = distance > 2000 ? 'driving' : 'foot';
            let routeData = await planRouteWithOSRM(startLat, startLon, endLat, endLon, mode);

            // 如果首选模式失败，尝试另一种模式
            if (!routeData) {
                const fallbackMode = mode === 'driving' ? 'foot' : 'driving';
                routeData = await planRouteWithOSRM(startLat, startLon, endLat, endLon, fallbackMode);
            }

            return routeData;
        }

        // 备用路径规划
        async function planFallbackRoute(startLat, startLon, endLat, endLon) {
            log('🔄 使用备用路径规划...', 'info');

            // 尝试GraphHopper
            let routeData = await planRouteWithGraphHopper(startLat, startLon, endLat, endLon);

            // 尝试OpenRouteService
            if (!routeData) {
                routeData = await planRouteWithOpenRouteService(startLat, startLon, endLat, endLon);
            }

            // 最后使用直线路径
            if (!routeData) {
                routeData = createStraightLineRoute(startLat, startLon, endLat, endLon);
            }

            return routeData;
        }

        // 合并两段路径
        function combineRouteSegments(segment1, segment2, waypoint) {
            log('🔗 合并路径段...', 'info');

            // 合并坐标点
            const combinedCoordinates = [...segment1.coordinates, ...segment2.coordinates.slice(1)];

            // 合并距离和时间
            const totalDistance = segment1.distance + segment2.distance;
            const totalDuration = segment1.duration + segment2.duration;

            // 合并导航步骤
            const combinedSteps = [];

            // 添加第一段步骤
            if (segment1.steps) {
                combinedSteps.push(...segment1.steps);
            }

            // 添加经过出入口的提示
            combinedSteps.push({
                maneuver: { type: 'waypoint' },
                name: waypoint.name,
                distance: 0,
                duration: 0,
                translatedInstruction: `经过${waypoint.name}`
            });

            // 添加第二段步骤
            if (segment2.steps) {
                combinedSteps.push(...segment2.steps);
            }

            log(`✅ 路径合并完成: 总距离${(totalDistance/1000).toFixed(2)}km, 总时间${Math.round(totalDuration/60)}分钟`, 'success');

            return {
                coordinates: combinedCoordinates,
                distance: totalDistance,
                duration: totalDuration,
                steps: combinedSteps,
                provider: `校园智能导航 (${segment1.provider} + ${segment2.provider})`,
                waypoint: waypoint
            };
        }

        // 实时更新路线
        async function updateRouteIfNeeded() {
            if (!isNavigating || !currentDestination) return;

            const now = Date.now();
            if (now - lastRouteUpdate < ROUTE_UPDATE_INTERVAL) return;

            const currentLat = parseFloat(document.getElementById('currentLat').textContent) || DEFAULT_LAT;
            const currentLon = parseFloat(document.getElementById('currentLon').textContent) || DEFAULT_LON;

            // 检查是否位置有显著变化
            const lastLat = currentPosition ? currentPosition.lat : DEFAULT_LAT;
            const lastLon = currentPosition ? currentPosition.lng : DEFAULT_LON;
            const distance = calculateDistance(currentLat, currentLon, lastLat, lastLon);

            // 如果移动距离超过50米，重新规划路线
            if (distance > 0.05) { // 约50米
                log('🔄 位置变化显著，重新规划路线...', 'info');

                const routeData = await planRoute(currentLat, currentLon, currentDestination.lat, currentDestination.lon);

                if (routeData) {
                    displayRoute(routeData);
                    log(`✅ 路线已更新！新距离: ${(routeData.distance/1000).toFixed(2)}km`, 'success');
                }

                lastRouteUpdate = now;
            }
        }

        // 实时导航指令更新
        function updateNavigationInstructions() {
            if (!isNavigating || !currentRouteSteps.length || !currentPosition) return;

            const now = Date.now();
            if (now - lastStepCheckTime < 5000) return; // 每5秒检查一次

            const currentLat = currentPosition.lat;
            const currentLon = currentPosition.lng;

            // 检查是否接近下一个转向点
            for (let i = currentStepIndex; i < currentRouteSteps.length; i++) {
                const step = currentRouteSteps[i];
                if (step.maneuver && step.maneuver.location) {
                    const stepLat = step.maneuver.location[1];
                    const stepLon = step.maneuver.location[0];
                    const distance = calculateDistance(currentLat, currentLon, stepLat, stepLon);

                    // 如果距离转向点小于30米，显示指令
                    if (distance < 0.03) { // 约30米
                        if (i !== currentStepIndex) {
                            currentStepIndex = i;
                            showCurrentInstruction(step, distance);
                        }
                        break;
                    }
                }
            }

            lastStepCheckTime = now;
        }

        // 显示当前导航指令
        function showCurrentInstruction(step, distance) {
            const instruction = step.maneuver ? step.maneuver.instruction : step.instruction || '继续前进';
            const distanceText = distance < 0.01 ? '即将' : `${Math.round(distance * 1000)}米后`;

            // 更新指令面板
            highlightCurrentStep(currentStepIndex);

            // 显示语音提示
            const message = `${distanceText}${instruction}`;
            log(`🧭 导航提示: ${message}`, 'info');

            // 如果浏览器支持语音合成，播放语音提示
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(message);
                utterance.lang = 'zh-CN';
                utterance.rate = 0.8;
                speechSynthesis.speak(utterance);
            }

            // 显示弹窗提示
            showNavigationAlert(message);
        }

        // 高亮当前步骤
        function highlightCurrentStep(stepIndex) {
            const instructionsList = document.getElementById('instructionsList');
            if (!instructionsList) return;

            // 清除之前的高亮
            const items = instructionsList.querySelectorAll('.instruction-item');
            items.forEach(item => item.classList.remove('current-step'));

            // 高亮当前步骤
            if (items[stepIndex]) {
                items[stepIndex].classList.add('current-step');
                items[stepIndex].scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        // 显示导航提示弹窗
        function showNavigationAlert(message) {
            // 创建提示框
            const alertDiv = document.createElement('div');
            alertDiv.className = 'navigation-alert';
            alertDiv.innerHTML = `
                <div class="alert-content">
                    <span class="alert-icon">🧭</span>
                    <span class="alert-text">${message}</span>
                </div>
            `;

            // 添加样式
            alertDiv.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: #FF4444;
                color: white;
                padding: 15px 25px;
                border-radius: 10px;
                box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                z-index: 10000;
                font-size: 16px;
                font-weight: bold;
                animation: slideDown 0.3s ease-out;
            `;

            document.body.appendChild(alertDiv);

            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.style.animation = 'slideUp 0.3s ease-in';
                    setTimeout(() => {
                        if (alertDiv.parentNode) {
                            alertDiv.parentNode.removeChild(alertDiv);
                        }
                    }, 300);
                }
            }, 3000);
        }

        // 创建直线路径（备选方案）
        function createStraightLineRoute(startLat, startLon, endLat, endLon) {
            const distance = calculateDistance(startLat, startLon, endLat, endLon);
            const duration = distance / 35 * 3600; // 假设35km/h平均速度

            return {
                coordinates: [[startLon, startLat], [endLon, endLat]],
                distance: distance * 1000,
                duration: duration,
                steps: [
                    { instruction: '从起点出发', distance: distance * 1000 },
                    { instruction: '直行到达目的地', distance: 0 }
                ],
                provider: '直线路径'
            };
        }

        // 计算两点间距离（Haversine公式）
        function calculateDistance(lat1, lon1, lat2, lon2) {
            const R = 6371; // 地球半径（公里）
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLon = (lon2 - lon1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLon/2) * Math.sin(dLon/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        // 在地图上显示路径
        function displayRoute(routeData) {
            // 清除之前的路径
            if (routeLayer) {
                map.removeLayer(routeLayer);
            }

            // 检查路径数据
            if (!routeData || !routeData.coordinates || routeData.coordinates.length === 0) {
                log('❌ 路径数据为空或无效', 'error');
                return;
            }

            log(`📍 路径包含 ${routeData.coordinates.length} 个坐标点`, 'info');
            log(`🗺️ 第一个点: [${routeData.coordinates[0][1]}, ${routeData.coordinates[0][0]}]`, 'info');
            log(`🏁 最后一个点: [${routeData.coordinates[routeData.coordinates.length-1][1]}, ${routeData.coordinates[routeData.coordinates.length-1][0]}]`, 'info');

            // 转换坐标格式（经度,纬度 → 纬度,经度）
            const latLngs = routeData.coordinates.map(coord => {
                // 确保坐标有效
                if (!coord || coord.length < 2 || isNaN(coord[0]) || isNaN(coord[1])) {
                    log(`⚠️ 发现无效坐标: ${JSON.stringify(coord)}`, 'warn');
                    return null;
                }
                return [coord[1], coord[0]]; // [纬度, 经度]
            }).filter(coord => coord !== null);

            if (latLngs.length === 0) {
                log('❌ 没有有效的坐标点', 'error');
                return;
            }

            log(`✅ 转换后有效坐标点: ${latLngs.length} 个`, 'success');

            // 创建路径线 - 使用更醒目的颜色和样式
            routeLayer = L.polyline(latLngs, {
                color: '#FF4444',        // 鲜红色，非常醒目
                weight: 8,               // 更粗的线条
                opacity: 1.0,            // 完全不透明
                lineCap: 'round',        // 圆形线帽
                lineJoin: 'round'        // 圆形连接
            }).addTo(map);

            // 添加路径动画效果
            let offset = 0;
            const animatePath = () => {
                offset += 2;
                if (routeLayer) {
                    routeLayer.setStyle({
                        dashArray: `20, 10`,
                        dashOffset: offset
                    });
                }
            };
            setInterval(animatePath, 100); // 每100ms更新一次动画

            // 添加起点和终点标记
            const startPoint = latLngs[0];
            const endPoint = latLngs[latLngs.length - 1];

            L.marker(startPoint, {
                icon: L.divIcon({
                    className: 'start-marker',
                    html: '🚗',
                    iconSize: [30, 30]
                })
            }).addTo(map).bindPopup('🚗 起点');

            L.marker(endPoint, {
                icon: L.divIcon({
                    className: 'end-marker',
                    html: '🏁',
                    iconSize: [30, 30]
                })
            }).addTo(map).bindPopup('🏁 终点');

            // 调整地图视野以显示完整路径
            map.fitBounds(routeLayer.getBounds(), { padding: [20, 20] });

            // 显示导航信息
            showNavigationInfo(routeData);

            log(`✅ 路径显示完成，使用${routeData.provider}服务`, 'success');
        }

        // 显示导航信息
        function showNavigationInfo(routeData) {
            const navigationInfo = document.getElementById('navigationInfo');
            const routeDistance = document.getElementById('routeDistance');
            const routeTime = document.getElementById('routeTime');
            const routeProvider = document.getElementById('routeProvider');

            routeDistance.textContent = `${(routeData.distance / 1000).toFixed(2)} 公里`;
            routeTime.textContent = `${Math.round(routeData.duration / 60)} 分钟`;
            routeProvider.textContent = routeData.provider;

            navigationInfo.style.display = 'block';

            // 保存路线步骤用于实时导航
            currentRouteSteps = routeData.steps || routeData.instructions || [];
            currentStepIndex = 0;
            lastStepCheckTime = 0;

            // 显示详细指令
            if (currentRouteSteps.length > 0) {
                showRouteInstructions(currentRouteSteps);
                log(`🧭 导航指令已准备，共${currentRouteSteps.length}个步骤`, 'info');

                // 打印每个步骤的详细信息用于调试
                currentRouteSteps.forEach((step, index) => {
                    log(`步骤${index + 1}: ${JSON.stringify(step)}`, 'info');
                });
            } else {
                log('⚠️ 没有收到详细导航指令', 'warning');
                // 如果没有详细指令，创建基本指令
                const basicInstructions = [
                    { text: '从当前位置出发', distance: 0 },
                    { text: '沿道路前往万达广场', distance: routeData.distance },
                    { text: '到达目的地：万达广场', distance: 0 }
                ];
                showRouteInstructions(basicInstructions);
                log(`🧭 使用基本导航指令，共${basicInstructions.length}个步骤`, 'info');
            }
        }

        // 转换OSRM指令为中文方向指示
        function translateManeuver(maneuver) {
            if (!maneuver) return '';

            const type = maneuver.type;
            const modifier = maneuver.modifier;

            const directions = {
                'depart': '出发',
                'turn': '转弯',
                'new name': '继续行驶',
                'continue': '直行',
                'merge': '汇入',
                'on ramp': '上匝道',
                'off ramp': '下匝道',
                'fork': '岔路',
                'end of road': '道路尽头',
                'use lane': '使用车道',
                'arrive': '到达目的地',
                'roundabout': '环岛',
                'rotary': '转盘',
                'roundabout turn': '环岛转弯',
                'notification': '提示',
                'exit roundabout': '驶出环岛'
            };

            const modifiers = {
                'straight': '直行',
                'uturn': '掉头',
                'sharp right': '急右转',
                'right': '右转',
                'slight right': '稍向右转',
                'slight left': '稍向左转',
                'left': '左转',
                'sharp left': '急左转'
            };

            let instruction = directions[type] || type;
            if (modifier && modifiers[modifier]) {
                if (type === 'turn') {
                    instruction = modifiers[modifier];
                } else if (type === 'continue' && modifier !== 'straight') {
                    instruction = modifiers[modifier];
                } else if (modifier !== 'straight') {
                    instruction += `，${modifiers[modifier]}`;
                }
            }

            return instruction;
        }

        // 显示路径指令
        function showRouteInstructions(instructions) {
            const instructionsList = document.getElementById('instructionsList');
            instructionsList.innerHTML = '';

            if (!instructions || instructions.length === 0) {
                const item = document.createElement('div');
                item.className = 'instruction-item';
                item.innerHTML = '<div class="instruction-icon">!</div><div>暂无导航指令</div>';
                instructionsList.appendChild(item);
                document.getElementById('routeInstructions').style.display = 'block';
                return;
            }

            instructions.forEach((instruction, index) => {
                const item = document.createElement('div');
                item.className = 'instruction-item';

                const icon = document.createElement('div');
                icon.className = 'instruction-icon';
                icon.textContent = index + 1;

                const text = document.createElement('div');
                // 构建详细的中文导航指令
                let instructionText = '';

                // 获取方向指示
                let direction = '';
                if (instruction.maneuver) {
                    direction = translateManeuver(instruction.maneuver);
                }

                // 获取道路名称
                let roadName = '';
                if (instruction.name) {
                    roadName = instruction.name;
                }

                // 构建完整指令 - 优先使用已翻译的指令
                if (typeof instruction === 'string') {
                    // 如果指令是字符串，直接使用
                    instructionText = instruction;
                } else if (instruction.translatedInstruction) {
                    // 优先使用OSRM翻译后的中文指令
                    instructionText = instruction.translatedInstruction;
                } else {
                    // 处理其他格式的步骤
                    let baseInstruction = '';

                    if (instruction.maneuver) {
                        const maneuver = instruction.maneuver;
                        baseInstruction = translateManeuver(maneuver);

                        // 添加道路名称
                        if (instruction.name && instruction.name.trim() !== '') {
                            baseInstruction += `，沿${instruction.name}行驶`;
                        } else if (instruction.ref) {
                            baseInstruction += `，沿${instruction.ref}行驶`;
                        }

                        // 添加目的地信息
                        if (instruction.destinations && instruction.destinations.length > 0) {
                            baseInstruction += `，朝${instruction.destinations.join('、')}方向`;
                        }

                    } else if (direction && roadName) {
                        baseInstruction = `${direction}，沿${roadName}行驶`;
                    } else if (direction) {
                        baseInstruction = direction;
                    } else if (roadName) {
                        baseInstruction = `沿${roadName}行驶`;
                    } else if (instruction.instruction) {
                        baseInstruction = instruction.instruction;
                    } else if (instruction.text) {
                        baseInstruction = instruction.text;
                    } else if (instruction.narrative) {
                        baseInstruction = instruction.narrative;
                    } else {
                        baseInstruction = `步骤 ${index + 1}：继续前进`;
                    }

                    instructionText = baseInstruction;
                }

                // 添加距离信息
                if (instruction.distance) {
                    const distance = instruction.distance > 1000 ?
                        `${(instruction.distance/1000).toFixed(1)}km` :
                        `${Math.round(instruction.distance)}m`;
                    instructionText += ` (${distance})`;
                }

                text.textContent = instructionText;
                log(`📋 导航指令 ${index + 1}: ${instructionText}`, 'info');

                item.appendChild(icon);
                item.appendChild(text);
                instructionsList.appendChild(item);
            });

            document.getElementById('routeInstructions').style.display = 'block';
            log(`✅ 已显示 ${instructions.length} 条导航指令`, 'success');
        }

        // 控制函数
        async function refreshGPSData() {
            log('🔄 手动刷新GPS数据...', 'info');
            await loadGPSFromThingSpeak();
        }

        function centerMap() {
            const lat = parseFloat(document.getElementById('currentLat').textContent);
            const lon = parseFloat(document.getElementById('currentLon').textContent);

            if (!isNaN(lat) && !isNaN(lon)) {
                map.setView([lat, lon], 16);
                log('📍 地图已居中到当前GPS位置', 'info');
            }
        }

        // 显示目的地选择面板
        function showDestinationOptions() {
            const panel = document.getElementById('destinationPanel');
            const optionsContainer = document.getElementById('destinationOptions');

            // 清空现有选项
            optionsContainer.innerHTML = '';

            // 生成目的地选项
            DESTINATION_OPTIONS.forEach((destination, index) => {
                const option = document.createElement('div');
                option.className = 'destination-option';
                option.onclick = () => navigateToDestination(destination);

                option.innerHTML = `
                    <div class="destination-icon">${destination.icon}</div>
                    <div class="destination-info">
                        <div class="destination-name">${destination.name}</div>
                        <div class="destination-description">${destination.description}</div>
                    </div>
                `;

                optionsContainer.appendChild(option);
            });

            panel.style.display = 'block';
            log('🎯 显示目的地选择面板', 'info');
        }

        // 隐藏目的地选择面板
        function hideDestinationOptions() {
            document.getElementById('destinationPanel').style.display = 'none';
            log('❌ 关闭目的地选择面板', 'info');
        }

        // 导航到选定的目的地
        async function navigateToDestination(destination) {
            const currentLat = parseFloat(document.getElementById('currentLat').textContent) || DEFAULT_LAT;
            const currentLon = parseFloat(document.getElementById('currentLon').textContent) || DEFAULT_LON;

            hideDestinationOptions();

            log(`🎯 开始导航到${destination.name}...`, 'info');
            log(`📍 当前位置: (${currentLat.toFixed(6)}, ${currentLon.toFixed(6)})`, 'info');
            log(`🎯 目的地: ${destination.name} (${destination.lat.toFixed(6)}, ${destination.lon.toFixed(6)})`, 'info');

            // 使用智能校园导航系统
            const routeData = await planRoute(currentLat, currentLon, destination.lat, destination.lon);

            if (routeData) {
                displayRoute(routeData);
                log(`✅ 到${destination.name}的路径规划完成！`, 'success');
            } else {
                log(`❌ 到${destination.name}的路径规划失败`, 'error');
            }
        }

        async function startWandaNavigation() {
            log('🎯 启动万达广场导航...', 'info');

            const currentLat = parseFloat(document.getElementById('currentLat').textContent) || DEFAULT_LAT;
            const currentLon = parseFloat(document.getElementById('currentLon').textContent) || DEFAULT_LON;

            log(`📍 当前位置: (${currentLat.toFixed(6)}, ${currentLon.toFixed(6)})`, 'info');
            log(`🎯 目标: 万达广场 (${WANDA_LAT.toFixed(6)}, ${WANDA_LON.toFixed(6)})`, 'info');

            // 使用标准OpenStreetMap路径规划，让系统自动选择最佳路径
            log('🗺️ 使用OpenStreetMap标准路径规划...', 'info');

            const routeData = await planRouteWithOSRM(currentLat, currentLon, WANDA_LAT, WANDA_LON);

            if (routeData) {
                displayRoute(routeData);
                isNavigating = true;
                lastRouteUpdate = Date.now();

                const totalDistance = (routeData.distance / 1000).toFixed(2);
                const totalTime = Math.round(routeData.duration / 60);

                log(`✅ 万达广场导航启动成功！`, 'success');
                log(`📏 距离: ${totalDistance}km, 预计时间: ${totalTime}分钟`, 'success');
                log('🛣️ 使用OpenStreetMap标准路径', 'info');

                currentDestination = { lat: WANDA_LAT, lon: WANDA_LON, name: '万达广场' };
            } else {
                log('❌ 万达广场导航启动失败', 'error');
                currentDestination = null;
            }
        }

        // 检查是否在校园内
        function isInsideCampus(lat, lon) {
            // 湖南工程学院大致边界（根据您的GPS位置推算）
            const campusBounds = {
                north: 26.885,   // 北边界
                south: 26.878,   // 南边界
                east: 112.682,   // 东边界
                west: 112.674    // 西边界
            };

            const isInside = lat >= campusBounds.south && lat <= campusBounds.north &&
                           lon >= campusBounds.west && lon <= campusBounds.east;

            log(`🏫 校园边界检查: (${lat.toFixed(6)}, ${lon.toFixed(6)}) ${isInside ? '在校园内' : '在校园外'}`, 'info');
            return isInside;
        }

        // 创建经过校门口的路径（考虑围栏限制）
        async function createCampusExitRoute(startLat, startLon, campusExit, destLat, destLon) {
            try {
                log('🛣️ 创建校园出口路径（考虑围栏限制）...', 'info');

                // 第一段：当前位置到校门口
                log('🚶 规划校园内到校门口的路径...', 'info');
                const campusRoute = await planRoute(startLat, startLon, campusExit.lat, campusExit.lon);

                // 第二段：校门口到万达广场
                log('🚗 规划校门口到万达广场的路径...', 'info');
                const externalRoute = await planRoute(campusExit.lat, campusExit.lon, destLat, destLon);

                if (campusRoute && externalRoute && campusRoute.coordinates && externalRoute.coordinates) {
                    log(`✅ 校园内路径: ${campusRoute.coordinates.length} 个点, ${(campusRoute.distance/1000).toFixed(2)}km`, 'success');
                    log(`✅ 校外路径: ${externalRoute.coordinates.length} 个点, ${(externalRoute.distance/1000).toFixed(2)}km`, 'success');

                    // 合并两段路径
                    const combinedCoordinates = [
                        ...campusRoute.coordinates,
                        ...externalRoute.coordinates.slice(1) // 跳过重复的校门口点
                    ];

                    const totalDistance = campusRoute.distance + externalRoute.distance;
                    const totalDuration = campusRoute.duration + externalRoute.duration;

                    // 合并导航步骤
                    const combinedSteps = [];

                    // 添加校园内步骤
                    if (campusRoute.steps && campusRoute.steps.length > 0) {
                        combinedSteps.push(...campusRoute.steps);
                    }

                    // 添加过渡步骤
                    combinedSteps.push({
                        maneuver: { type: 'continue', modifier: 'straight' },
                        name: `从${campusExit.name}继续前往万达广场`,
                        distance: 0,
                        duration: 0
                    });

                    // 添加校外步骤
                    if (externalRoute.steps && externalRoute.steps.length > 0) {
                        combinedSteps.push(...externalRoute.steps);
                    }

                    log(`🔗 路径合并完成: 总共 ${combinedCoordinates.length} 个点, ${combinedSteps.length} 个步骤`, 'success');

                    return {
                        coordinates: combinedCoordinates,
                        distance: totalDistance,
                        duration: totalDuration,
                        steps: combinedSteps,
                        provider: 'OSRM校园路径'
                    };
                } else {
                    log('⚠️ 分段路径规划失败，尝试直接规划', 'warning');
                    // 如果分段规划失败，直接规划到万达广场
                    return await planRoute(startLat, startLon, destLat, destLon);
                }

            } catch (error) {
                log(`❌ 校园出口路径创建失败: ${error.message}`, 'error');
                return null;
            }
        }

        async function planCustomRoute() {
            const startCoords = prompt('请输入起点坐标 (格式: 纬度,经度):', `${DEFAULT_LAT},${DEFAULT_LON}`);
            const endCoords = prompt('请输入终点坐标 (格式: 纬度,经度):', '26.8869,112.6758');

            if (startCoords && endCoords) {
                try {
                    const [startLat, startLon] = startCoords.split(',').map(x => parseFloat(x.trim()));
                    const [endLat, endLon] = endCoords.split(',').map(x => parseFloat(x.trim()));

                    if (isNaN(startLat) || isNaN(startLon) || isNaN(endLat) || isNaN(endLon)) {
                        throw new Error('坐标格式错误');
                    }

                    log(`🗺️ 自定义路径规划: (${startLat}, ${startLon}) → (${endLat}, ${endLon})`, 'info');

                    const routeData = await planRoute(startLat, startLon, endLat, endLon);

                    if (routeData) {
                        displayRoute(routeData);
                        isNavigating = true;
                        log(`✅ 自定义路径规划成功！`, 'success');
                    }
                } catch (error) {
                    log(`❌ 自定义路径规划失败: ${error.message}`, 'error');
                    alert('坐标格式错误，请使用格式: 纬度,经度');
                }
            }
        }



        // 创建直线路径（用于校园内部）
        function createStraightRoute(startLat, startLon, endLat, endLon) {
            const coordinates = [
                [startLon, startLat],
                [endLon, endLat]
            ];

            // 计算直线距离
            const distance = calculateDistance(startLat, startLon, endLat, endLon);
            const duration = distance / 1.4 * 60; // 假设步行速度1.4m/s

            return {
                coordinates: coordinates,
                distance: distance,
                duration: duration,
                instructions: [`直线步行 ${(distance/1000).toFixed(2)}km`]
            };
        }

        // 计算两点间距离（米）
        function calculateDistance(lat1, lon1, lat2, lon2) {
            const R = 6371000; // 地球半径（米）
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLon = (lon2 - lon1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLon/2) * Math.sin(dLon/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        function clearRoute() {
            if (routeLayer) {
                map.removeLayer(routeLayer);
                routeLayer = null;
            }

            // 清除所有标记（除了当前位置标记）
            map.eachLayer(function(layer) {
                if (layer instanceof L.Marker && layer !== currentMarker) {
                    map.removeLayer(layer);
                }
            });

            // 隐藏导航信息
            document.getElementById('navigationInfo').style.display = 'none';
            document.getElementById('routeInstructions').style.display = 'none';

            // 停止导航并清除目的地
            isNavigating = false;
            currentDestination = null;
            lastRouteUpdate = 0;
            currentRouteSteps = [];
            currentStepIndex = 0;
            lastStepCheckTime = 0;

            log('🧹 路径已清除，导航已停止', 'info');
        }

        function toggleInstructions() {
            const instructions = document.getElementById('routeInstructions');
            if (instructions.style.display === 'none' || instructions.style.display === '') {
                instructions.style.display = 'block';
                log('📋 显示导航指令', 'info');
            } else {
                instructions.style.display = 'none';
                log('📋 隐藏导航指令', 'info');
            }
        }

        // ==================== 串口数据处理功能 ====================

        // 开始读取串口数据
        async function startSerialReading() {
            if (!serialPort || !isSerialConnected) return;

            try {
                const textDecoder = new TextDecoderStream();
                const readableStreamClosed = serialPort.readable.pipeTo(textDecoder.writable);
                reader = textDecoder.readable.getReader();

                log('📡 开始监听串口数据...', 'info');

                // 持续读取数据
                while (isSerialConnected) {
                    try {
                        const { value, done } = await reader.read();
                        if (done) break;

                        // 处理接收到的数据
                        processSerialData(value);

                    } catch (error) {
                        log(`⚠️ 串口读取错误: ${error.message}`, 'warning');
                        break;
                    }
                }
            } catch (error) {
                log(`❌ 串口读取失败: ${error.message}`, 'error');
            }
        }

        // 处理串口数据
        function processSerialData(data) {
            const lines = data.split('\n');

            for (let line of lines) {
                line = line.trim();
                if (line.length === 0) continue;

                log(`📥 串口数据: ${line}`, 'info');

                // 检查是否是万达导航命令
                if (line.includes('🗺️ 开始导航到酃湖万达广场') ||
                    line.includes('WANDA_') ||
                    line.includes('🎯 目的地: 酃湖万达广场')) {

                    log('🎯 检测到万达导航命令，自动启动导航！', 'success');

                    // 延迟500ms后自动启动万达导航
                    setTimeout(() => {
                        startWandaNavigation();
                    }, 500);
                }

                // 检查GPS位置更新
                if (line.includes('📍 当前位置:')) {
                    // 可以在这里解析GPS坐标并更新地图
                    parseGPSFromSerial(line);
                }
            }
        }

        // 从串口数据解析GPS坐标
        function parseGPSFromSerial(line) {
            try {
                // 匹配格式: "📍 当前位置: 26.881200°N, 112.676900°E"
                const match = line.match(/📍 当前位置:\s*([\d.]+)°N,\s*([\d.]+)°E/);
                if (match) {
                    const lat = parseFloat(match[1]);
                    const lon = parseFloat(match[2]);

                    if (lat && lon) {
                        log(`🛰️ 从串口更新GPS: ${lat.toFixed(6)}, ${lon.toFixed(6)}`, 'info');
                        updateGPSPosition(lat, lon);
                    }
                }
            } catch (error) {
                log(`⚠️ GPS数据解析错误: ${error.message}`, 'warning');
            }
        }

        // 更新GPS位置
        function updateGPSPosition(lat, lon) {
            // 更新当前位置标记
            if (currentMarker) {
                currentMarker.setLatLng([lat, lon]);
                currentMarker.bindPopup(`📍 当前GPS位置<br>纬度: ${lat.toFixed(6)}°<br>经度: ${lon.toFixed(6)}°`);
            }

            // 更新显示的坐标
            document.getElementById('currentLat').textContent = lat.toFixed(6);
            document.getElementById('currentLon').textContent = lon.toFixed(6);

            // 如果正在导航，检查是否需要重新规划路线
            if (isNavigating && currentDestination) {
                const now = Date.now();
                if (now - lastRouteUpdate > ROUTE_UPDATE_INTERVAL) {
                    log('🔄 GPS位置更新，重新规划路线...', 'info');
                    planRoute(lat, lon, currentDestination.lat, currentDestination.lon)
                        .then(routeData => {
                            if (routeData) {
                                displayRoute(routeData);
                                lastRouteUpdate = now;
                            }
                        });
                }
            }
        }

        // ==================== 串口功能结束 ====================

        // 初始化应用
        window.onload = function() {
            log('🚀 STM32F407VET6 GPS追踪导航系统启动中...', 'info');

            initMap();
            loadGPSFromThingSpeak();

            // 每10秒自动刷新GPS数据
            setInterval(loadGPSFromThingSpeak, 10000);

            log('✅ 系统初始化完成！', 'success');
            log('💡 提示: 点击"万达导航"开始路径规划', 'info');
        };

        // 调试模式切换
        function toggleDebugMode() {
            window.DEBUG_MODE = !window.DEBUG_MODE;
            const status = window.DEBUG_MODE ? '开启' : '关闭';
            log(`🔧 调试模式已${status}`, window.DEBUG_MODE ? 'info' : 'success');

            if (window.DEBUG_MODE) {
                log('📊 调试模式功能:', 'info');
                log('  - 显示详细的OSRM API响应数据', 'info');
                log('  - 显示路径坐标转换过程', 'info');
                log('  - 显示GPS数据处理详情', 'info');
            }
        }

        // 手动触发路径重新规划（调试用）
        function debugRePlanRoute() {
            if (!currentDestination) {
                log('❌ 没有当前目的地，无法重新规划', 'error');
                return;
            }

            const currentLat = parseFloat(document.getElementById('currentLat').textContent) || DEFAULT_LAT;
            const currentLon = parseFloat(document.getElementById('currentLon').textContent) || DEFAULT_LON;

            log('🔧 调试: 手动重新规划路径...', 'info');
            planRoute(currentLat, currentLon, currentDestination.lat, currentDestination.lon)
                .then(routeData => {
                    if (routeData) {
                        displayRoute(routeData);
                        log('✅ 调试: 路径重新规划完成', 'success');
                    } else {
                        log('❌ 调试: 路径重新规划失败', 'error');
                    }
                });
        }

        // 测试串口连接
        async function testSerialConnection() {
            log('📡 开始串口连接测试...', 'info');

            if (!('serial' in navigator)) {
                log('❌ 浏览器不支持Web Serial API', 'error');
                log('💡 请使用Chrome/Edge浏览器，并确保启用了实验性功能', 'info');
                return;
            }

            try {
                if (!serialPort) {
                    log('🔌 请求串口权限...', 'info');
                    serialPort = await navigator.serial.requestPort();
                    log('✅ 串口权限获取成功', 'success');
                }

                if (!isSerialConnected) {
                    log('🔗 打开串口连接...', 'info');
                    await serialPort.open({ baudRate: 115200 });
                    isSerialConnected = true;
                    log('✅ 串口连接成功 (115200 baud)', 'success');

                    // 开始读取数据
                    startSerialReading();
                } else {
                    log('✅ 串口已连接', 'success');
                }

                // 发送测试命令
                log('📤 发送测试命令到单片机...', 'info');
                const writer = serialPort.writable.getWriter();
                await writer.write(new TextEncoder().encode('gps_status\r\n'));
                writer.releaseLock();
                log('✅ 测试命令已发送', 'success');

            } catch (error) {
                log(`❌ 串口连接失败: ${error.message}`, 'error');
                log('💡 请检查:', 'info');
                log('  1. 单片机是否已连接到电脑', 'info');
                log('  2. 串口驱动是否正确安装', 'info');
                log('  3. 其他程序是否占用了串口', 'info');
            }
        }
    </script>
</body>
</html>
