# LCD硬件连接检查清单

## 🚨 紧急检查 - 串口正常但LCD白屏

### 📌 引脚连接检查

**请用万用表测量以下引脚电压（相对于GND）：**

| STM32引脚 | LCD引脚 | 期望电压 | 实测电压 | 状态 |
|-----------|---------|----------|----------|------|
| PB13      | SCK     | 有时钟信号 | ___V | ⬜ |
| PB15      | SDA/MOSI| 有数据信号 | ___V | ⬜ |
| PD0       | DC      | 3.3V     | ___V | ⬜ |
| PD1       | BLK     | 3.3V     | ___V | ⬜ |
| PD4       | RES     | 3.3V     | ___V | ⬜ |
| 3.3V      | VCC     | 3.3V     | ___V | ⬜ |
| GND       | GND     | 0V       | ___V | ⬜ |

### 🔍 关键检查点

#### 1. 背光测试
运行新代码后，LCD背光应该闪烁10次。
- [ ] 看到背光闪烁 ✅
- [ ] 背光不闪烁 ❌ → 检查PD1连接

#### 2. 显示反转测试
初始化过程中会测试显示反转：
- [ ] 看到屏幕从白色变黑色再变白色 ✅
- [ ] 没有任何变化 ❌ → SPI通信问题

#### 3. 红色填充测试
最后会强制填充红色：
- [ ] 看到屏幕变红色 ✅
- [ ] 仍然是白色 ❌ → 硬件问题

### 🛠️ 常见问题

#### 问题1：LCD模块不兼容
**症状**: 串口正常，但LCD无任何反应
**检查**: 
- LCD是否为ILI9341驱动？
- 是否为4线SPI接口？
- 尝试其他LCD模块

#### 问题2：电源问题
**症状**: 背光不亮或很暗
**检查**:
- VCC电压是否为3.3V？
- 电流是否足够（至少100mA）？

#### 问题3：SPI连接错误
**症状**: 背光亮但无显示
**检查**:
- SCK和MOSI是否接反？
- 是否有虚焊？

#### 问题4：控制信号问题
**症状**: 有部分显示但不正确
**检查**:
- DC引脚是否正确连接到PD0？
- RES引脚是否正确连接到PD4？

### 📋 测试步骤

1. **编译并烧录新代码**
2. **观察串口输出**
3. **观察LCD变化**：
   - 背光闪烁10次
   - 显示反转测试（白→黑→白）
   - 红色填充测试
4. **用万用表测量所有引脚电压**
5. **检查连线是否牢固**

### 🆘 如果仍然白屏

请提供：
1. 万用表测量的所有引脚电压
2. LCD模块的型号和照片
3. 硬件连接的清晰照片
4. 是否看到背光闪烁
5. 是否看到任何显示变化

### 💡 临时解决方案

如果确认硬件连接正确但仍有问题，可以尝试：

1. **更换LCD模块** - 可能是LCD损坏
2. **降低SPI速度** - 在lcd_init_hal.c中增加更多延时
3. **尝试不同的初始化序列** - 可能需要特定的LCD驱动

### 🎯 成功标志

修复成功后应该看到：
- 背光正常闪烁
- 显示反转时屏幕变化
- 红色填充时整个屏幕变红
- 后续的彩色方块测试正常显示
