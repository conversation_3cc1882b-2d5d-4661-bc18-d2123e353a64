#ifndef __MPU6050_DRIVER_H__
#define __MPU6050_DRIVER_H__

#include "main.h"
#include "i2c.h"
#include <stdint.h>
#include <string.h>

/* MPU6050 设备地址 */
#define MPU6050_ADDR        0xD0    // MPU6050的IIC地址 (AD0=0时)
#define MPU6050_ADDR_ALT    0xD2    // MPU6050的IIC地址 (AD0=1时)

/* MPU6050 寄存器地址 */
#define MPU6050_REG_SMPLRT_DIV      0x19    // 采样率分频器
#define MPU6050_REG_CONFIG          0x1A    // 配置寄存器
#define MPU6050_REG_GYRO_CONFIG     0x1B    // 陀螺仪配置
#define MPU6050_REG_ACCEL_CONFIG    0x1C    // 加速度计配置
#define MPU6050_REG_ACCEL_XOUT_H    0x3B    // 加速度计X轴高字节
#define MPU6050_REG_ACCEL_XOUT_L    0x3C    // 加速度计X轴低字节
#define MPU6050_REG_ACCEL_YOUT_H    0x3D    // 加速度计Y轴高字节
#define MPU6050_REG_ACCEL_YOUT_L    0x3E    // 加速度计Y轴低字节
#define MPU6050_REG_ACCEL_ZOUT_H    0x3F    // 加速度计Z轴高字节
#define MPU6050_REG_ACCEL_ZOUT_L    0x40    // 加速度计Z轴低字节
#define MPU6050_REG_TEMP_OUT_H      0x41    // 温度传感器高字节
#define MPU6050_REG_TEMP_OUT_L      0x42    // 温度传感器低字节
#define MPU6050_REG_GYRO_XOUT_H     0x43    // 陀螺仪X轴高字节
#define MPU6050_REG_GYRO_XOUT_L     0x44    // 陀螺仪X轴低字节
#define MPU6050_REG_GYRO_YOUT_H     0x45    // 陀螺仪Y轴高字节
#define MPU6050_REG_GYRO_YOUT_L     0x46    // 陀螺仪Y轴低字节
#define MPU6050_REG_GYRO_ZOUT_H     0x47    // 陀螺仪Z轴高字节
#define MPU6050_REG_GYRO_ZOUT_L     0x48    // 陀螺仪Z轴低字节
#define MPU6050_REG_PWR_MGMT_1      0x6B    // 电源管理1
#define MPU6050_REG_PWR_MGMT_2      0x6C    // 电源管理2
#define MPU6050_REG_WHO_AM_I        0x75    // 设备ID寄存器

/* MPU6050 配置值 */
#define MPU6050_WHO_AM_I_VAL        0x68    // WHO_AM_I寄存器期望值

/* 陀螺仪量程配置 */
typedef enum {
    MPU6050_GYRO_RANGE_250DPS  = 0x00,  // ±250°/s
    MPU6050_GYRO_RANGE_500DPS  = 0x08,  // ±500°/s
    MPU6050_GYRO_RANGE_1000DPS = 0x10,  // ±1000°/s
    MPU6050_GYRO_RANGE_2000DPS = 0x18   // ±2000°/s
} MPU6050_GyroRange_t;

/* 加速度计量程配置 */
typedef enum {
    MPU6050_ACCEL_RANGE_2G  = 0x00,     // ±2g
    MPU6050_ACCEL_RANGE_4G  = 0x08,     // ±4g
    MPU6050_ACCEL_RANGE_8G  = 0x10,     // ±8g
    MPU6050_ACCEL_RANGE_16G = 0x18      // ±16g
} MPU6050_AccelRange_t;

/* MPU6050 原始数据结构体 */
typedef struct {
    int16_t accel_x_raw;    // 加速度计X轴原始数据
    int16_t accel_y_raw;    // 加速度计Y轴原始数据
    int16_t accel_z_raw;    // 加速度计Z轴原始数据
    int16_t temp_raw;       // 温度传感器原始数据
    int16_t gyro_x_raw;     // 陀螺仪X轴原始数据
    int16_t gyro_y_raw;     // 陀螺仪Y轴原始数据
    int16_t gyro_z_raw;     // 陀螺仪Z轴原始数据
} MPU6050_RawData_t;

/* MPU6050 处理后数据结构体 */
typedef struct {
    float accel_x;          // 加速度计X轴数据 (g)
    float accel_y;          // 加速度计Y轴数据 (g)
    float accel_z;          // 加速度计Z轴数据 (g)
    float temperature;      // 温度数据 (°C)
    float gyro_x;           // 陀螺仪X轴数据 (°/s)
    float gyro_y;           // 陀螺仪Y轴数据 (°/s)
    float gyro_z;           // 陀螺仪Z轴数据 (°/s)
} MPU6050_Data_t;

/* MPU6050 配置结构体 */
typedef struct {
    uint8_t device_addr;                // 设备IIC地址
    MPU6050_GyroRange_t gyro_range;     // 陀螺仪量程
    MPU6050_AccelRange_t accel_range;   // 加速度计量程
    uint8_t sample_rate_div;            // 采样率分频器
    uint8_t dlpf_cfg;                   // 数字低通滤波器配置
} MPU6050_Config_t;

/* MPU6050 设备结构体 */
typedef struct {
    I2C_HandleTypeDef *hi2c;            // IIC句柄
    MPU6050_Config_t config;            // 配置参数
    MPU6050_RawData_t raw_data;         // 原始数据
    MPU6050_Data_t data;                // 处理后数据
    uint8_t is_initialized;             // 初始化标志
    uint8_t is_connected;               // 连接状态标志
} MPU6050_t;

/* 函数声明 */
uint8_t MPU6050_WriteReg(MPU6050_t *mpu, uint8_t reg, uint8_t data);
uint8_t MPU6050_ReadReg(MPU6050_t *mpu, uint8_t reg, uint8_t *data);
uint8_t MPU6050_ReadRegs(MPU6050_t *mpu, uint8_t reg, uint8_t *data, uint8_t length);

uint8_t MPU6050_Init(MPU6050_t *mpu, I2C_HandleTypeDef *hi2c);
uint8_t MPU6050_CheckConnection(MPU6050_t *mpu);
uint8_t MPU6050_Configure(MPU6050_t *mpu, MPU6050_Config_t *config);

uint8_t MPU6050_ReadRawData(MPU6050_t *mpu);
void MPU6050_ProcessData(MPU6050_t *mpu);

#endif
