#include "mpu6050_driver.h"

/* IIC通信超时时间 */
#define MPU6050_I2C_TIMEOUT     100

/* 量程转换系数 */
static const float gyro_scale_factors[] = {
    131.0f,     // ±250°/s
    65.5f,      // ±500°/s
    32.8f,      // ±1000°/s
    16.4f       // ±2000°/s
};

static const float accel_scale_factors[] = {
    16384.0f,   // ±2g
    8192.0f,    // ±4g
    4096.0f,    // ±8g
    2048.0f     // ±16g
};

/**
 * @brief 向MPU6050写入单个寄存器
 * @param mpu MPU6050设备结构体指针
 * @param reg 寄存器地址
 * @param data 要写入的数据
 * @return 0:成功, 1:失败
 */
uint8_t MPU6050_WriteReg(MPU6050_t *mpu, uint8_t reg, uint8_t data)
{
    if (mpu == NULL || mpu->hi2c == NULL) {
        return 1;
    }
    
    HAL_StatusTypeDef status = HAL_I2C_Mem_Write(mpu->hi2c, mpu->config.device_addr, 
                                                 reg, I2C_MEMADD_SIZE_8BIT, 
                                                 &data, 1, MPU6050_I2C_TIMEOUT);
    return (status == HAL_OK) ? 0 : 1;
}

/**
 * @brief 从MPU6050读取单个寄存器
 * @param mpu MPU6050设备结构体指针
 * @param reg 寄存器地址
 * @param data 读取数据的缓冲区指针
 * @return 0:成功, 1:失败
 */
uint8_t MPU6050_ReadReg(MPU6050_t *mpu, uint8_t reg, uint8_t *data)
{
    if (mpu == NULL || mpu->hi2c == NULL || data == NULL) {
        return 1;
    }
    
    HAL_StatusTypeDef status = HAL_I2C_Mem_Read(mpu->hi2c, mpu->config.device_addr, 
                                                reg, I2C_MEMADD_SIZE_8BIT, 
                                                data, 1, MPU6050_I2C_TIMEOUT);
    return (status == HAL_OK) ? 0 : 1;
}

/**
 * @brief 从MPU6050读取多个寄存器
 * @param mpu MPU6050设备结构体指针
 * @param reg 起始寄存器地址
 * @param data 读取数据的缓冲区指针
 * @param length 要读取的字节数
 * @return 0:成功, 1:失败
 */
uint8_t MPU6050_ReadRegs(MPU6050_t *mpu, uint8_t reg, uint8_t *data, uint8_t length)
{
    if (mpu == NULL || mpu->hi2c == NULL || data == NULL || length == 0) {
        return 1;
    }
    
    HAL_StatusTypeDef status = HAL_I2C_Mem_Read(mpu->hi2c, mpu->config.device_addr, 
                                                reg, I2C_MEMADD_SIZE_8BIT, 
                                                data, length, MPU6050_I2C_TIMEOUT);
    return (status == HAL_OK) ? 0 : 1;
}

/**
 * @brief 检查MPU6050连接状态
 * @param mpu MPU6050设备结构体指针
 * @return 0:连接正常, 1:连接失败
 */
uint8_t MPU6050_CheckConnection(MPU6050_t *mpu)
{
    uint8_t who_am_i = 0;
    
    if (MPU6050_ReadReg(mpu, MPU6050_REG_WHO_AM_I, &who_am_i) != 0) {
        mpu->is_connected = 0;
        return 1;
    }
    
    if (who_am_i == MPU6050_WHO_AM_I_VAL) {
        mpu->is_connected = 1;
        return 0;
    } else {
        mpu->is_connected = 0;
        return 1;
    }
}

/**
 * @brief 配置MPU6050
 * @param mpu MPU6050设备结构体指针
 * @param config 配置参数指针
 * @return 0:成功, 1:失败
 */
uint8_t MPU6050_Configure(MPU6050_t *mpu, MPU6050_Config_t *config)
{
    if (mpu == NULL || config == NULL) {
        return 1;
    }
    
    // 复制配置参数
    mpu->config = *config;
    
    // 唤醒MPU6050 (清除睡眠位)
    if (MPU6050_WriteReg(mpu, MPU6050_REG_PWR_MGMT_1, 0x00) != 0) {
        return 1;
    }
    HAL_Delay(100); // 等待设备稳定
    
    // 设置采样率分频器
    if (MPU6050_WriteReg(mpu, MPU6050_REG_SMPLRT_DIV, config->sample_rate_div) != 0) {
        return 1;
    }
    
    // 设置数字低通滤波器
    if (MPU6050_WriteReg(mpu, MPU6050_REG_CONFIG, config->dlpf_cfg) != 0) {
        return 1;
    }
    
    // 设置陀螺仪量程
    if (MPU6050_WriteReg(mpu, MPU6050_REG_GYRO_CONFIG, config->gyro_range) != 0) {
        return 1;
    }
    
    // 设置加速度计量程
    if (MPU6050_WriteReg(mpu, MPU6050_REG_ACCEL_CONFIG, config->accel_range) != 0) {
        return 1;
    }
    
    return 0;
}

/**
 * @brief 初始化MPU6050
 * @param mpu MPU6050设备结构体指针
 * @param hi2c IIC句柄指针
 * @return 0:成功, 1:失败
 */
uint8_t MPU6050_Init(MPU6050_t *mpu, I2C_HandleTypeDef *hi2c)
{
    if (mpu == NULL || hi2c == NULL) {
        return 1;
    }

    // 初始化设备结构体
    memset(mpu, 0, sizeof(MPU6050_t));
    mpu->hi2c = hi2c;

    // 设置默认配置
    mpu->config.device_addr = MPU6050_ADDR;
    mpu->config.gyro_range = MPU6050_GYRO_RANGE_250DPS;
    mpu->config.accel_range = MPU6050_ACCEL_RANGE_2G;
    mpu->config.sample_rate_div = 7;    // 1kHz / (1 + 7) = 125Hz
    mpu->config.dlpf_cfg = 0x03;        // 44Hz低通滤波

    // 检查连接
    if (MPU6050_CheckConnection(mpu) != 0) {
        return 1;
    }

    // 配置设备
    if (MPU6050_Configure(mpu, &mpu->config) != 0) {
        return 1;
    }

    mpu->is_initialized = 1;
    return 0;
}

/**
 * @brief 读取MPU6050原始数据
 * @param mpu MPU6050设备结构体指针
 * @return 0:成功, 1:失败
 */
uint8_t MPU6050_ReadRawData(MPU6050_t *mpu)
{
    uint8_t raw_data[14];

    if (mpu == NULL || !mpu->is_initialized) {
        return 1;
    }

    // 从加速度计X轴高字节开始连续读取14个字节
    if (MPU6050_ReadRegs(mpu, MPU6050_REG_ACCEL_XOUT_H, raw_data, 14) != 0) {
        return 1;
    }

    // 解析原始数据
    mpu->raw_data.accel_x_raw = (int16_t)((raw_data[0] << 8) | raw_data[1]);
    mpu->raw_data.accel_y_raw = (int16_t)((raw_data[2] << 8) | raw_data[3]);
    mpu->raw_data.accel_z_raw = (int16_t)((raw_data[4] << 8) | raw_data[5]);
    mpu->raw_data.temp_raw = (int16_t)((raw_data[6] << 8) | raw_data[7]);
    mpu->raw_data.gyro_x_raw = (int16_t)((raw_data[8] << 8) | raw_data[9]);
    mpu->raw_data.gyro_y_raw = (int16_t)((raw_data[10] << 8) | raw_data[11]);
    mpu->raw_data.gyro_z_raw = (int16_t)((raw_data[12] << 8) | raw_data[13]);

    return 0;
}

/**
 * @brief 处理MPU6050数据(原始数据转换为物理量)
 * @param mpu MPU6050设备结构体指针
 */
void MPU6050_ProcessData(MPU6050_t *mpu)
{
    if (mpu == NULL || !mpu->is_initialized) {
        return;
    }

    // 获取量程索引
    uint8_t gyro_range_index = mpu->config.gyro_range >> 3;
    uint8_t accel_range_index = mpu->config.accel_range >> 3;

    // 转换加速度计数据 (g)
    mpu->data.accel_x = (float)mpu->raw_data.accel_x_raw / accel_scale_factors[accel_range_index];
    mpu->data.accel_y = (float)mpu->raw_data.accel_y_raw / accel_scale_factors[accel_range_index];
    mpu->data.accel_z = (float)mpu->raw_data.accel_z_raw / accel_scale_factors[accel_range_index];

    // 转换陀螺仪数据 (°/s)
    mpu->data.gyro_x = (float)mpu->raw_data.gyro_x_raw / gyro_scale_factors[gyro_range_index];
    mpu->data.gyro_y = (float)mpu->raw_data.gyro_y_raw / gyro_scale_factors[gyro_range_index];
    mpu->data.gyro_z = (float)mpu->raw_data.gyro_z_raw / gyro_scale_factors[gyro_range_index];

    // 转换温度数据 (°C)
    mpu->data.temperature = (float)mpu->raw_data.temp_raw / 340.0f + 36.53f;
}
