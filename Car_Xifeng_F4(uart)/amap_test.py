# -*- coding: utf-8 -*-
"""
高德地图GPS测试程序
模拟STM32发送GPS数据到高德地图
"""
import time
import sys

def test_amap_gps():
    """测试高德地图GPS上传"""
    print("=== 高德地图GPS追踪系统测试 ===")
    print("API Key: 946b162f62c7b5af1892cc6fc00b2ea1")
    print("ThingSpeak: 3014831 (LU22ZUP4ZTFK4IY9)")
    print("位置: 衡阳市体育中心")
    print("-" * 50)
    
    # 衡阳市体育中心精确坐标
    lat = 26.885054837223990
    lon = 112.679572502899990
    alt = 68.0
    
    count = 0
    
    try:
        while True:
            count += 1
            
            # 模拟轻微GPS漂移
            lat_drift = lat + (count % 10 - 5) * 0.000001
            lon_drift = lon + (count % 8 - 4) * 0.000001
            
            # 发送高德地图专用格式
            amap_data = "AMAP_GPS:%.6f,%.6f,%.1f" % (lat_drift, lon_drift, alt)
            print(amap_data)
            
            # 每5次显示详细信息
            if count % 5 == 0:
                print("🗺️ GPS已上传到高德地图 #%d: %.6fN, %.6fE" % (count, lat_drift, lon_drift))
                print("🌐 高德地图API Key: 946b162f62c7b5af1892cc6fc00b2ea1")
                print("📊 ThingSpeak频道: 3014831")
                print("-" * 30)
            
            time.sleep(3)  # 3秒间隔
            
    except KeyboardInterrupt:
        print("\n测试停止")
        print("总共发送GPS数据: %d 次" % count)
        print("高德地图测试完成")

if __name__ == "__main__":
    test_amap_gps()
