"""
颜色追踪业务逻辑模块
实现颜色学习和追踪的核心算法
"""
import time
from typing import Optional, List, Tuple, Any
from dataclasses import dataclass

from config import app_config


@dataclass
class ColorThreshold:
    """颜色阈值数据类"""
    l_min: int = 0
    l_max: int = 100
    a_min: int = -128
    a_max: int = 127
    b_min: int = -128
    b_max: int = 127
    
    def to_list(self) -> List[int]:
        """转换为列表格式"""
        return [self.l_min, self.l_max, self.a_min, self.a_max, self.b_min, self.b_max]
    
    def from_list(self, threshold_list: List[int]) -> None:
        """从列表加载阈值"""
        if len(threshold_list) >= 6:
            self.l_min = threshold_list[0]
            self.l_max = threshold_list[1]
            self.a_min = threshold_list[2]
            self.a_max = threshold_list[3]
            self.b_min = threshold_list[4]
            self.b_max = threshold_list[5]
    
    def is_valid(self) -> bool:
        """检查阈值是否有效"""
        return (self.l_min <= self.l_max and 
                self.a_min <= self.a_max and 
                self.b_min <= self.b_max)


@dataclass
class DetectionResult:
    """检测结果数据类"""
    found: bool = False
    center_x: int = 0
    center_y: int = 0
    rect: Tuple[int, int, int, int] = (0, 0, 0, 0)
    area: int = 0
    confidence: float = 0.0


class ColorLearner:
    """颜色学习器"""
    
    def __init__(self):
        self.reset()
    
    def reset(self) -> None:
        """重置学习状态"""
        self._learning_data = []
        self._current_threshold = ColorThreshold()
    
    def add_sample(self, image, roi: Tuple[int, int, int, int]) -> bool:
        """添加学习样本"""
        try:
            hist = image.get_histogram(roi=roi)
            lo = hist.get_percentile(0.1)
            hi = hist.get_percentile(0.9)
            
            sample = {
                'l_min': lo.l_value(),
                'l_max': hi.l_value(),
                'a_min': lo.a_value(),
                'a_max': hi.a_value(),
                'b_min': lo.b_value(),
                'b_max': hi.b_value()
            }
            
            self._learning_data.append(sample)
            return True
        except Exception as e:
            print(f"添加学习样本失败: {e}")
            return False
    
    def compute_threshold(self) -> Optional[ColorThreshold]:
        """计算最终的颜色阈值"""
        if not self._learning_data:
            return None
        
        try:
            # 计算各通道的平均值
            num_samples = len(self._learning_data)
            
            l_min_sum = sum(sample['l_min'] for sample in self._learning_data)
            l_max_sum = sum(sample['l_max'] for sample in self._learning_data)
            a_min_sum = sum(sample['a_min'] for sample in self._learning_data)
            a_max_sum = sum(sample['a_max'] for sample in self._learning_data)
            b_min_sum = sum(sample['b_min'] for sample in self._learning_data)
            b_max_sum = sum(sample['b_max'] for sample in self._learning_data)
            
            threshold = ColorThreshold(
                l_min=l_min_sum // num_samples,
                l_max=l_max_sum // num_samples,
                a_min=a_min_sum // num_samples,
                a_max=a_max_sum // num_samples,
                b_min=b_min_sum // num_samples,
                b_max=b_max_sum // num_samples
            )
            
            if threshold.is_valid():
                self._current_threshold = threshold
                return threshold
            else:
                print("计算的阈值无效")
                return None
                
        except Exception as e:
            print(f"计算颜色阈值失败: {e}")
            return None
    
    def get_current_threshold(self) -> Optional[ColorThreshold]:
        """获取当前阈值"""
        return self._current_threshold if self._current_threshold.is_valid() else None


class ColorDetector:
    """颜色检测器"""
    
    def __init__(self):
        self._threshold: Optional[ColorThreshold] = None
    
    def set_threshold(self, threshold: ColorThreshold) -> None:
        """设置检测阈值"""
        self._threshold = threshold
    
    def detect(self, image) -> DetectionResult:
        """检测颜色目标"""
        result = DetectionResult()
        
        if not self._threshold:
            return result
        
        try:
            # 查找色块
            blobs = image.find_blobs(
                [self._threshold.to_list()],
                pixels_threshold=app_config.detection.pixels_threshold,
                area_threshold=app_config.detection.area_threshold,
                merge=app_config.detection.merge_blobs
            )
            
            if blobs:
                # 找到最大的色块
                largest_blob = max(blobs, key=lambda b: b.pixels())
                
                result.found = True
                result.center_x = largest_blob.cx()
                result.center_y = largest_blob.cy()
                result.rect = largest_blob.rect()
                result.area = largest_blob.pixels()
                
                # 计算置信度（基于面积大小）
                max_area = app_config.display.width * app_config.display.height
                result.confidence = min(result.area / max_area, 1.0)
                
        except Exception as e:
            print(f"颜色检测失败: {e}")
        
        return result


class ColorTracker:
    """颜色追踪器 - 整合学习和检测功能"""
    
    def __init__(self):
        self.learner = ColorLearner()
        self.detector = ColorDetector()
        self._is_learning = False
        self._learning_progress = 0
    
    def start_learning(self) -> None:
        """开始颜色学习"""
        self.learner.reset()
        self._is_learning = True
        self._learning_progress = 0
        print("开始颜色学习...")
    
    def add_learning_frame(self, image, roi: Tuple[int, int, int, int]) -> bool:
        """添加学习帧"""
        if not self._is_learning:
            return False
        
        success = self.learner.add_sample(image, roi)
        if success:
            self._learning_progress += 1
            
            # 检查是否完成学习
            if self._learning_progress >= app_config.learning.learning_frames:
                self._finish_learning()
        
        return success
    
    def _finish_learning(self) -> None:
        """完成学习过程"""
        threshold = self.learner.compute_threshold()
        if threshold:
            self.detector.set_threshold(threshold)
            print(f"颜色学习完成: {threshold.to_list()}")
        else:
            print("颜色学习失败")
        
        self._is_learning = False
        self._learning_progress = 0
    
    def detect_color(self, image) -> DetectionResult:
        """检测颜色目标"""
        return self.detector.detect(image)
    
    def is_learning(self) -> bool:
        """检查是否正在学习"""
        return self._is_learning
    
    def get_learning_progress(self) -> Tuple[int, int]:
        """获取学习进度"""
        return (self._learning_progress, app_config.learning.learning_frames)
    
    def has_learned_color(self) -> bool:
        """检查是否已学习颜色"""
        return self.detector._threshold is not None
    
    def get_current_threshold(self) -> Optional[ColorThreshold]:
        """获取当前颜色阈值"""
        return self.detector._threshold
    
    def set_threshold(self, threshold: ColorThreshold) -> None:
        """手动设置颜色阈值"""
        self.detector.set_threshold(threshold)
        self._is_learning = False
        self._learning_progress = 0


class ImageRenderer:
    """图像渲染器 - 负责在图像上绘制各种元素"""
    
    @staticmethod
    def draw_learning_roi(image, roi: Tuple[int, int, int, int]) -> None:
        """绘制学习区域"""
        image.draw_rectangle(roi, 
                           color=app_config.ui.learning_roi_color, 
                           thickness=app_config.ui.line_thickness)
    
    @staticmethod
    def draw_learning_progress(image, roi: Tuple[int, int, int, int]) -> None:
        """绘制学习进度"""
        image.draw_rectangle(roi, 
                           color=app_config.ui.learning_progress_color, 
                           thickness=app_config.ui.line_thickness)
    
    @staticmethod
    def draw_detection_result(image, result: DetectionResult) -> None:
        """绘制检测结果"""
        if result.found:
            # 绘制边界框
            image.draw_rectangle(result.rect, 
                               color=app_config.ui.detection_color, 
                               thickness=app_config.ui.line_thickness)
            
            # 绘制中心点
            image.draw_cross(result.center_x, result.center_y, 
                           color=app_config.ui.detection_color)
    
    @staticmethod
    def draw_status_text(image, text: str, x: int = 10, y: int = 10) -> None:
        """绘制状态文本"""
        image.draw_string(x, y, text, 
                         color=app_config.ui.text_color, 
                         scale=app_config.ui.text_scale)
