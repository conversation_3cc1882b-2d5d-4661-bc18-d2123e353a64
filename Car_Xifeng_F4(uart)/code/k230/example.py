"""
K230颜色追踪应用 - 主入口文件
支持颜色学习和实时追踪功能

版本: 2.0 (模块化重构版)
特性:
- 模块化设计，低耦合架构
- 硬件抽象层，便于移植和测试
- 配置管理系统，支持参数化配置
- 完善的错误处理和日志记录
- 可扩展的业务逻辑框架

使用方法:
1. 确保在K230开发板环境中运行
2. 运行: python example.py
3. 按照屏幕提示操作
"""

# 导入新的模块化组件
from app_controller import create_app


def main():
    """
    主函数 - 使用新的模块化架构
    这个函数现在只是一个简单的入口点，所有复杂的逻辑都被封装在模块中
    """
    print("=== K230颜色追踪应用 ===")
    print("版本: 2.0 (模块化重构版)")
    print("功能: 颜色学习和实时追踪")
    print("=" * 30)

    # 创建并运行应用程序
    app = create_app()

    if app.initialize():
        app.run()
    else:
        print("应用程序初始化失败，请检查硬件连接")
        app.cleanup()


if __name__ == "__main__":
    main()

# 旧代码已被模块化架构替代
# 所有功能现在都通过 app_controller 模块实现
