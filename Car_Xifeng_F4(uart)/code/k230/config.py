"""
K230颜色追踪应用配置管理模块
提供统一的配置管理和参数设置
"""
from typing import Dict, Any, <PERSON><PERSON>
from dataclasses import dataclass


@dataclass
class DisplayConfig:
    """显示配置"""
    width: int = 640
    height: int = 480
    fps: int = 100
    display_type: str = "VIRT"  # 虚拟显示


@dataclass
class SensorConfig:
    """传感器配置"""
    width: int = 640
    height: int = 480
    pixel_format: str = "RGB565"


@dataclass
class HardwareConfig:
    """硬件引脚配置"""
    led_pin: int = 52
    key_pin: int = 21
    led_gpio: str = "GPIO52"
    key_gpio: str = "GPIO21"


@dataclass
class LearningConfig:
    """颜色学习配置"""
    roi_size: int = 100  # 学习区域大小
    learning_frames: int = 30  # 学习帧数
    debounce_delay: int = 10  # 按键消抖延时(ms)
    initial_threshold: Tuple[int, ...] = (50, 50, 0, 0, 0, 0)


@dataclass
class DetectionConfig:
    """检测配置"""
    pixels_threshold: int = 200
    area_threshold: int = 200
    merge_blobs: bool = True


@dataclass
class UIConfig:
    """用户界面配置"""
    learning_roi_color: Tuple[int, int, int] = (255, 0, 0)  # 红色
    learning_progress_color: Tuple[int, int, int] = (0, 255, 0)  # 绿色
    detection_color: Tuple[int, int, int] = (0, 255, 0)  # 绿色
    text_color: Tuple[int, int, int] = (255, 0, 0)  # 红色
    text_scale: int = 2
    line_thickness: int = 2


class AppConfig:
    """应用程序主配置类"""
    
    def __init__(self):
        self.display = DisplayConfig()
        self.sensor = SensorConfig()
        self.hardware = HardwareConfig()
        self.learning = LearningConfig()
        self.detection = DetectionConfig()
        self.ui = UIConfig()
    
    def get_learning_roi(self) -> Tuple[int, int, int, int]:
        """获取学习区域ROI坐标"""
        x = (self.display.width // 2) - (self.learning.roi_size // 2)
        y = (self.display.height // 2) - (self.learning.roi_size // 2)
        return (x, y, self.learning.roi_size, self.learning.roi_size)
    
    def validate_config(self) -> bool:
        """验证配置参数的有效性"""
        try:
            # 验证显示配置
            assert self.display.width > 0 and self.display.height > 0
            assert self.display.fps > 0
            
            # 验证传感器配置
            assert self.sensor.width > 0 and self.sensor.height > 0
            
            # 验证硬件配置
            assert self.hardware.led_pin >= 0 and self.hardware.key_pin >= 0
            
            # 验证学习配置
            assert self.learning.roi_size > 0
            assert self.learning.learning_frames > 0
            assert self.learning.debounce_delay >= 0
            
            # 验证检测配置
            assert self.detection.pixels_threshold >= 0
            assert self.detection.area_threshold >= 0
            
            return True
        except AssertionError:
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典格式"""
        return {
            'display': self.display.__dict__,
            'sensor': self.sensor.__dict__,
            'hardware': self.hardware.__dict__,
            'learning': self.learning.__dict__,
            'detection': self.detection.__dict__,
            'ui': self.ui.__dict__
        }
    
    def from_dict(self, config_dict: Dict[str, Any]) -> None:
        """从字典加载配置"""
        if 'display' in config_dict:
            for key, value in config_dict['display'].items():
                if hasattr(self.display, key):
                    setattr(self.display, key, value)
        
        if 'sensor' in config_dict:
            for key, value in config_dict['sensor'].items():
                if hasattr(self.sensor, key):
                    setattr(self.sensor, key, value)
        
        if 'hardware' in config_dict:
            for key, value in config_dict['hardware'].items():
                if hasattr(self.hardware, key):
                    setattr(self.hardware, key, value)
        
        if 'learning' in config_dict:
            for key, value in config_dict['learning'].items():
                if hasattr(self.learning, key):
                    setattr(self.learning, key, value)
        
        if 'detection' in config_dict:
            for key, value in config_dict['detection'].items():
                if hasattr(self.detection, key):
                    setattr(self.detection, key, value)
        
        if 'ui' in config_dict:
            for key, value in config_dict['ui'].items():
                if hasattr(self.ui, key):
                    setattr(self.ui, key, value)


# 全局配置实例
app_config = AppConfig()
