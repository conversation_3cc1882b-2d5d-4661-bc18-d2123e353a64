"""
K230硬件抽象层
提供硬件设备的统一接口，降低硬件依赖的耦合度
"""
import time
from typing import Optional, Protocol, runtime_checkable
from abc import ABC, abstractmethod

from config import app_config

# 尝试导入K230特定模块
try:
    from media.sensor import Sensor
    from media.display import Display
    from media.media import MediaManager
    from machine import Pin, FPIOA
    K230_AVAILABLE = True
except ImportError:
    K230_AVAILABLE = False
    # 创建模拟类用于开发和测试
    class Sensor:
        def __init__(self, **kwargs): pass
        def reset(self): pass
        def set_framesize(self, **kwargs): pass
        def set_pixformat(self, format): pass
        def run(self): pass
        def stop(self): pass
        def snapshot(self): return MockImage()
    
    class Display:
        VIRT = "VIRT"
        @staticmethod
        def init(*args, **kwargs): pass
        @staticmethod
        def show_image(img): pass
        @staticmethod
        def deinit(): pass
    
    class MediaManager:
        @staticmethod
        def init(): pass
        @staticmethod
        def deinit(): pass
    
    class Pin:
        OUT = "OUT"
        IN = "IN"
        PULL_UP = "PULL_UP"
        def __init__(self, pin, mode, pull=None): pass
        def value(self, val=None): return 1 if val is None else None
    
    class FPIOA:
        GPIO52 = "GPIO52"
        GPIO21 = "GPIO21"
        def set_function(self, pin, func): pass
    
    class MockImage:
        def draw_rectangle(self, *args, **kwargs): pass
        def draw_cross(self, *args, **kwargs): pass
        def draw_string(self, *args, **kwargs): pass
        def find_blobs(self, *args, **kwargs): return []
        def get_histogram(self, *args, **kwargs): return MockHistogram()
    
    class MockHistogram:
        def get_percentile(self, val): return MockPercentile()
    
    class MockPercentile:
        def l_value(self): return 50
        def a_value(self): return 0
        def b_value(self): return 0


@runtime_checkable
class HardwareInterface(Protocol):
    """硬件接口协议"""
    
    def initialize(self) -> bool:
        """初始化硬件"""
        ...
    
    def cleanup(self) -> None:
        """清理硬件资源"""
        ...


class LEDController:
    """LED控制器"""
    
    def __init__(self):
        self._led: Optional[Pin] = None
        self._initialized = False
    
    def initialize(self) -> bool:
        """初始化LED"""
        try:
            if K230_AVAILABLE:
                fpioa = FPIOA()
                fpioa.set_function(app_config.hardware.led_pin, 
                                 getattr(FPIOA, app_config.hardware.led_gpio))
            
            self._led = Pin(app_config.hardware.led_pin, Pin.OUT)
            self._initialized = True
            self.turn_off()  # 初始状态为关闭
            return True
        except Exception as e:
            print(f"LED初始化失败: {e}")
            return False
    
    def turn_on(self) -> None:
        """点亮LED"""
        if self._led and self._initialized:
            self._led.value(1)
    
    def turn_off(self) -> None:
        """熄灭LED"""
        if self._led and self._initialized:
            self._led.value(0)
    
    def toggle(self) -> None:
        """切换LED状态"""
        if self._led and self._initialized:
            current = self._led.value()
            self._led.value(1 - current)
    
    def cleanup(self) -> None:
        """清理资源"""
        self.turn_off()
        self._initialized = False


class KeyController:
    """按键控制器"""
    
    def __init__(self):
        self._key: Optional[Pin] = None
        self._initialized = False
    
    def initialize(self) -> bool:
        """初始化按键"""
        try:
            if K230_AVAILABLE:
                fpioa = FPIOA()
                fpioa.set_function(app_config.hardware.key_pin, 
                                 getattr(FPIOA, app_config.hardware.key_gpio))
            
            self._key = Pin(app_config.hardware.key_pin, Pin.IN, Pin.PULL_UP)
            self._initialized = True
            return True
        except Exception as e:
            print(f"按键初始化失败: {e}")
            return False
    
    def is_pressed(self) -> bool:
        """检查按键是否被按下"""
        if self._key and self._initialized:
            return self._key.value() == 0
        return False
    
    def wait_for_press(self) -> bool:
        """等待按键按下（带消抖）"""
        if not self._initialized:
            return False
        
        if self.is_pressed():
            time.sleep_ms(app_config.learning.debounce_delay)
            if self.is_pressed():
                return True
        return False
    
    def wait_for_release(self) -> None:
        """等待按键释放"""
        if not self._initialized:
            return
        
        while self.is_pressed():
            time.sleep_ms(1)
    
    def cleanup(self) -> None:
        """清理资源"""
        self._initialized = False


class CameraController:
    """摄像头控制器"""
    
    def __init__(self):
        self._sensor: Optional[Sensor] = None
        self._initialized = False
    
    def initialize(self) -> bool:
        """初始化摄像头"""
        try:
            self._sensor = Sensor(width=app_config.sensor.width, 
                                height=app_config.sensor.height)
            self._sensor.reset()
            self._sensor.set_framesize(width=app_config.sensor.width, 
                                     height=app_config.sensor.height)
            
            # 设置像素格式
            if hasattr(Sensor, app_config.sensor.pixel_format):
                self._sensor.set_pixformat(getattr(Sensor, app_config.sensor.pixel_format))
            
            self._sensor.run()
            self._initialized = True
            return True
        except Exception as e:
            print(f"摄像头初始化失败: {e}")
            return False
    
    def capture_frame(self):
        """捕获一帧图像"""
        if self._sensor and self._initialized:
            return self._sensor.snapshot()
        return None
    
    def cleanup(self) -> None:
        """清理资源"""
        if self._sensor and self._initialized:
            self._sensor.stop()
        self._initialized = False


class DisplayController:
    """显示控制器"""
    
    def __init__(self):
        self._initialized = False
    
    def initialize(self) -> bool:
        """初始化显示"""
        try:
            display_type = getattr(Display, app_config.display.display_type, Display.VIRT)
            Display.init(display_type, 
                        width=app_config.display.width,
                        height=app_config.display.height, 
                        fps=app_config.display.fps)
            self._initialized = True
            return True
        except Exception as e:
            print(f"显示初始化失败: {e}")
            return False
    
    def show_image(self, image) -> None:
        """显示图像"""
        if self._initialized:
            Display.show_image(image)
    
    def cleanup(self) -> None:
        """清理资源"""
        if self._initialized:
            Display.deinit()
        self._initialized = False


class HardwareManager:
    """硬件管理器 - 统一管理所有硬件组件"""
    
    def __init__(self):
        self.led = LEDController()
        self.key = KeyController()
        self.camera = CameraController()
        self.display = DisplayController()
        self._media_initialized = False
    
    def initialize_all(self) -> bool:
        """初始化所有硬件组件"""
        success = True
        
        # 初始化媒体管理器
        try:
            MediaManager.init()
            self._media_initialized = True
        except Exception as e:
            print(f"媒体管理器初始化失败: {e}")
            success = False
        
        # 初始化各个组件
        if not self.led.initialize():
            print("LED初始化失败")
            success = False
        
        if not self.key.initialize():
            print("按键初始化失败")
            success = False
        
        if not self.camera.initialize():
            print("摄像头初始化失败")
            success = False
        
        if not self.display.initialize():
            print("显示初始化失败")
            success = False
        
        return success
    
    def cleanup_all(self) -> None:
        """清理所有硬件资源"""
        self.led.cleanup()
        self.key.cleanup()
        self.camera.cleanup()
        self.display.cleanup()
        
        if self._media_initialized:
            try:
                MediaManager.deinit()
            except Exception as e:
                print(f"媒体管理器清理失败: {e}")
            self._media_initialized = False
