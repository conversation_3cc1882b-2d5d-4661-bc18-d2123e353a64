"""
应用程序主控制器
协调硬件管理、颜色追踪和用户交互
"""
import time
import gc
import os
from typing import Optional
from enum import Enum

from config import app_config
from hardware import HardwareManager
from color_tracker import ColorTracker, ImageRenderer


class AppState(Enum):
    """应用程序状态"""
    INITIALIZING = "initializing"
    WAITING_FOR_LEARNING = "waiting_for_learning"
    LEARNING = "learning"
    TRACKING = "tracking"
    ERROR = "error"


class ColorTrackingApp:
    """颜色追踪应用程序主控制器"""
    
    def __init__(self):
        self.hardware = HardwareManager()
        self.color_tracker = ColorTracker()
        self.renderer = ImageRenderer()
        self.state = AppState.INITIALIZING
        self._running = False
        self._fps_counter = None
        
        # 初始化FPS计数器
        try:
            self._fps_counter = time.clock()
        except:
            self._fps_counter = None
    
    def initialize(self) -> bool:
        """初始化应用程序"""
        print("正在初始化颜色追踪应用...")
        
        # 验证配置
        if not app_config.validate_config():
            print("错误: 配置参数无效")
            self.state = AppState.ERROR
            return False
        
        # 初始化硬件
        if not self.hardware.initialize_all():
            print("错误: 硬件初始化失败")
            self.state = AppState.ERROR
            return False
        
        print("应用程序初始化成功")
        self.state = AppState.WAITING_FOR_LEARNING
        return True
    
    def run(self) -> None:
        """运行应用程序主循环"""
        if self.state == AppState.ERROR:
            print("应用程序处于错误状态，无法运行")
            return
        
        self._running = True
        print("颜色追踪应用程序开始运行")
        print("请将要追踪的颜色放在中心方框内，然后按下按键开始学习")
        
        try:
            while self._running:
                self._update_fps()
                self._handle_exit_point()
                self._process_frame()
                self._handle_user_input()
                self._cleanup_memory()
                
        except KeyboardInterrupt:
            print("用户停止应用程序")
        except Exception as e:
            print(f"应用程序运行时发生异常: {e}")
            self.state = AppState.ERROR
        finally:
            self.cleanup()
    
    def stop(self) -> None:
        """停止应用程序"""
        self._running = False
    
    def cleanup(self) -> None:
        """清理应用程序资源"""
        print("正在清理应用程序资源...")
        self.hardware.cleanup_all()
        
        # 确保LED熄灭
        try:
            self.hardware.led.turn_off()
        except:
            pass
        
        # 启用睡眠模式
        try:
            os.exitpoint(os.EXITPOINT_ENABLE_SLEEP)
            time.sleep_ms(100)
        except:
            pass
        
        print("应用程序清理完成")
    
    def _update_fps(self) -> None:
        """更新FPS计数"""
        if self._fps_counter:
            try:
                self._fps_counter.tick()
            except:
                pass
    
    def _handle_exit_point(self) -> None:
        """处理退出点"""
        try:
            os.exitpoint()
        except:
            pass
    
    def _process_frame(self) -> None:
        """处理单帧图像"""
        # 捕获图像
        image = self.hardware.camera.capture_frame()
        if image is None:
            return
        
        # 获取学习区域
        learning_roi = app_config.get_learning_roi()
        
        # 根据当前状态处理图像
        if self.state == AppState.WAITING_FOR_LEARNING:
            self._process_waiting_state(image, learning_roi)
        elif self.state == AppState.LEARNING:
            self._process_learning_state(image, learning_roi)
        elif self.state == AppState.TRACKING:
            self._process_tracking_state(image, learning_roi)
        
        # 显示图像
        self.hardware.display.show_image(image)
    
    def _process_waiting_state(self, image, roi) -> None:
        """处理等待学习状态"""
        # 绘制学习区域
        self.renderer.draw_learning_roi(image, roi)
        
        # 显示状态文本
        status_text = "按键开始学习颜色"
        self.renderer.draw_status_text(image, status_text)
    
    def _process_learning_state(self, image, roi) -> None:
        """处理学习状态"""
        # 添加学习帧
        self.color_tracker.add_learning_frame(image, roi)
        
        # 绘制学习进度
        self.renderer.draw_learning_progress(image, roi)
        
        # 显示进度文本
        current, total = self.color_tracker.get_learning_progress()
        status_text = f"学习中... {current}/{total}"
        self.renderer.draw_status_text(image, status_text)
        
        # 检查学习是否完成
        if not self.color_tracker.is_learning():
            if self.color_tracker.has_learned_color():
                self.state = AppState.TRACKING
                self.hardware.led.turn_off()
                print("颜色学习完成，开始追踪")
            else:
                self.state = AppState.WAITING_FOR_LEARNING
                self.hardware.led.turn_off()
                print("颜色学习失败，请重试")
    
    def _process_tracking_state(self, image, roi) -> None:
        """处理追踪状态"""
        # 绘制学习区域（参考）
        self.renderer.draw_learning_roi(image, roi)
        
        # 检测颜色目标
        detection_result = self.color_tracker.detect_color(image)
        
        # 绘制检测结果
        self.renderer.draw_detection_result(image, detection_result)
        
        # 显示状态文本
        if detection_result.found:
            status_text = f"追踪中 - 置信度: {detection_result.confidence:.2f}"
        else:
            status_text = "追踪中 - 未发现目标"
        
        self.renderer.draw_status_text(image, status_text)
    
    def _handle_user_input(self) -> None:
        """处理用户输入"""
        if self.hardware.key.wait_for_press():
            self._handle_key_press()
            self.hardware.key.wait_for_release()
    
    def _handle_key_press(self) -> None:
        """处理按键按下事件"""
        if self.state == AppState.WAITING_FOR_LEARNING:
            # 开始学习
            self.hardware.led.turn_on()
            self.color_tracker.start_learning()
            self.state = AppState.LEARNING
            print("开始学习颜色...")
            
        elif self.state == AppState.TRACKING:
            # 重新学习
            self.hardware.led.turn_on()
            self.color_tracker.start_learning()
            self.state = AppState.LEARNING
            print("重新学习颜色...")
    
    def _cleanup_memory(self) -> None:
        """清理内存"""
        try:
            gc.collect()
        except:
            pass
    
    def get_current_state(self) -> AppState:
        """获取当前应用程序状态"""
        return self.state
    
    def get_color_threshold(self) -> Optional[str]:
        """获取当前颜色阈值（用于调试）"""
        threshold = self.color_tracker.get_current_threshold()
        if threshold:
            return str(threshold.to_list())
        return None


def create_app() -> ColorTrackingApp:
    """创建应用程序实例"""
    return ColorTrackingApp()


def main():
    """主函数"""
    app = create_app()
    
    if app.initialize():
        app.run()
    else:
        print("应用程序初始化失败")
        app.cleanup()


if __name__ == "__main__":
    main()
