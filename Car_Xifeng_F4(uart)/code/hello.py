"""
OpenCV图像处理脚本 - 主入口文件
使用模块化架构重构的图像处理应用

版本: 2.0 (模块化重构版)
特性:
- 模块化设计，低耦合架构
- 配置管理系统，支持参数化配置
- 完善的错误处理和日志记录
- 支持批量处理和交互模式
- 可扩展的图像处理功能

安装依赖:
pip install opencv-python numpy

使用方法:
1. 处理单个图像: python hello.py image.png
2. 交互模式: python hello.py
3. 使用完整应用: python opencv_app.py
"""

from opencv_app import create_app


def main():
    """
    主函数 - 使用新的模块化架构
    保持向后兼容，同时提供更强大的功能
    """
    print("=== OpenCV图像处理应用 ===")
    print("版本: 2.0 (模块化重构版)")
    print("功能: 图像加载、处理、分析和显示")
    print("=" * 40)

    # 创建应用程序实例
    app = create_app()

    # 默认处理image.png文件
    default_image = 'image.png'

    print(f"\n正在处理默认图像: {default_image}")
    success = app.process_single_image(
        image_path=default_image,
        grayscale=True,  # 保持原有的灰度模式
        show_analysis=True,
        save_processed=False
    )

    if not success:
        print(f"\n处理 {default_image} 失败")
        print("您可以:")
        print("1. 确保图像文件存在")
        print("2. 运行交互模式选择其他图像")

        # 提供交互模式选项
        choice = input("\n是否进入交互模式? (y/n): ").strip().lower()
        if choice == 'y':
            app.run_interactive_mode()


if __name__ == "__main__":
    main()