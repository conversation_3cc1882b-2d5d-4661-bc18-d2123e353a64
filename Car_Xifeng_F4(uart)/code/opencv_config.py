"""
OpenCV图像处理配置管理模块
提供统一的配置管理和参数设置
"""
import os
import json
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class ImageConfig:
    """图像处理配置"""
    default_image_path: str = "image.png"
    supported_formats: List[str] = None
    max_image_size: Tuple[int, int] = (4096, 4096)
    default_grayscale: bool = True
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif']


@dataclass
class DisplayConfig:
    """显示配置"""
    window_name: str = "OpenCV Image Viewer"
    window_flags: int = 1  # cv2.WINDOW_AUTOSIZE
    wait_key_timeout: int = 0  # 0表示无限等待
    auto_resize: bool = True
    max_display_size: Tuple[int, int] = (1920, 1080)


@dataclass
class ProcessingConfig:
    """图像处理配置"""
    enable_histogram_equalization: bool = False
    enable_noise_reduction: bool = False
    gaussian_blur_kernel: Tuple[int, int] = (5, 5)
    gaussian_blur_sigma: float = 1.0


@dataclass
class LoggingConfig:
    """日志配置"""
    enable_logging: bool = True
    log_level: str = "INFO"
    log_to_file: bool = False
    log_file_path: str = "opencv_app.log"
    max_log_size: int = 10 * 1024 * 1024  # 10MB


class OpenCVConfig:
    """OpenCV应用程序主配置类"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.image = ImageConfig()
        self.display = DisplayConfig()
        self.processing = ProcessingConfig()
        self.logging = LoggingConfig()
        
        self.config_file = config_file or "opencv_config.json"
        self.load_config()
    
    def validate_config(self) -> bool:
        """验证配置参数的有效性"""
        try:
            # 验证图像配置
            if self.image.max_image_size[0] <= 0 or self.image.max_image_size[1] <= 0:
                return False
            
            # 验证显示配置
            if self.display.max_display_size[0] <= 0 or self.display.max_display_size[1] <= 0:
                return False
            
            if self.display.wait_key_timeout < 0:
                return False
            
            # 验证处理配置
            if (self.processing.gaussian_blur_kernel[0] <= 0 or 
                self.processing.gaussian_blur_kernel[1] <= 0):
                return False
            
            if self.processing.gaussian_blur_sigma <= 0:
                return False
            
            # 验证日志配置
            if self.logging.max_log_size <= 0:
                return False
            
            return True
        except Exception:
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典格式"""
        return {
            'image': asdict(self.image),
            'display': asdict(self.display),
            'processing': asdict(self.processing),
            'logging': asdict(self.logging)
        }
    
    def from_dict(self, config_dict: Dict[str, Any]) -> None:
        """从字典加载配置"""
        if 'image' in config_dict:
            for key, value in config_dict['image'].items():
                if hasattr(self.image, key):
                    setattr(self.image, key, value)
        
        if 'display' in config_dict:
            for key, value in config_dict['display'].items():
                if hasattr(self.display, key):
                    setattr(self.display, key, value)
        
        if 'processing' in config_dict:
            for key, value in config_dict['processing'].items():
                if hasattr(self.processing, key):
                    setattr(self.processing, key, value)
        
        if 'logging' in config_dict:
            for key, value in config_dict['logging'].items():
                if hasattr(self.logging, key):
                    setattr(self.logging, key, value)
    
    def save_config(self) -> bool:
        """保存配置到文件"""
        try:
            config_dict = self.to_dict()
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
    
    def load_config(self) -> bool:
        """从文件加载配置"""
        if not os.path.exists(self.config_file):
            # 如果配置文件不存在，创建默认配置
            return self.save_config()
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
            self.from_dict(config_dict)
            return True
        except Exception as e:
            print(f"加载配置失败: {e}")
            return False
    
    def get_supported_image_path(self, image_path: str) -> Optional[str]:
        """检查并返回支持的图像路径"""
        path = Path(image_path)
        
        # 检查文件是否存在
        if not path.exists():
            return None
        
        # 检查文件扩展名
        if path.suffix.lower() not in self.image.supported_formats:
            return None
        
        return str(path.absolute())
    
    def get_display_size(self, image_shape: Tuple[int, int]) -> Tuple[int, int]:
        """根据图像大小和配置计算显示大小"""
        if not self.display.auto_resize:
            return image_shape
        
        height, width = image_shape[:2]
        max_width, max_height = self.display.max_display_size
        
        # 计算缩放比例
        scale_w = max_width / width
        scale_h = max_height / height
        scale = min(scale_w, scale_h, 1.0)  # 不放大，只缩小
        
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        return (new_height, new_width)


# 全局配置实例
opencv_config = OpenCVConfig()
