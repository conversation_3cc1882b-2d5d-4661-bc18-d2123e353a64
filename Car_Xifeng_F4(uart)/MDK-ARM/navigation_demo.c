/**
 * 导航演示程序
 * 展示完整的触摸翻页导航功能
 */

#include "navigation_types.h"
#include "navigation_paging.h"
#include "touch_driver.h"
#include "lcd_display_hal.h"

/**
 * @brief 导航演示主函数
 */
void NavigationDemo_Main(void)
{
    static uint8_t demo_initialized = 0;
    static uint32_t last_update = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 初始化演示
    if (!demo_initialized) {
        NavPaging_Init();
        demo_initialized = 1;
        
        // 显示欢迎信息
        LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);
        LCD_ShowString(10, 10, (const uint8_t*)"Touch Navigation Demo", WHITE, BLACK, 16, 0);
        LCD_ShowString(10, 40, (const uint8_t*)"15 steps, 4 pages", CYAN, BLACK, 12, 0);
        LCD_ShowString(10, 60, (const uint8_t*)"Touch to navigate", YELL<PERSON>, BLACK, 12, 0);
        HAL_Delay(2000);
    }
    
    // 处理触摸事件
    TouchState_t touch_state;
    if (Touch_Scan(&touch_state)) {
        if (touch_state.event != TOUCH_EVENT_NONE) {
            NavPaging_HandleTouch(touch_state.event, touch_state.current.x, touch_state.current.y);
        }
    }
    
    // 更新显示（每200ms）
    if (current_time - last_update >= 200) {
        last_update = current_time;
        NavPaging_Display();
    }
}

/**
 * @brief 显示触摸帮助信息
 */
void NavigationDemo_ShowHelp(void)
{
    LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);
    
    LCD_ShowString(10, 10, (const uint8_t*)"Touch Navigation Help", WHITE, BLACK, 16, 0);
    LCD_ShowString(10, 40, (const uint8_t*)"Touch Controls:", YELLOW, BLACK, 12, 0);
    LCD_ShowString(10, 60, (const uint8_t*)"- Tap NEXT> for next page", GREEN, BLACK, 10, 0);
    LCD_ShowString(10, 75, (const uint8_t*)"- Tap <PREV for prev page", GREEN, BLACK, 10, 0);
    LCD_ShowString(10, 90, (const uint8_t*)"- Tap HOME to go to page 1", GREEN, BLACK, 10, 0);
    
    LCD_ShowString(10, 115, (const uint8_t*)"Gestures:", YELLOW, BLACK, 12, 0);
    LCD_ShowString(10, 135, (const uint8_t*)"- Swipe left for next page", CYAN, BLACK, 10, 0);
    LCD_ShowString(10, 150, (const uint8_t*)"- Swipe right for prev page", CYAN, BLACK, 10, 0);
    
    LCD_ShowString(10, 175, (const uint8_t*)"Route Info:", YELLOW, BLACK, 12, 0);
    LCD_ShowString(10, 195, (const uint8_t*)"- Total: 15 steps", WHITE, BLACK, 10, 0);
    LCD_ShowString(10, 210, (const uint8_t*)"- Distance: 3.78km", WHITE, BLACK, 10, 0);
    LCD_ShowString(10, 225, (const uint8_t*)"- Time: 6 minutes", WHITE, BLACK, 10, 0);
    
    LCD_ShowString(10, 250, (const uint8_t*)"Touch anywhere to start", RED, BLACK, 12, 0);
}

/**
 * @brief 显示当前导航状态
 */
void NavigationDemo_ShowStatus(void)
{
    char buffer[64];
    uint16_t y_pos = LCD_H - 80;
    
    // 显示当前状态
    snprintf(buffer, sizeof(buffer), "Current: Step %d/%d", 
             g_nav_paging.current_step, g_nav_paging.total_steps);
    LCD_ShowString(10, y_pos, (const uint8_t*)buffer, YELLOW, BLACK, 12, 0);
    
    snprintf(buffer, sizeof(buffer), "Page: %d/%d", 
             g_nav_paging.current_page + 1, g_nav_paging.total_pages);
    LCD_ShowString(10, y_pos + 15, (const uint8_t*)buffer, CYAN, BLACK, 12, 0);
    
    // 显示触摸状态
    TouchState_t touch_state;
    if (Touch_Scan(&touch_state)) {
        if (touch_state.current.valid) {
            snprintf(buffer, sizeof(buffer), "Touch: %d,%d", 
                     touch_state.current.x, touch_state.current.y);
            LCD_ShowString(10, y_pos + 30, (const uint8_t*)buffer, GREEN, BLACK, 10, 0);
        }
    }
}
