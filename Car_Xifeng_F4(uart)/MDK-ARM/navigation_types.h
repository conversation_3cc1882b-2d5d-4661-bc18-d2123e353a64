/**
 * 导航系统类型定义
 * 避免循环依赖的独立类型定义文件
 */

#ifndef __NAVIGATION_TYPES_H__
#define __NAVIGATION_TYPES_H__

#include <stdint.h>

// 触摸事件类型（保留用于兼容性，但不使用）
typedef enum {
    TOUCH_EVENT_NONE = 0
} TouchEvent_t;

// 导航步骤结构体
typedef struct {
    uint8_t step_num;           // 步骤编号
    char instruction[64];       // 导航指令
    char distance[16];          // 距离信息
    char road_name[32];         // 道路名称
    uint8_t direction;          // 方向类型：0-直行，1-左转，2-右转，3-到达
    uint8_t completed;          // 是否已完成
} NavigationStep_t;

// 触摸相关结构体已删除 - 屏幕不支持触摸

// 分页显示状态
typedef struct {
    NavigationStep_t* steps;    // 导航步骤数组
    uint8_t total_steps;        // 总步骤数
    uint8_t current_page;       // 当前页码
    uint8_t steps_per_page;     // 每页显示的步骤数
    uint8_t total_pages;        // 总页数
    uint8_t current_step;       // 当前执行的步骤
    float total_distance;       // 总距离(km)
    uint16_t estimated_time;    // 预计时间(分钟)
} NavigationPaging_t;

// 页面类型
typedef enum {
    NAV_PAGE_OVERVIEW = 0,      // 概览页
    NAV_PAGE_STEPS,             // 步骤页
    NAV_PAGE_SETTINGS           // 设置页
} NavigationPageType_t;

// 触摸相关定义已删除 - 屏幕不支持触摸

// 自动翻页配置
#define AUTO_FLIP_INTERVAL     10000  // 自动翻页间隔(ms) - 10秒

// 检测器数据结构
typedef struct {
    uint8_t step_num;       // 步骤编号
    char instruction[128];  // 导航指令
    char distance[32];      // 距离信息
    char road_name[64];     // 道路名称
    uint8_t direction;      // 方向（0:直行, 1:左转, 2:右转, 3:到达）
    uint8_t completed;      // 是否已完成
} DetectorStep_t;

typedef struct {
    float total_distance;           // 总距离(km)
    int estimated_time;             // 预计时间(min)
    int step_count;                 // 步骤数量
    DetectorStep_t steps[20];       // 导航步骤
    uint8_t current_step;           // 当前步骤
    char destination_name[64];      // 目的地名称
} NavigationDetectorData_t;

#endif /* __NAVIGATION_TYPES_H__ */
