<html>
<body>
<pre>
<h1>�Vision Build Log</h1>
<h2>Tool Versions:</h2>
IDE-Version: ��Vision V5.41.0.0
Copyright (C) 2024 ARM Ltd and ARM Germany GmbH. All rights reserved.
License Information: 666 ASUS, 666, LIC=32B1Q-NAAN7-EVX1R-K2M1U-E0BQN-Q6NAJ
 
Tool Versions:
Toolchain:       MDK-ARM Plus  Version: 5.41.0.0
Toolchain Path:  C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCLANG\Bin
C Compiler:      ArmClang.exe V6.22
Assembler:       Armasm.exe V6.22
Linker/Locator:  ArmLink.exe V6.22
Library Manager: ArmAr.exe V6.22
Hex Converter:   FromElf.exe V6.22
CPU DLL:         SARMCM3.DLL V5.41.0.0
Dialog DLL:      DCM.DLL V1.17.5.0
Target DLL:      STLink\ST-LINKIII-KEIL_SWO.dll V3.3.0.0
Dialog DLL:      TCM.DLL V1.56.4.0
 
<h2>Project:</h2>
C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\MDK-ARM\Car_Xifeng_F4.uvprojx
Project File Date:  08/11/2025

<h2>Output:</h2>
*** Using Compiler 'V6.22', folder: 'C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCLANG\Bin'
Build target 'Car_Xifeng_F4'
linking...
Car_Xifeng_F4\Car_Xifeng_F4.axf: Error: L6218E: Undefined symbol esp01_SendCommand (referred from navigation_paging.o).
Not enough information to list image symbols.
Not enough information to list load addresses in the image map.
Finished: 2 information, 0 warning and 1 error messages.
"Car_Xifeng_F4\Car_Xifeng_F4.axf" - 1 Error(s), 0 Warning(s).

<h2>Software Packages used:</h2>

Package Vendor: Keil
                https://www.keil.com/pack/Keil.STM32F4xx_DFP.3.0.0.pack
                Keil::STM32F4xx_DFP@3.0.0
                STMicroelectronics STM32F4 Series Device Support

<h2>Collection of Component include folders:</h2>

<h2>Collection of Component Files used:</h2>
Target not created.
Build Time Elapsed:  00:00:01
</pre>
</body>
</html>
