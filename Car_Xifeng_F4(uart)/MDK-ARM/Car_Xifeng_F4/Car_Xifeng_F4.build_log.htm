<html>
<body>
<pre>
<h1>�Vision Build Log</h1>
<h2>Tool Versions:</h2>
IDE-Version: ��Vision V5.41.0.0
Copyright (C) 2024 ARM Ltd and ARM Germany GmbH. All rights reserved.
License Information: 666 ASUS, 666, LIC=32B1Q-NAAN7-EVX1R-K2M1U-E0BQN-Q6NAJ
 
Tool Versions:
Toolchain:       MDK-ARM Plus  Version: 5.41.0.0
Toolchain Path:  C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCLANG\Bin
C Compiler:      ArmClang.exe V6.22
Assembler:       Armasm.exe V6.22
Linker/Locator:  ArmLink.exe V6.22
Library Manager: ArmAr.exe V6.22
Hex Converter:   FromElf.exe V6.22
CPU DLL:         SARMCM3.DLL V5.41.0.0
Dialog DLL:      DCM.DLL V1.17.5.0
Target DLL:      STLink\ST-LINKIII-KEIL_SWO.dll V3.3.0.0
Dialog DLL:      TCM.DLL V1.56.4.0
 
<h2>Project:</h2>
C:\Users\<USER>\Desktop\Car_Xifeng_F4 (1)\Car_Xifeng_F4(uart)\MDK-ARM\Car_Xifeng_F4.uvprojx
Project File Date:  08/11/2025

<h2>Output:</h2>
*** Using Compiler 'V6.22', folder: 'C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCLANG\Bin'
Build target 'Car_Xifeng_F4'
compiling navigation_routes.c...
navigation_paging.c(284): error: no member named 'step_num' in 'DetectorStep_t'
  284 |             data->steps[i].step_num,
      |             ~~~~~~~~~~~~~~ ^
navigation_paging.c(287): error: no member named 'road_name' in 'DetectorStep_t'
  287 |             data->steps[i].road_name,
      |             ~~~~~~~~~~~~~~ ^
navigation_paging.c(728): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  728 |     data->steps[0] = (NavigationStep_t){1, "Start navigation to Wanda Plaza", "0m", "Current Location", 0, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
navigation_paging.c(729): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  729 |     data->steps[1] = (NavigationStep_t){2, "Head toward Hengzhou Avenue", "800m", "Local Road", 0, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
navigation_paging.c(730): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  730 |     data->steps[2] = (NavigationStep_t){3, "Turn left onto Hengzhou Ave", "0m", "Hengzhou Avenue", 1, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
navigation_paging.c(731): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  731 |     data->steps[3] = (NavigationStep_t){4, "Continue on Hengzhou Ave", "2500m", "Hengzhou Avenue", 0, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
navigation_paging.c(732): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  732 |     data->steps[4] = (NavigationStep_t){5, "Turn right to Wanda Plaza", "200m", "Wanda Plaza Road", 2, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
navigation_paging.c(733): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  733 |     data->steps[5] = (NavigationStep_t){6, "Arrive at Wanda Plaza", "0m", "Wanda Plaza", 3, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
navigation_paging.c(745): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  745 |     data->steps[0] = (NavigationStep_t){1, "Start navigation to Academy", "0m", "Current Location", 0, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
navigation_paging.c(746): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  746 |     data->steps[1] = (NavigationStep_t){2, "Follow GPS guidance", "800m", "Local Road", 0, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
navigation_paging.c(747): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  747 |     data->steps[2] = (NavigationStep_t){3, "Continue toward Academy", "1200m", "Academy Road", 0, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
navigation_paging.c(748): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  748 |     data->steps[3] = (NavigationStep_t){4, "Arrive at Linghu Academy", "0m", "Linghu Academy", 3, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
navigation_paging.c(760): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  760 |     data->steps[0] = (NavigationStep_t){1, "Start navigation to Sports Center", "0m", "Current Location", 0, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
navigation_paging.c(761): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  761 |     data->steps[1] = (NavigationStep_t){2, "Follow GPS guidance", "1500m", "Local Road", 0, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
navigation_paging.c(762): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  762 |     data->steps[2] = (NavigationStep_t){3, "Continue toward Sports Center", "2000m", "Sports Road", 0, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
navigation_paging.c(763): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  763 |     data->steps[3] = (NavigationStep_t){4, "Arrive at Sports Center", "0m", "Sports Center", 3, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
navigation_paging.c(775): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  775 |     data->steps[0] = (NavigationStep_t){1, "Start navigation to Train Station", "0m", "Current Location", 0, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
navigation_paging.c(776): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  776 |     data->steps[1] = (NavigationStep_t){2, "Follow GPS guidance", "2000m", "Local Road", 0, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
navigation_paging.c(777): error: assigning to 'DetectorStep_t' from incompatible type 'NavigationStep_t'
  777 |     data->steps[2] = (NavigationStep_t){3, "Continue toward Train Station", "2500m", "Station Road", 0, 0};
      |                    ^ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
fatal error: too many errors emitted, stopping now [-ferror-limit=]
20 errors generated.
compiling navigation_paging.c...
"Car_Xifeng_F4\Car_Xifeng_F4.axf" - 19 Error(s), 0 Warning(s).

<h2>Software Packages used:</h2>

Package Vendor: Keil
                https://www.keil.com/pack/Keil.STM32F4xx_DFP.3.0.0.pack
                Keil::STM32F4xx_DFP@3.0.0
                STMicroelectronics STM32F4 Series Device Support

<h2>Collection of Component include folders:</h2>

<h2>Collection of Component Files used:</h2>
Target not created.
Build Time Elapsed:  00:00:02
</pre>
</body>
</html>
