--cpu=Cortex-M4.fp.sp
"car_xifeng_f4\startup_stm32f407xx.o"
"car_xifeng_f4\main.o"
"car_xifeng_f4\gpio.o"
"car_xifeng_f4\dma.o"
"car_xifeng_f4\i2c.o"
"car_xifeng_f4\tim.o"
"car_xifeng_f4\usart.o"
"car_xifeng_f4\stm32f4xx_it.o"
"car_xifeng_f4\stm32f4xx_hal_msp.o"
"car_xifeng_f4\stm32f4xx_hal_i2c.o"
"car_xifeng_f4\stm32f4xx_hal_i2c_ex.o"
"car_xifeng_f4\stm32f4xx_hal_rcc.o"
"car_xifeng_f4\stm32f4xx_hal_rcc_ex.o"
"car_xifeng_f4\stm32f4xx_hal_flash.o"
"car_xifeng_f4\stm32f4xx_hal_flash_ex.o"
"car_xifeng_f4\stm32f4xx_hal_flash_ramfunc.o"
"car_xifeng_f4\stm32f4xx_hal_gpio.o"
"car_xifeng_f4\stm32f4xx_hal_dma_ex.o"
"car_xifeng_f4\stm32f4xx_hal_dma.o"
"car_xifeng_f4\stm32f4xx_hal_pwr.o"
"car_xifeng_f4\stm32f4xx_hal_pwr_ex.o"
"car_xifeng_f4\stm32f4xx_hal_cortex.o"
"car_xifeng_f4\stm32f4xx_hal.o"
"car_xifeng_f4\stm32f4xx_hal_exti.o"
"car_xifeng_f4\stm32f4xx_hal_tim.o"
"car_xifeng_f4\stm32f4xx_hal_tim_ex.o"
"car_xifeng_f4\stm32f4xx_hal_uart.o"
"car_xifeng_f4\system_stm32f4xx.o"
"car_xifeng_f4\encoder_driver.o"
"car_xifeng_f4\hardware_iic.o"
"car_xifeng_f4\ringbuffer.o"
"car_xifeng_f4\uart_driver.o"
"car_xifeng_f4\uart2_driver.o"
"car_xifeng_f4\uart3_driver.o"
"car_xifeng_f4\uart6_driver.o"
"car_xifeng_f4\lcd_init_hal.o"
"car_xifeng_f4\lcd_display_hal.o"
"car_xifeng_f4\scheduler.o"
"car_xifeng_f4\uart_app.o"
"car_xifeng_f4\uart2_app.o"
"car_xifeng_f4\uart3_app.o"
"car_xifeng_f4\esp01_app.o"
"car_xifeng_f4\gps_app.o"
"car_xifeng_f4\navigation_app.o"
"car_xifeng_f4\uart6_app.o"
"car_xifeng_f4\tft_app.o"
"car_xifeng_f4\lcd_map_display.o"
"car_xifeng_f4\lcd_test.o"
"car_xifeng_f4\navigation_paging.o"
--library_type=microlib --strict --scatter "Car_Xifeng_F4\Car_Xifeng_F4.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "Car_Xifeng_F4.map" -o Car_Xifeng_F4\Car_Xifeng_F4.axf