Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA1_Stream1_IRQHandler) for DMA1_Stream1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.UART4_IRQHandler) for UART4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler) for DMA2_Stream1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(.text.USART6_IRQHandler) for USART6_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(.text.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to main.o(.text.SystemClock_Config) for SystemClock_Config
    main.o(.text.main) refers to gpio.o(.text.MX_GPIO_Init) for MX_GPIO_Init
    main.o(.text.main) refers to dma.o(.text.MX_DMA_Init) for MX_DMA_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM1_Init) for MX_TIM1_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM3_Init) for MX_TIM3_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM4_Init) for MX_TIM4_Init
    main.o(.text.main) refers to i2c.o(.text.MX_I2C2_Init) for MX_I2C2_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(.text.main) refers to usart.o(.text.MX_UART4_Init) for MX_UART4_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART6_UART_Init) for MX_USART6_UART_Init
    main.o(.text.main) refers to scheduler.o(.text.Scheduler_Init) for Scheduler_Init
    main.o(.text.main) refers to scheduler.o(.text.Scheduler_Run) for Scheduler_Run
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.ARM.exidx.text.SystemClock_Config) refers to main.o(.text.SystemClock_Config) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    gpio.o(.text.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(.text.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(.ARM.exidx.text.MX_GPIO_Init) refers to gpio.o(.text.MX_GPIO_Init) for [Anonymous Symbol]
    dma.o(.text.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(.text.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    dma.o(.ARM.exidx.text.MX_DMA_Init) refers to dma.o(.text.MX_DMA_Init) for [Anonymous Symbol]
    i2c.o(.text.MX_I2C2_Init) refers to i2c.o(.bss.hi2c2) for hi2c2
    i2c.o(.text.MX_I2C2_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(.text.MX_I2C2_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    i2c.o(.ARM.exidx.text.MX_I2C2_Init) refers to i2c.o(.text.MX_I2C2_Init) for [Anonymous Symbol]
    i2c.o(.text.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(.ARM.exidx.text.HAL_I2C_MspInit) refers to i2c.o(.text.HAL_I2C_MspInit) for [Anonymous Symbol]
    i2c.o(.text.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit) refers to i2c.o(.text.HAL_I2C_MspDeInit) for [Anonymous Symbol]
    tim.o(.text.MX_TIM1_Init) refers to tim.o(.bss.htim1) for htim1
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM1_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(.text.MX_TIM1_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.MX_TIM1_Init) refers to tim.o(.text.MX_TIM1_Init) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.HAL_TIM_MspPostInit) refers to tim.o(.text.HAL_TIM_MspPostInit) for [Anonymous Symbol]
    tim.o(.text.MX_TIM3_Init) refers to tim.o(.bss.htim3) for htim3
    tim.o(.text.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(.text.MX_TIM3_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.ARM.exidx.text.MX_TIM3_Init) refers to tim.o(.text.MX_TIM3_Init) for [Anonymous Symbol]
    tim.o(.text.MX_TIM4_Init) refers to tim.o(.bss.htim4) for htim4
    tim.o(.text.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(.text.MX_TIM4_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.ARM.exidx.text.MX_TIM4_Init) refers to tim.o(.text.MX_TIM4_Init) for [Anonymous Symbol]
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    usart.o(.text.MX_UART4_Init) refers to usart.o(.bss.huart4) for huart4
    usart.o(.text.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_UART4_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_UART4_Init) refers to usart.o(.text.MX_UART4_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.huart1) for huart1
    usart.o(.text.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART1_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART1_UART_Init) refers to usart.o(.text.MX_USART1_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART2_UART_Init) refers to usart.o(.bss.huart2) for huart2
    usart.o(.text.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART2_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART2_UART_Init) refers to usart.o(.text.MX_USART2_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART3_UART_Init) refers to usart.o(.bss.huart3) for huart3
    usart.o(.text.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART3_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART3_UART_Init) refers to usart.o(.text.MX_USART3_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART6_UART_Init) refers to usart.o(.bss.huart6) for huart6
    usart.o(.text.MX_USART6_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART6_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART6_UART_Init) refers to usart.o(.text.MX_USART6_UART_Init) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_usart3_rx) for hdma_usart3_rx
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(.text.HAL_UART_MspInit) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_usart6_rx) for hdma_usart6_rx
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    usart.o(.text.HAL_UART_MspInit) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(.text.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to usart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    usart.o(.text.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    usart.o(.text.my_printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(.ARM.exidx.text.my_printf) refers to usart.o(.text.my_printf) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f4xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f4xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f4xx_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f4xx_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f4xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f4xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.SysTick_Handler) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f4xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA1_Stream1_IRQHandler) refers to usart.o(.bss.hdma_usart3_rx) for hdma_usart3_rx
    stm32f4xx_it.o(.text.DMA1_Stream1_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream1_IRQHandler) refers to stm32f4xx_it.o(.text.DMA1_Stream1_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream5_IRQHandler) refers to stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to usart.o(.bss.huart1) for huart1
    stm32f4xx_it.o(.text.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler) refers to stm32f4xx_it.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to usart.o(.bss.huart2) for huart2
    stm32f4xx_it.o(.text.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART2_IRQHandler) refers to stm32f4xx_it.o(.text.USART2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART3_IRQHandler) refers to usart.o(.bss.huart3) for huart3
    stm32f4xx_it.o(.text.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART3_IRQHandler) refers to stm32f4xx_it.o(.text.USART3_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.UART4_IRQHandler) refers to usart.o(.bss.huart4) for huart4
    stm32f4xx_it.o(.text.UART4_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.UART4_IRQHandler) refers to stm32f4xx_it.o(.text.UART4_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler) refers to usart.o(.bss.hdma_usart6_rx) for hdma_usart6_rx
    stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream1_IRQHandler) refers to stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream2_IRQHandler) refers to stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.USART6_IRQHandler) refers to usart.o(.bss.huart6) for huart6
    stm32f4xx_it.o(.text.USART6_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.USART6_IRQHandler) refers to stm32f4xx_it.o(.text.USART6_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) refers to i2c.o(.text.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) refers to i2c.o(.text.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetMode) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent) refers to stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_msp.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f4xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f4xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f4xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f4xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f4xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_EnableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_DisableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending) for [Anonymous Symbol]
    stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI) refers to stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to tim.o(.text.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMAError) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to tim.o(.text.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) refers to tim.o(.text.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_GetChannelState) for [Anonymous Symbol]
    stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurstState) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState) refers to stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to uart_driver.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to uart_driver.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to uart_driver.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to uart_driver.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for [Anonymous Symbol]
    system_stm32f4xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32f4xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    encoder_driver.o(.text.Encoder_Driver_Init) refers to stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    encoder_driver.o(.ARM.exidx.text.Encoder_Driver_Init) refers to encoder_driver.o(.text.Encoder_Driver_Init) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.Encoder_Driver_Update) refers to encoder_driver.o(.text.Encoder_Driver_Update) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_ReadByte) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_ReadByte) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    hardware_iic.o(.ARM.exidx.text.IIC_ReadByte) refers to hardware_iic.o(.text.IIC_ReadByte) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_ReadBytes) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_ReadBytes) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.IIC_ReadBytes) refers to hardware_iic.o(.text.IIC_ReadBytes) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_WriteByte) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_WriteByte) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    hardware_iic.o(.ARM.exidx.text.IIC_WriteByte) refers to hardware_iic.o(.text.IIC_WriteByte) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_WriteBytes) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_WriteBytes) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    hardware_iic.o(.ARM.exidx.text.IIC_WriteBytes) refers to hardware_iic.o(.text.IIC_WriteBytes) for [Anonymous Symbol]
    hardware_iic.o(.text.Ping) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.Ping) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.Ping) refers to hardware_iic.o(.text.Ping) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Digtal) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_Get_Digtal) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Digtal) refers to hardware_iic.o(.text.IIC_Get_Digtal) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Anolog) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_Get_Anolog) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Anolog) refers to hardware_iic.o(.text.IIC_Get_Anolog) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Single_Anolog) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_Get_Single_Anolog) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Single_Anolog) refers to hardware_iic.o(.text.IIC_Get_Single_Anolog) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Anolog_Normalize) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_Anolog_Normalize) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    hardware_iic.o(.ARM.exidx.text.IIC_Anolog_Normalize) refers to hardware_iic.o(.text.IIC_Anolog_Normalize) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Offset) refers to i2c.o(.bss.hi2c2) for hi2c2
    hardware_iic.o(.text.IIC_Get_Offset) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Offset) refers to hardware_iic.o(.text.IIC_Get_Offset) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_status) refers to ringbuffer.o(.text.rt_ringbuffer_status) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_init) refers to ringbuffer.o(.text.rt_ringbuffer_init) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_put) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put) refers to ringbuffer.o(.text.rt_ringbuffer_put) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_data_len) refers to ringbuffer.o(.text.rt_ringbuffer_data_len) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_put_force) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put_force) refers to ringbuffer.o(.text.rt_ringbuffer_put_force) for [Anonymous Symbol]
    ringbuffer.o(.text.rt_ringbuffer_get) refers to memcpya.o(.text) for __aeabi_memcpy
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_get) refers to ringbuffer.o(.text.rt_ringbuffer_get) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_peek) refers to ringbuffer.o(.text.rt_ringbuffer_peek) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar) refers to ringbuffer.o(.text.rt_ringbuffer_putchar) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar_force) refers to ringbuffer.o(.text.rt_ringbuffer_putchar_force) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_getchar) refers to ringbuffer.o(.text.rt_ringbuffer_getchar) for [Anonymous Symbol]
    ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_reset) refers to ringbuffer.o(.text.rt_ringbuffer_reset) for [Anonymous Symbol]
    uart_driver.o(.text.Uart_Printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart_driver.o(.text.Uart_Printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    uart_driver.o(.ARM.exidx.text.Uart_Printf) refers to uart_driver.o(.text.Uart_Printf) for [Anonymous Symbol]
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) for HAL_UARTEx_RxEventCallback_UART3
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to uart_driver.o(.bss.uart_rx_dma_buffer) for uart_rx_dma_buffer
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to uart_driver.o(.bss.ring_buffer) for ring_buffer
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(.text.rt_ringbuffer_put) for rt_ringbuffer_put
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to memseta.o(.text) for __aeabi_memclr4
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss.huart1) for huart1
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) for HAL_UARTEx_RxEventCallback_UART2
    uart_driver.o(.text.HAL_UARTEx_RxEventCallback) refers to uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) for HAL_UARTEx_RxEventCallback_UART6
    uart_driver.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to uart_driver.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    uart2_driver.o(.text.Uart2_Printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart2_driver.o(.text.Uart2_Printf) refers to usart.o(.bss.huart1) for huart1
    uart2_driver.o(.text.Uart2_Printf) refers to usart.o(.text.my_printf) for my_printf
    uart2_driver.o(.text.Uart2_Printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    uart2_driver.o(.ARM.exidx.text.Uart2_Printf) refers to uart2_driver.o(.text.Uart2_Printf) for [Anonymous Symbol]
    uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) refers to uart2_driver.o(.bss.uart2_rx_dma_buffer) for uart2_rx_dma_buffer
    uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) refers to uart2_driver.o(.bss.uart2_ring_buffer) for uart2_ring_buffer
    uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) refers to ringbuffer.o(.text.rt_ringbuffer_put) for rt_ringbuffer_put
    uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) refers to memseta.o(.text) for __aeabi_memclr4
    uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) refers to usart.o(.bss.huart2) for huart2
    uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    uart2_driver.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback_UART2) refers to uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2) for [Anonymous Symbol]
    uart3_driver.o(.text.Uart3_Printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart3_driver.o(.text.Uart3_Printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    uart3_driver.o(.ARM.exidx.text.Uart3_Printf) refers to uart3_driver.o(.text.Uart3_Printf) for [Anonymous Symbol]
    uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) refers to uart3_driver.o(.bss.uart3_rx_dma_buffer) for uart3_rx_dma_buffer
    uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) refers to uart3_driver.o(.bss.uart3_ring_buffer) for uart3_ring_buffer
    uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) refers to ringbuffer.o(.text.rt_ringbuffer_put) for rt_ringbuffer_put
    uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) refers to memseta.o(.text) for __aeabi_memclr4
    uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) refers to usart.o(.bss.huart3) for huart3
    uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) refers to usart.o(.bss.hdma_usart3_rx) for hdma_usart3_rx
    uart3_driver.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback_UART3) refers to uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3) for [Anonymous Symbol]
    uart6_driver.o(.text.Uart6_Printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart6_driver.o(.text.Uart6_Printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    uart6_driver.o(.ARM.exidx.text.Uart6_Printf) refers to uart6_driver.o(.text.Uart6_Printf) for [Anonymous Symbol]
    uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) refers to uart6_driver.o(.bss.uart6_rx_dma_buffer) for uart6_rx_dma_buffer
    uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) refers to uart6_driver.o(.bss.uart6_ring_buffer) for uart6_ring_buffer
    uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) refers to ringbuffer.o(.text.rt_ringbuffer_put) for rt_ringbuffer_put
    uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) refers to memseta.o(.text) for __aeabi_memclr4
    uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) refers to usart.o(.bss.huart6) for huart6
    uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) refers to usart.o(.bss.hdma_usart6_rx) for hdma_usart6_rx
    uart6_driver.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback_UART6) refers to uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6) for [Anonymous Symbol]
    lcd_init_hal.o(.text.LCD_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd_init_hal.o(.text.LCD_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd_init_hal.o(.ARM.exidx.text.LCD_GPIO_Init) refers to lcd_init_hal.o(.text.LCD_GPIO_Init) for [Anonymous Symbol]
    lcd_init_hal.o(.text.LCD_Writ_Bus) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd_init_hal.o(.ARM.exidx.text.LCD_Writ_Bus) refers to lcd_init_hal.o(.text.LCD_Writ_Bus) for [Anonymous Symbol]
    lcd_init_hal.o(.text.LCD_WR_DATA8) refers to lcd_init_hal.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    lcd_init_hal.o(.ARM.exidx.text.LCD_WR_DATA8) refers to lcd_init_hal.o(.text.LCD_WR_DATA8) for [Anonymous Symbol]
    lcd_init_hal.o(.text.LCD_WR_DATA) refers to lcd_init_hal.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    lcd_init_hal.o(.ARM.exidx.text.LCD_WR_DATA) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for [Anonymous Symbol]
    lcd_init_hal.o(.text.LCD_WR_REG) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd_init_hal.o(.text.LCD_WR_REG) refers to lcd_init_hal.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    lcd_init_hal.o(.ARM.exidx.text.LCD_WR_REG) refers to lcd_init_hal.o(.text.LCD_WR_REG) for [Anonymous Symbol]
    lcd_init_hal.o(.text.LCD_Address_Set) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd_init_hal.o(.text.LCD_Address_Set) refers to lcd_init_hal.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    lcd_init_hal.o(.ARM.exidx.text.LCD_Address_Set) refers to lcd_init_hal.o(.text.LCD_Address_Set) for [Anonymous Symbol]
    lcd_init_hal.o(.text.LCD_Init) refers to usart.o(.bss.huart1) for huart1
    lcd_init_hal.o(.text.LCD_Init) refers to lcd_init_hal.o(.rodata.str1.1) for .L.str
    lcd_init_hal.o(.text.LCD_Init) refers to usart.o(.text.my_printf) for my_printf
    lcd_init_hal.o(.text.LCD_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    lcd_init_hal.o(.text.LCD_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    lcd_init_hal.o(.text.LCD_Init) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    lcd_init_hal.o(.text.LCD_Init) refers to lcd_init_hal.o(.text.LCD_Writ_Bus) for LCD_Writ_Bus
    lcd_init_hal.o(.text.LCD_Init) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd_init_hal.o(.ARM.exidx.text.LCD_Init) refers to lcd_init_hal.o(.text.LCD_Init) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_GetDigitPixel) refers to lcd_display_hal.o(.rodata..L__const.LCD_GetDigitPixel.digit_patterns) for .L__const.LCD_GetDigitPixel.digit_patterns
    lcd_display_hal.o(.ARM.exidx.text.LCD_GetDigitPixel) refers to lcd_display_hal.o(.text.LCD_GetDigitPixel) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_GetLetterPixel) refers to lcd_display_hal.o(.rodata..L__const.LCD_GetLetterPixel.letter_patterns) for .L__const.LCD_GetLetterPixel.letter_patterns
    lcd_display_hal.o(.ARM.exidx.text.LCD_GetLetterPixel) refers to lcd_display_hal.o(.text.LCD_GetLetterPixel) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_Fill) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd_display_hal.o(.text.LCD_Fill) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd_display_hal.o(.ARM.exidx.text.LCD_Fill) refers to lcd_display_hal.o(.text.LCD_Fill) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_DrawPoint) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd_display_hal.o(.text.LCD_DrawPoint) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd_display_hal.o(.ARM.exidx.text.LCD_DrawPoint) refers to lcd_display_hal.o(.text.LCD_DrawPoint) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_DrawLine) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd_display_hal.o(.text.LCD_DrawLine) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd_display_hal.o(.ARM.exidx.text.LCD_DrawLine) refers to lcd_display_hal.o(.text.LCD_DrawLine) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_DrawRectangle) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd_display_hal.o(.text.LCD_DrawRectangle) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd_display_hal.o(.ARM.exidx.text.LCD_DrawRectangle) refers to lcd_display_hal.o(.text.LCD_DrawRectangle) for [Anonymous Symbol]
    lcd_display_hal.o(.text.Draw_Circle) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd_display_hal.o(.text.Draw_Circle) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd_display_hal.o(.ARM.exidx.text.Draw_Circle) refers to lcd_display_hal.o(.text.Draw_Circle) for [Anonymous Symbol]
    lcd_display_hal.o(.ARM.exidx.text.mypow) refers to lcd_display_hal.o(.text.mypow) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_ShowIntNum) refers to lcd_display_hal.o(.text.LCD_ShowChar) for LCD_ShowChar
    lcd_display_hal.o(.ARM.exidx.text.LCD_ShowIntNum) refers to lcd_display_hal.o(.text.LCD_ShowIntNum) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_ShowChar) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd_display_hal.o(.text.LCD_ShowChar) refers to lcd_display_hal.o(.rodata..L__const.LCD_GetDigitPixel.digit_patterns) for .L__const.LCD_GetDigitPixel.digit_patterns
    lcd_display_hal.o(.text.LCD_ShowChar) refers to lcd_display_hal.o(.rodata..L__const.LCD_GetLetterPixel.letter_patterns) for .L__const.LCD_GetLetterPixel.letter_patterns
    lcd_display_hal.o(.text.LCD_ShowChar) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd_display_hal.o(.ARM.exidx.text.LCD_ShowChar) refers to lcd_display_hal.o(.text.LCD_ShowChar) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_ShowFloatNum1) refers to lcd_display_hal.o(.text.LCD_ShowChar) for LCD_ShowChar
    lcd_display_hal.o(.ARM.exidx.text.LCD_ShowFloatNum1) refers to lcd_display_hal.o(.text.LCD_ShowFloatNum1) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_ShowString) refers to lcd_display_hal.o(.text.LCD_ShowChar) for LCD_ShowChar
    lcd_display_hal.o(.ARM.exidx.text.LCD_ShowString) refers to lcd_display_hal.o(.text.LCD_ShowString) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_GetChinesePattern) refers to strncmp.o(.text) for strncmp
    lcd_display_hal.o(.ARM.exidx.text.LCD_GetChinesePattern) refers to lcd_display_hal.o(.text.LCD_GetChinesePattern) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_ShowChinese) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd_display_hal.o(.text.LCD_ShowChinese) refers to lcd_display_hal.o(.text.LCD_GetChinesePattern) for LCD_GetChinesePattern
    lcd_display_hal.o(.text.LCD_ShowChinese) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd_display_hal.o(.ARM.exidx.text.LCD_ShowChinese) refers to lcd_display_hal.o(.text.LCD_ShowChinese) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_ShowChinese12x12) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd_display_hal.o(.text.LCD_ShowChinese12x12) refers to lcd_display_hal.o(.text.LCD_GetChinesePattern) for LCD_GetChinesePattern
    lcd_display_hal.o(.text.LCD_ShowChinese12x12) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd_display_hal.o(.ARM.exidx.text.LCD_ShowChinese12x12) refers to lcd_display_hal.o(.text.LCD_ShowChinese12x12) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_ShowChinese16x16) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd_display_hal.o(.text.LCD_ShowChinese16x16) refers to lcd_display_hal.o(.text.LCD_GetChinesePattern) for LCD_GetChinesePattern
    lcd_display_hal.o(.text.LCD_ShowChinese16x16) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd_display_hal.o(.ARM.exidx.text.LCD_ShowChinese16x16) refers to lcd_display_hal.o(.text.LCD_ShowChinese16x16) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_ShowChinese24x24) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd_display_hal.o(.text.LCD_ShowChinese24x24) refers to lcd_display_hal.o(.text.LCD_GetChinesePattern) for LCD_GetChinesePattern
    lcd_display_hal.o(.text.LCD_ShowChinese24x24) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd_display_hal.o(.ARM.exidx.text.LCD_ShowChinese24x24) refers to lcd_display_hal.o(.text.LCD_ShowChinese24x24) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_ShowChinese32x32) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd_display_hal.o(.text.LCD_ShowChinese32x32) refers to lcd_display_hal.o(.text.LCD_GetChinesePattern) for LCD_GetChinesePattern
    lcd_display_hal.o(.text.LCD_ShowChinese32x32) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd_display_hal.o(.ARM.exidx.text.LCD_ShowChinese32x32) refers to lcd_display_hal.o(.text.LCD_ShowChinese32x32) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_ShowMixedString) refers to lcd_display_hal.o(.text.LCD_ShowChar) for LCD_ShowChar
    lcd_display_hal.o(.text.LCD_ShowMixedString) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd_display_hal.o(.text.LCD_ShowMixedString) refers to lcd_display_hal.o(.text.LCD_GetChinesePattern) for LCD_GetChinesePattern
    lcd_display_hal.o(.text.LCD_ShowMixedString) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd_display_hal.o(.ARM.exidx.text.LCD_ShowMixedString) refers to lcd_display_hal.o(.text.LCD_ShowMixedString) for [Anonymous Symbol]
    lcd_display_hal.o(.text.LCD_ShowPicture) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    lcd_display_hal.o(.text.LCD_ShowPicture) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    lcd_display_hal.o(.ARM.exidx.text.LCD_ShowPicture) refers to lcd_display_hal.o(.text.LCD_ShowPicture) for [Anonymous Symbol]
    scheduler.o(.text.GPS_AutoUpload_Task) refers to scheduler.o(.bss.GPS_AutoUpload_Task.upload_count) for GPS_AutoUpload_Task.upload_count
    scheduler.o(.text.GPS_AutoUpload_Task) refers to usart.o(.bss.huart1) for huart1
    scheduler.o(.text.GPS_AutoUpload_Task) refers to usart.o(.text.my_printf) for my_printf
    scheduler.o(.text.GPS_AutoUpload_Task) refers to esp01_app.o(.text.esp01_UploadGPSData) for esp01_UploadGPSData
    scheduler.o(.ARM.exidx.text.GPS_AutoUpload_Task) refers to scheduler.o(.text.GPS_AutoUpload_Task) for [Anonymous Symbol]
    scheduler.o(.text.System_Init) refers to uart_app.o(.text.Uart_Init) for Uart_Init
    scheduler.o(.text.System_Init) refers to uart2_app.o(.text.Uart2_Init) for Uart2_Init
    scheduler.o(.text.System_Init) refers to uart6_app.o(.text.Uart6_Init) for Uart6_Init
    scheduler.o(.text.System_Init) refers to gps_app.o(.text.GPS_Virtual_Init) for GPS_Virtual_Init
    scheduler.o(.text.System_Init) refers to esp01_app.o(.text.esp01_Init) for esp01_Init
    scheduler.o(.text.System_Init) refers to tft_app.o(.text.tft_Init) for tft_Init
    scheduler.o(.ARM.exidx.text.System_Init) refers to scheduler.o(.text.System_Init) for [Anonymous Symbol]
    scheduler.o(.text.Scheduler_Init) refers to uart_app.o(.text.Uart_Init) for Uart_Init
    scheduler.o(.text.Scheduler_Init) refers to uart2_app.o(.text.Uart2_Init) for Uart2_Init
    scheduler.o(.text.Scheduler_Init) refers to uart6_app.o(.text.Uart6_Init) for Uart6_Init
    scheduler.o(.text.Scheduler_Init) refers to gps_app.o(.text.GPS_Virtual_Init) for GPS_Virtual_Init
    scheduler.o(.text.Scheduler_Init) refers to esp01_app.o(.text.esp01_Init) for esp01_Init
    scheduler.o(.text.Scheduler_Init) refers to tft_app.o(.text.tft_Init) for tft_Init
    scheduler.o(.text.Scheduler_Init) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.ARM.exidx.text.Scheduler_Init) refers to scheduler.o(.text.Scheduler_Init) for [Anonymous Symbol]
    scheduler.o(.text.Scheduler_Run) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.text.Scheduler_Run) refers to scheduler.o(.data.scheduler_task) for scheduler_task
    scheduler.o(.text.Scheduler_Run) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    scheduler.o(.ARM.exidx.text.Scheduler_Run) refers to scheduler.o(.text.Scheduler_Run) for [Anonymous Symbol]
    scheduler.o(.data.scheduler_task) refers to uart_app.o(.text.Uart_Task) for Uart_Task
    scheduler.o(.data.scheduler_task) refers to uart2_app.o(.text.Uart2_Task) for Uart2_Task
    scheduler.o(.data.scheduler_task) refers to uart3_app.o(.text.Uart3_Task) for Uart3_Task
    scheduler.o(.data.scheduler_task) refers to gps_app.o(.text.GPS_Task) for GPS_Task
    scheduler.o(.data.scheduler_task) refers to esp01_app.o(.text.esp01_Task) for esp01_Task
    scheduler.o(.data.scheduler_task) refers to uart6_app.o(.text.Uart6_Task) for Uart6_Task
    scheduler.o(.data.scheduler_task) refers to tft_app.o(.text.tft_Task) for tft_Task
    scheduler.o(.data.scheduler_task) refers to scheduler.o(.text.GPS_AutoUpload_Task) for GPS_AutoUpload_Task
    uart_app.o(.text.Uart_Init) refers to usart.o(.bss.huart1) for huart1
    uart_app.o(.text.Uart_Init) refers to usart.o(.text.my_printf) for my_printf
    uart_app.o(.text.Uart_Init) refers to uart_driver.o(.bss.ring_buffer) for ring_buffer
    uart_app.o(.text.Uart_Init) refers to uart_driver.o(.bss.ring_buffer_input) for ring_buffer_input
    uart_app.o(.text.Uart_Init) refers to ringbuffer.o(.text.rt_ringbuffer_init) for rt_ringbuffer_init
    uart_app.o(.text.Uart_Init) refers to uart_driver.o(.bss.uart_rx_dma_buffer) for uart_rx_dma_buffer
    uart_app.o(.text.Uart_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_app.o(.text.Uart_Init) refers to usart.o(.bss.hdma_usart1_rx) for hdma_usart1_rx
    uart_app.o(.ARM.exidx.text.Uart_Init) refers to uart_app.o(.text.Uart_Init) for [Anonymous Symbol]
    uart_app.o(.text.Uart_Task) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    uart_app.o(.text.Uart_Task) refers to uart_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    uart_app.o(.text.Uart_Task) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    uart_app.o(.text.Uart_Task) refers to usart.o(.bss.huart1) for huart1
    uart_app.o(.text.Uart_Task) refers to usart.o(.text.my_printf) for my_printf
    uart_app.o(.text.Uart_Task) refers to uart_driver.o(.bss.ring_buffer) for ring_buffer
    uart_app.o(.text.Uart_Task) refers to ringbuffer.o(.text.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart_app.o(.text.Uart_Task) refers to uart_driver.o(.bss.uart_data_buffer) for uart_data_buffer
    uart_app.o(.text.Uart_Task) refers to ringbuffer.o(.text.rt_ringbuffer_get) for rt_ringbuffer_get
    uart_app.o(.text.Uart_Task) refers to strncpy.o(.text) for strncpy
    uart_app.o(.text.Uart_Task) refers to strchr.o(.text) for strchr
    uart_app.o(.text.Uart_Task) refers to memcmp.o(.text) for memcmp
    uart_app.o(.text.Uart_Task) refers to uart_app.o(.rodata.str1.1) for .L.str.5
    uart_app.o(.text.Uart_Task) refers to navigation_app.o(.text.Navigation_ProcessCommand) for Navigation_ProcessCommand
    uart_app.o(.text.Uart_Task) refers to navigation_app.o(.text.Navigation_StartNavigation) for Navigation_StartNavigation
    uart_app.o(.text.Uart_Task) refers to esp01_app.o(.text.esp01_GetState) for esp01_GetState
    uart_app.o(.text.Uart_Task) refers to esp01_app.o(.text.esp01_UploadGPSData) for esp01_UploadGPSData
    uart_app.o(.text.Uart_Task) refers to esp01_app.o(.text.esp01_Reset) for esp01_Reset
    uart_app.o(.text.Uart_Task) refers to memseta.o(.text) for __aeabi_memclr
    uart_app.o(.text.Uart_Task) refers to ringbuffer.o(.text.rt_ringbuffer_reset) for rt_ringbuffer_reset
    uart_app.o(.text.Uart_Task) refers to usart.o(.bss.huart2) for huart2
    uart_app.o(.text.Uart_Task) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    uart_app.o(.text.Uart_Task) refers to esp01_app.o(.text.esp01_GetRealLocation) for esp01_GetRealLocation
    uart_app.o(.text.Uart_Task) refers to f2d.o(.text) for __aeabi_f2d
    uart_app.o(.text.Uart_Task) refers to esp01_app.o(.text.esp01_ReconnectWiFi) for esp01_ReconnectWiFi
    uart_app.o(.text.Uart_Task) refers to esp01_app.o(.text.esp01_CheckConnection) for esp01_CheckConnection
    uart_app.o(.text.Uart_Task) refers to tft_app.o(.text.tft_BasicTest) for tft_BasicTest
    uart_app.o(.text.Uart_Task) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    uart_app.o(.text.Uart_Task) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    uart_app.o(.text.Uart_Task) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    uart_app.o(.ARM.exidx.text.Uart_Task) refers to uart_app.o(.text.Uart_Task) for [Anonymous Symbol]
    uart2_app.o(.text.Uart2_Init) refers to uart2_driver.o(.bss.uart2_ring_buffer) for uart2_ring_buffer
    uart2_app.o(.text.Uart2_Init) refers to uart2_driver.o(.bss.uart2_ring_buffer_input) for uart2_ring_buffer_input
    uart2_app.o(.text.Uart2_Init) refers to ringbuffer.o(.text.rt_ringbuffer_init) for rt_ringbuffer_init
    uart2_app.o(.text.Uart2_Init) refers to usart.o(.bss.huart2) for huart2
    uart2_app.o(.text.Uart2_Init) refers to uart2_driver.o(.bss.uart2_rx_dma_buffer) for uart2_rx_dma_buffer
    uart2_app.o(.text.Uart2_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart2_app.o(.text.Uart2_Init) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    uart2_app.o(.ARM.exidx.text.Uart2_Init) refers to uart2_app.o(.text.Uart2_Init) for [Anonymous Symbol]
    uart2_app.o(.text.Uart2_Task) refers to uart2_driver.o(.bss.uart2_ring_buffer) for uart2_ring_buffer
    uart2_app.o(.text.Uart2_Task) refers to ringbuffer.o(.text.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart2_app.o(.text.Uart2_Task) refers to uart2_app.o(.bss.Uart2_Task.last_receive_time) for Uart2_Task.last_receive_time
    uart2_app.o(.text.Uart2_Task) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    uart2_app.o(.text.Uart2_Task) refers to usart.o(.bss.huart2) for huart2
    uart2_app.o(.text.Uart2_Task) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart2_app.o(.text.Uart2_Task) refers to uart2_driver.o(.bss.uart2_rx_dma_buffer) for uart2_rx_dma_buffer
    uart2_app.o(.text.Uart2_Task) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart2_app.o(.text.Uart2_Task) refers to usart.o(.bss.hdma_usart2_rx) for hdma_usart2_rx
    uart2_app.o(.text.Uart2_Task) refers to uart2_driver.o(.bss.uart2_data_buffer) for uart2_data_buffer
    uart2_app.o(.text.Uart2_Task) refers to ringbuffer.o(.text.rt_ringbuffer_get) for rt_ringbuffer_get
    uart2_app.o(.text.Uart2_Task) refers to usart.o(.bss.huart1) for huart1
    uart2_app.o(.text.Uart2_Task) refers to usart.o(.text.my_printf) for my_printf
    uart2_app.o(.text.Uart2_Task) refers to strstr.o(.text) for strstr
    uart2_app.o(.text.Uart2_Task) refers to uart2_app.o(.rodata.str1.1) for .L.str.2
    uart2_app.o(.text.Uart2_Task) refers to esp01_app.o(.text.esp01_SetConnected) for esp01_SetConnected
    uart2_app.o(.text.Uart2_Task) refers to memseta.o(.text) for __aeabi_memclr
    uart2_app.o(.text.Uart2_Task) refers to esp01_app.o(.text.esp01_StartInit) for esp01_StartInit
    uart2_app.o(.text.Uart2_Task) refers to esp01_app.o(.text.esp01_SetTCPConnected) for esp01_SetTCPConnected
    uart2_app.o(.text.Uart2_Task) refers to strchr.o(.text) for strchr
    uart2_app.o(.text.Uart2_Task) refers to esp01_app.o(.text.esp01_SetDataSendReady) for esp01_SetDataSendReady
    uart2_app.o(.text.Uart2_Task) refers to esp01_app.o(.text.esp01_ResetTCPState) for esp01_ResetTCPState
    uart2_app.o(.ARM.exidx.text.Uart2_Task) refers to uart2_app.o(.text.Uart2_Task) for [Anonymous Symbol]
    uart3_app.o(.text.Uart3_Init) refers to gps_app.o(.text.GPS_Init) for GPS_Init
    uart3_app.o(.text.Uart3_Init) refers to navigation_app.o(.text.Navigation_Init) for Navigation_Init
    uart3_app.o(.ARM.exidx.text.Uart3_Init) refers to uart3_app.o(.text.Uart3_Init) for [Anonymous Symbol]
    uart3_app.o(.text.Uart3_Task) refers to gps_app.o(.text.GPS_Task) for GPS_Task
    uart3_app.o(.text.Uart3_Task) refers to navigation_app.o(.text.Navigation_Task) for Navigation_Task
    uart3_app.o(.ARM.exidx.text.Uart3_Task) refers to uart3_app.o(.text.Uart3_Task) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_Init) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_Init) refers to esp01_app.o(.rodata.str1.1) for .L.str
    esp01_app.o(.text.esp01_Init) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_Init) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.text.esp01_Init) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_Init) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_Init) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.ARM.exidx.text.esp01_Init) refers to esp01_app.o(.text.esp01_Init) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_CheckAndReinit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    esp01_app.o(.text.esp01_CheckAndReinit) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.text.esp01_CheckAndReinit) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_CheckAndReinit) refers to esp01_app.o(.rodata.str1.1) for .L.str.14
    esp01_app.o(.text.esp01_CheckAndReinit) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_CheckAndReinit) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_CheckAndReinit) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_CheckAndReinit) refers to esp01_app.o(.text.esp01_Init) for esp01_Init
    esp01_app.o(.ARM.exidx.text.esp01_CheckAndReinit) refers to esp01_app.o(.text.esp01_CheckAndReinit) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_StartInit) refers to esp01_app.o(.text.esp01_Init) for esp01_Init
    esp01_app.o(.text.esp01_StartInit) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_StartInit) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.ARM.exidx.text.esp01_StartInit) refers to esp01_app.o(.text.esp01_StartInit) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_GetState) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.ARM.exidx.text.esp01_GetState) refers to esp01_app.o(.text.esp01_GetState) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_SetSimulationLocation) refers to esp01_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_SetSimulationLocation) refers to f2d.o(.text) for __aeabi_f2d
    esp01_app.o(.text.esp01_SetSimulationLocation) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_SetSimulationLocation) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.ARM.exidx.text.esp01_SetSimulationLocation) refers to esp01_app.o(.text.esp01_SetSimulationLocation) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_GetRealLocation) refers to esp01_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.ARM.exidx.text.esp01_GetRealLocation) refers to esp01_app.o(.text.esp01_GetRealLocation) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_UploadGPSData) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.text.esp01_UploadGPSData) refers to esp01_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_UploadGPSData) refers to f2d.o(.text) for __aeabi_f2d
    esp01_app.o(.text.esp01_UploadGPSData) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_UploadGPSData) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_UploadGPSData) refers to esp01_app.o(.rodata.str1.1) for .L.str.19
    esp01_app.o(.text.esp01_UploadGPSData) refers to printfa.o(i.__0snprintf) for __2snprintf
    esp01_app.o(.text.esp01_UploadGPSData) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_UploadGPSData) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_UploadGPSData) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.text.esp01_UploadGPSData) refers to strlen.o(.text) for strlen
    esp01_app.o(.ARM.exidx.text.esp01_UploadGPSData) refers to esp01_app.o(.text.esp01_UploadGPSData) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_UploadNavigationCommand) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.text.esp01_UploadNavigationCommand) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_UploadNavigationCommand) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_UploadNavigationCommand) refers to esp01_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_UploadNavigationCommand) refers to f2d.o(.text) for __aeabi_f2d
    esp01_app.o(.text.esp01_UploadNavigationCommand) refers to esp01_app.o(.rodata.str1.1) for .L.str.27
    esp01_app.o(.text.esp01_UploadNavigationCommand) refers to printfa.o(i.__0snprintf) for __2snprintf
    esp01_app.o(.text.esp01_UploadNavigationCommand) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_UploadNavigationCommand) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_UploadNavigationCommand) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.text.esp01_UploadNavigationCommand) refers to strlen.o(.text) for strlen
    esp01_app.o(.ARM.exidx.text.esp01_UploadNavigationCommand) refers to esp01_app.o(.text.esp01_UploadNavigationCommand) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_SendNavigationData) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_SendNavigationData) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_SendNavigationData) refers to esp01_app.o(.text.esp01_UploadGPSData) for esp01_UploadGPSData
    esp01_app.o(.ARM.exidx.text.esp01_SendNavigationData) refers to esp01_app.o(.text.esp01_SendNavigationData) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_Task) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    esp01_app.o(.text.esp01_Task) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.text.esp01_Task) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_Task) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_Task) refers to esp01_app.o(.rodata.str1.1) for .L.str.14
    esp01_app.o(.text.esp01_Task) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_Task) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.ARM.exidx.text.esp01_Task) refers to esp01_app.o(.text.esp01_Task) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_Reset) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_Reset) refers to esp01_app.o(.rodata.str1.1) for .L.str.34
    esp01_app.o(.text.esp01_Reset) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_Reset) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.text.esp01_Reset) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_Reset) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_Reset) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.text.esp01_Reset) refers to esp01_app.o(.text.esp01_Init) for esp01_Init
    esp01_app.o(.ARM.exidx.text.esp01_Reset) refers to esp01_app.o(.text.esp01_Reset) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_ReconnectWiFi) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_ReconnectWiFi) refers to esp01_app.o(.rodata.str1.1) for .L.str.8
    esp01_app.o(.text.esp01_ReconnectWiFi) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_ReconnectWiFi) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.text.esp01_ReconnectWiFi) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_ReconnectWiFi) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_ReconnectWiFi) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.ARM.exidx.text.esp01_ReconnectWiFi) refers to esp01_app.o(.text.esp01_ReconnectWiFi) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_CheckConnection) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_CheckConnection) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_CheckConnection) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_CheckConnection) refers to esp01_app.o(.rodata.str1.1) for .L.str.37
    esp01_app.o(.text.esp01_CheckConnection) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_CheckConnection) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.text.esp01_CheckConnection) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.ARM.exidx.text.esp01_CheckConnection) refers to esp01_app.o(.text.esp01_CheckConnection) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_SendLocationData) refers to esp01_app.o(.text.esp01_UploadGPSData) for esp01_UploadGPSData
    esp01_app.o(.ARM.exidx.text.esp01_SendLocationData) refers to esp01_app.o(.text.esp01_SendLocationData) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_EstablishTCPConnection) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.ARM.exidx.text.esp01_EstablishTCPConnection) refers to esp01_app.o(.text.esp01_EstablishTCPConnection) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_TryTCPWithIP) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.ARM.exidx.text.esp01_TryTCPWithIP) refers to esp01_app.o(.text.esp01_TryTCPWithIP) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_SetConnected) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.ARM.exidx.text.esp01_SetConnected) refers to esp01_app.o(.text.esp01_SetConnected) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_SetTCPConnected) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.ARM.exidx.text.esp01_SetTCPConnected) refers to esp01_app.o(.text.esp01_SetTCPConnected) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_ResetTCPState) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.ARM.exidx.text.esp01_ResetTCPState) refers to esp01_app.o(.text.esp01_ResetTCPState) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_ForceReset) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_ForceReset) refers to esp01_app.o(.rodata.str1.1) for .L.str.34
    esp01_app.o(.text.esp01_ForceReset) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_ForceReset) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.text.esp01_ForceReset) refers to usart.o(.bss.huart2) for huart2
    esp01_app.o(.text.esp01_ForceReset) refers to uart2_driver.o(.text.Uart2_Printf) for Uart2_Printf
    esp01_app.o(.text.esp01_ForceReset) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    esp01_app.o(.text.esp01_ForceReset) refers to esp01_app.o(.text.esp01_Init) for esp01_Init
    esp01_app.o(.ARM.exidx.text.esp01_ForceReset) refers to esp01_app.o(.text.esp01_ForceReset) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_NetworkDiagnostic) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_NetworkDiagnostic) refers to esp01_app.o(.rodata.str1.1) for .L.str.49
    esp01_app.o(.text.esp01_NetworkDiagnostic) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_NetworkDiagnostic) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.text.esp01_NetworkDiagnostic) refers to esp01_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_NetworkDiagnostic) refers to f2d.o(.text) for __aeabi_f2d
    esp01_app.o(.ARM.exidx.text.esp01_NetworkDiagnostic) refers to esp01_app.o(.text.esp01_NetworkDiagnostic) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_NetworkDiagnostics) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_NetworkDiagnostics) refers to esp01_app.o(.rodata.str1.1) for .L.str.49
    esp01_app.o(.text.esp01_NetworkDiagnostics) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_NetworkDiagnostics) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.text.esp01_NetworkDiagnostics) refers to esp01_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    esp01_app.o(.text.esp01_NetworkDiagnostics) refers to f2d.o(.text) for __aeabi_f2d
    esp01_app.o(.ARM.exidx.text.esp01_NetworkDiagnostics) refers to esp01_app.o(.text.esp01_NetworkDiagnostics) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_QuickTest) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_QuickTest) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.text.esp01_QuickTest) refers to esp01_app.o(.bss..L_MergedGlobals.64) for .L_MergedGlobals.64
    esp01_app.o(.ARM.exidx.text.esp01_QuickTest) refers to esp01_app.o(.text.esp01_QuickTest) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_SetDataSendReady) refers to usart.o(.bss.huart1) for huart1
    esp01_app.o(.text.esp01_SetDataSendReady) refers to usart.o(.text.my_printf) for my_printf
    esp01_app.o(.ARM.exidx.text.esp01_SetDataSendReady) refers to esp01_app.o(.text.esp01_SetDataSendReady) for [Anonymous Symbol]
    esp01_app.o(.text.esp01_GetDestinationCode) refers to strstr.o(.text) for strstr
    esp01_app.o(.ARM.exidx.text.esp01_GetDestinationCode) refers to esp01_app.o(.text.esp01_GetDestinationCode) for [Anonymous Symbol]
    gps_app.o(.text.GPS_Init) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.text.GPS_Init) refers to memseta.o(.text) for __aeabi_memclr4
    gps_app.o(.text.GPS_Init) refers to uart3_driver.o(.bss.uart3_ring_buffer) for uart3_ring_buffer
    gps_app.o(.text.GPS_Init) refers to uart3_driver.o(.bss.uart3_ring_buffer_input) for uart3_ring_buffer_input
    gps_app.o(.text.GPS_Init) refers to ringbuffer.o(.text.rt_ringbuffer_init) for rt_ringbuffer_init
    gps_app.o(.text.GPS_Init) refers to usart.o(.bss.huart3) for huart3
    gps_app.o(.text.GPS_Init) refers to uart3_driver.o(.bss.uart3_rx_dma_buffer) for uart3_rx_dma_buffer
    gps_app.o(.text.GPS_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    gps_app.o(.text.GPS_Init) refers to usart.o(.bss.hdma_usart3_rx) for hdma_usart3_rx
    gps_app.o(.ARM.exidx.text.GPS_Init) refers to gps_app.o(.text.GPS_Init) for [Anonymous Symbol]
    gps_app.o(.text.clrStruct) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.text.clrStruct) refers to memseta.o(.text) for __aeabi_memclr4
    gps_app.o(.ARM.exidx.text.clrStruct) refers to gps_app.o(.text.clrStruct) for [Anonymous Symbol]
    gps_app.o(.text.GPS_Task) refers to uart3_driver.o(.bss.uart3_ring_buffer) for uart3_ring_buffer
    gps_app.o(.text.GPS_Task) refers to ringbuffer.o(.text.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    gps_app.o(.text.GPS_Task) refers to uart3_driver.o(.bss.uart3_data_buffer) for uart3_data_buffer
    gps_app.o(.text.GPS_Task) refers to ringbuffer.o(.text.rt_ringbuffer_get) for rt_ringbuffer_get
    gps_app.o(.text.GPS_Task) refers to gps_app.o(.rodata.str1.1) for .L.str
    gps_app.o(.text.GPS_Task) refers to strstr.o(.text) for strstr
    gps_app.o(.text.GPS_Task) refers to gps_app.o(.text.parseGpsBuffer) for parseGpsBuffer
    gps_app.o(.text.GPS_Task) refers to memseta.o(.text) for __aeabi_memclr
    gps_app.o(.ARM.exidx.text.GPS_Task) refers to gps_app.o(.text.GPS_Task) for [Anonymous Symbol]
    gps_app.o(.text.parseGpsBuffer) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.text.parseGpsBuffer) refers to uart3_driver.o(.bss.uart3_data_buffer) for uart3_data_buffer
    gps_app.o(.text.parseGpsBuffer) refers to gps_app.o(.rodata.str1.1) for .L.str
    gps_app.o(.text.parseGpsBuffer) refers to strstr.o(.text) for strstr
    gps_app.o(.text.parseGpsBuffer) refers to strchr.o(.text) for strchr
    gps_app.o(.text.parseGpsBuffer) refers to memcpya.o(.text) for __aeabi_memcpy
    gps_app.o(.text.parseGpsBuffer) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    gps_app.o(.text.parseGpsBuffer) refers to ddiv.o(.text) for __aeabi_ddiv
    gps_app.o(.text.parseGpsBuffer) refers to d2f.o(.text) for __aeabi_d2f
    gps_app.o(.text.parseGpsBuffer) refers to gps_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    gps_app.o(.ARM.exidx.text.parseGpsBuffer) refers to gps_app.o(.text.parseGpsBuffer) for [Anonymous Symbol]
    gps_app.o(.text.printGpsBuffer) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.text.printGpsBuffer) refers to gps_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    gps_app.o(.text.printGpsBuffer) refers to f2d.o(.text) for __aeabi_f2d
    gps_app.o(.text.printGpsBuffer) refers to usart.o(.bss.huart1) for huart1
    gps_app.o(.text.printGpsBuffer) refers to usart.o(.text.my_printf) for my_printf
    gps_app.o(.ARM.exidx.text.printGpsBuffer) refers to gps_app.o(.text.printGpsBuffer) for [Anonymous Symbol]
    gps_app.o(.text.GPS_LockPosition) refers to gps_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    gps_app.o(.text.GPS_LockPosition) refers to f2d.o(.text) for __aeabi_f2d
    gps_app.o(.text.GPS_LockPosition) refers to usart.o(.bss.huart1) for huart1
    gps_app.o(.text.GPS_LockPosition) refers to usart.o(.text.my_printf) for my_printf
    gps_app.o(.ARM.exidx.text.GPS_LockPosition) refers to gps_app.o(.text.GPS_LockPosition) for [Anonymous Symbol]
    gps_app.o(.text.GPS_UnlockPosition) refers to gps_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    gps_app.o(.text.GPS_UnlockPosition) refers to usart.o(.bss.huart1) for huart1
    gps_app.o(.text.GPS_UnlockPosition) refers to usart.o(.text.my_printf) for my_printf
    gps_app.o(.ARM.exidx.text.GPS_UnlockPosition) refers to gps_app.o(.text.GPS_UnlockPosition) for [Anonymous Symbol]
    gps_app.o(.text.GPS_SetLockedPosition) refers to gps_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    gps_app.o(.text.GPS_SetLockedPosition) refers to f2d.o(.text) for __aeabi_f2d
    gps_app.o(.text.GPS_SetLockedPosition) refers to usart.o(.bss.huart1) for huart1
    gps_app.o(.text.GPS_SetLockedPosition) refers to usart.o(.text.my_printf) for my_printf
    gps_app.o(.ARM.exidx.text.GPS_SetLockedPosition) refers to gps_app.o(.text.GPS_SetLockedPosition) for [Anonymous Symbol]
    gps_app.o(.text.GPS_ManualUpload) refers to usart.o(.bss.huart1) for huart1
    gps_app.o(.text.GPS_ManualUpload) refers to usart.o(.text.my_printf) for my_printf
    gps_app.o(.text.GPS_ManualUpload) refers to gps_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    gps_app.o(.text.GPS_ManualUpload) refers to f2d.o(.text) for __aeabi_f2d
    gps_app.o(.text.GPS_ManualUpload) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.ARM.exidx.text.GPS_ManualUpload) refers to gps_app.o(.text.GPS_ManualUpload) for [Anonymous Symbol]
    gps_app.o(.text.GPS_SetCustomLocationAndUpload) refers to gps_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    gps_app.o(.text.GPS_SetCustomLocationAndUpload) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.text.GPS_SetCustomLocationAndUpload) refers to f2d.o(.text) for __aeabi_f2d
    gps_app.o(.text.GPS_SetCustomLocationAndUpload) refers to usart.o(.bss.huart1) for huart1
    gps_app.o(.text.GPS_SetCustomLocationAndUpload) refers to gps_app.o(.rodata.str1.1) for .L.str.13
    gps_app.o(.text.GPS_SetCustomLocationAndUpload) refers to usart.o(.text.my_printf) for my_printf
    gps_app.o(.text.GPS_SetCustomLocationAndUpload) refers to gps_app.o(.text.GPS_ManualUpload) for GPS_ManualUpload
    gps_app.o(.ARM.exidx.text.GPS_SetCustomLocationAndUpload) refers to gps_app.o(.text.GPS_SetCustomLocationAndUpload) for [Anonymous Symbol]
    gps_app.o(.text.GPS_PrintUploadStatus) refers to usart.o(.bss.huart1) for huart1
    gps_app.o(.text.GPS_PrintUploadStatus) refers to usart.o(.text.my_printf) for my_printf
    gps_app.o(.text.GPS_PrintUploadStatus) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.text.GPS_PrintUploadStatus) refers to gps_app.o(.rodata.str1.1) for .L.str.16
    gps_app.o(.text.GPS_PrintUploadStatus) refers to gps_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    gps_app.o(.text.GPS_PrintUploadStatus) refers to f2d.o(.text) for __aeabi_f2d
    gps_app.o(.ARM.exidx.text.GPS_PrintUploadStatus) refers to gps_app.o(.text.GPS_PrintUploadStatus) for [Anonymous Symbol]
    gps_app.o(.text.GPS_Test_SimulateData) refers to usart.o(.bss.huart1) for huart1
    gps_app.o(.text.GPS_Test_SimulateData) refers to usart.o(.text.my_printf) for my_printf
    gps_app.o(.text.GPS_Test_SimulateData) refers to gps_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    gps_app.o(.text.GPS_Test_SimulateData) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.ARM.exidx.text.GPS_Test_SimulateData) refers to gps_app.o(.text.GPS_Test_SimulateData) for [Anonymous Symbol]
    gps_app.o(.ARM.exidx.text.GPS_Virtual_Init) refers to gps_app.o(.text.GPS_Virtual_Init) for [Anonymous Symbol]
    gps_app.o(.text.GPS_Virtual_SetLocation) refers to gps_app.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    gps_app.o(.text.GPS_Virtual_SetLocation) refers to gps_app.o(.bss.Save_Data) for Save_Data
    gps_app.o(.text.GPS_Virtual_SetLocation) refers to f2d.o(.text) for __aeabi_f2d
    gps_app.o(.text.GPS_Virtual_SetLocation) refers to usart.o(.bss.huart1) for huart1
    gps_app.o(.text.GPS_Virtual_SetLocation) refers to gps_app.o(.rodata.str1.1) for .L.str.13
    gps_app.o(.text.GPS_Virtual_SetLocation) refers to usart.o(.text.my_printf) for my_printf
    gps_app.o(.text.GPS_Virtual_SetLocation) refers to gps_app.o(.text.GPS_ManualUpload) for GPS_ManualUpload
    gps_app.o(.ARM.exidx.text.GPS_Virtual_SetLocation) refers to gps_app.o(.text.GPS_Virtual_SetLocation) for [Anonymous Symbol]
    gps_app.o(.ARM.exidx.text.GPS_Virtual_EnableMovement) refers to gps_app.o(.text.GPS_Virtual_EnableMovement) for [Anonymous Symbol]
    gps_app.o(.ARM.exidx.text.GPS_Virtual_GenerateData) refers to gps_app.o(.text.GPS_Virtual_GenerateData) for [Anonymous Symbol]
    gps_app.o(.text.GPS_UploadToAMap) refers to gps_app.o(.text.GPS_ManualUpload) for GPS_ManualUpload
    gps_app.o(.ARM.exidx.text.GPS_UploadToAMap) refers to gps_app.o(.text.GPS_UploadToAMap) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_Init) refers to navigation_app.o(.bss.nav_state) for nav_state
    navigation_app.o(.text.Navigation_Init) refers to navigation_app.o(.bss.current_navigation) for current_navigation
    navigation_app.o(.text.Navigation_Init) refers to memseta.o(.text) for __aeabi_memclr4
    navigation_app.o(.text.Navigation_Init) refers to usart.o(.bss.huart1) for huart1
    navigation_app.o(.text.Navigation_Init) refers to usart.o(.text.my_printf) for my_printf
    navigation_app.o(.ARM.exidx.text.Navigation_Init) refers to navigation_app.o(.text.Navigation_Init) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_Task) refers to navigation_app.o(.bss.nav_state) for nav_state
    navigation_app.o(.text.Navigation_Task) refers to navigation_app.o(.bss.current_navigation) for current_navigation
    navigation_app.o(.text.Navigation_Task) refers to usart.o(.bss.huart1) for huart1
    navigation_app.o(.text.Navigation_Task) refers to usart.o(.text.my_printf) for my_printf
    navigation_app.o(.ARM.exidx.text.Navigation_Task) refers to navigation_app.o(.text.Navigation_Task) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_StartNavigation) refers to navigation_app.o(.bss.nav_state) for nav_state
    navigation_app.o(.text.Navigation_StartNavigation) refers to usart.o(.bss.huart1) for huart1
    navigation_app.o(.text.Navigation_StartNavigation) refers to usart.o(.text.my_printf) for my_printf
    navigation_app.o(.text.Navigation_StartNavigation) refers to navigation_app.o(.bss.current_navigation) for current_navigation
    navigation_app.o(.text.Navigation_StartNavigation) refers to navigation_app.o(.rodata.str1.1) for .L.str.19
    navigation_app.o(.text.Navigation_StartNavigation) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    navigation_app.o(.text.Navigation_StartNavigation) refers to navigation_app.o(.text.Navigation_FindDestination) for Navigation_FindDestination
    navigation_app.o(.text.Navigation_StartNavigation) refers to esp01_app.o(.text.esp01_GetRealLocation) for esp01_GetRealLocation
    navigation_app.o(.text.Navigation_StartNavigation) refers to f2d.o(.text) for __aeabi_f2d
    navigation_app.o(.text.Navigation_StartNavigation) refers to memcpya.o(.text) for __aeabi_memcpy4
    navigation_app.o(.text.Navigation_StartNavigation) refers to navigation_app.o(.text.Navigation_PlanRoute) for Navigation_PlanRoute
    navigation_app.o(.text.Navigation_StartNavigation) refers to tft_app.o(.text.tft_SetMapDestination) for tft_SetMapDestination
    navigation_app.o(.text.Navigation_StartNavigation) refers to tft_app.o(.text.tft_UpdateMapPosition) for tft_UpdateMapPosition
    navigation_app.o(.text.Navigation_StartNavigation) refers to memcmp.o(.text) for memcmp
    navigation_app.o(.text.Navigation_StartNavigation) refers to esp01_app.o(.text.esp01_UploadNavigationCommand) for esp01_UploadNavigationCommand
    navigation_app.o(.ARM.exidx.text.Navigation_StartNavigation) refers to navigation_app.o(.text.Navigation_StartNavigation) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_StopNavigation) refers to navigation_app.o(.bss.current_navigation) for current_navigation
    navigation_app.o(.text.Navigation_StopNavigation) refers to navigation_app.o(.bss.nav_state) for nav_state
    navigation_app.o(.text.Navigation_StopNavigation) refers to usart.o(.bss.huart1) for huart1
    navigation_app.o(.text.Navigation_StopNavigation) refers to navigation_app.o(.rodata.str1.1) for .L.str.19
    navigation_app.o(.text.Navigation_StopNavigation) refers to usart.o(.text.my_printf) for my_printf
    navigation_app.o(.ARM.exidx.text.Navigation_StopNavigation) refers to navigation_app.o(.text.Navigation_StopNavigation) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_FindDestination) refers to navigation_app.o(.rodata.destinations) for destinations
    navigation_app.o(.text.Navigation_FindDestination) refers to strcmp.o(.text) for strcmp
    navigation_app.o(.text.Navigation_FindDestination) refers to memcpya.o(.text) for __aeabi_memcpy4
    navigation_app.o(.ARM.exidx.text.Navigation_FindDestination) refers to navigation_app.o(.text.Navigation_FindDestination) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_PlanRoute) refers to navigation_app.o(.bss.current_navigation) for current_navigation
    navigation_app.o(.text.Navigation_PlanRoute) refers to navigation_app.o(.text.Navigation_CalculateDistance) for Navigation_CalculateDistance
    navigation_app.o(.text.Navigation_PlanRoute) refers to navigation_app.o(.text.Navigation_CalculateBearing) for Navigation_CalculateBearing
    navigation_app.o(.text.Navigation_PlanRoute) refers to f2d.o(.text) for __aeabi_f2d
    navigation_app.o(.text.Navigation_PlanRoute) refers to printfa.o(i.__0snprintf) for __2snprintf
    navigation_app.o(.ARM.exidx.text.Navigation_PlanRoute) refers to navigation_app.o(.text.Navigation_PlanRoute) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_ProcessCommand) refers to strncmp.o(.text) for strncmp
    navigation_app.o(.text.Navigation_ProcessCommand) refers to strcmp.o(.text) for strcmp
    navigation_app.o(.text.Navigation_ProcessCommand) refers to navigation_app.o(.rodata.destinations) for destinations
    navigation_app.o(.text.Navigation_ProcessCommand) refers to usart.o(.bss.huart1) for huart1
    navigation_app.o(.text.Navigation_ProcessCommand) refers to usart.o(.text.my_printf) for my_printf
    navigation_app.o(.text.Navigation_ProcessCommand) refers to navigation_app.o(.rodata.str1.1) for .L.str.32
    navigation_app.o(.text.Navigation_ProcessCommand) refers to navigation_app.o(.text.Navigation_StartNavigation) for Navigation_StartNavigation
    navigation_app.o(.text.Navigation_ProcessCommand) refers to navigation_app.o(.bss.current_navigation) for current_navigation
    navigation_app.o(.text.Navigation_ProcessCommand) refers to navigation_app.o(.bss.nav_state) for nav_state
    navigation_app.o(.ARM.exidx.text.Navigation_ProcessCommand) refers to navigation_app.o(.text.Navigation_ProcessCommand) for [Anonymous Symbol]
    navigation_app.o(.ARM.exidx.text.Navigation_PrintDestinations) refers to navigation_app.o(.text.Navigation_PrintDestinations) for [Anonymous Symbol]
    navigation_app.o(.ARM.exidx.text.Navigation_PrintStatus) refers to navigation_app.o(.text.Navigation_PrintStatus) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_CalculateDistance) refers to f2d.o(.text) for __aeabi_f2d
    navigation_app.o(.text.Navigation_CalculateDistance) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    navigation_app.o(.text.Navigation_CalculateDistance) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    navigation_app.o(.text.Navigation_CalculateDistance) refers to dmul.o(.text) for __aeabi_dmul
    navigation_app.o(.text.Navigation_CalculateDistance) refers to dadd.o(.text) for __aeabi_dadd
    navigation_app.o(.text.Navigation_CalculateDistance) refers to d2f.o(.text) for __aeabi_d2f
    navigation_app.o(.text.Navigation_CalculateDistance) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    navigation_app.o(.text.Navigation_CalculateDistance) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    navigation_app.o(.ARM.exidx.text.Navigation_CalculateDistance) refers to navigation_app.o(.text.Navigation_CalculateDistance) for [Anonymous Symbol]
    navigation_app.o(.text.Navigation_CalculateBearing) refers to f2d.o(.text) for __aeabi_f2d
    navigation_app.o(.text.Navigation_CalculateBearing) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    navigation_app.o(.text.Navigation_CalculateBearing) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    navigation_app.o(.text.Navigation_CalculateBearing) refers to dmul.o(.text) for __aeabi_dmul
    navigation_app.o(.text.Navigation_CalculateBearing) refers to d2f.o(.text) for __aeabi_d2f
    navigation_app.o(.text.Navigation_CalculateBearing) refers to dadd.o(.text) for __aeabi_dsub
    navigation_app.o(.text.Navigation_CalculateBearing) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    navigation_app.o(.text.Navigation_CalculateBearing) refers to ddiv.o(.text) for __aeabi_ddiv
    navigation_app.o(.text.Navigation_CalculateBearing) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    navigation_app.o(.ARM.exidx.text.Navigation_CalculateBearing) refers to navigation_app.o(.text.Navigation_CalculateBearing) for [Anonymous Symbol]
    uart6_app.o(.text.Uart6_Init) refers to uart6_driver.o(.bss.uart6_ring_buffer) for uart6_ring_buffer
    uart6_app.o(.text.Uart6_Init) refers to uart6_driver.o(.bss.uart6_ring_buffer_input) for uart6_ring_buffer_input
    uart6_app.o(.text.Uart6_Init) refers to ringbuffer.o(.text.rt_ringbuffer_init) for rt_ringbuffer_init
    uart6_app.o(.text.Uart6_Init) refers to usart.o(.bss.huart6) for huart6
    uart6_app.o(.text.Uart6_Init) refers to uart6_driver.o(.bss.uart6_rx_dma_buffer) for uart6_rx_dma_buffer
    uart6_app.o(.text.Uart6_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart6_app.o(.text.Uart6_Init) refers to usart.o(.bss.hdma_usart6_rx) for hdma_usart6_rx
    uart6_app.o(.text.Uart6_Init) refers to usart.o(.bss.huart1) for huart1
    uart6_app.o(.text.Uart6_Init) refers to usart.o(.text.my_printf) for my_printf
    uart6_app.o(.ARM.exidx.text.Uart6_Init) refers to uart6_app.o(.text.Uart6_Init) for [Anonymous Symbol]
    uart6_app.o(.text.Uart6_Task) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    uart6_app.o(.text.Uart6_Task) refers to uart6_app.o(.bss.Uart6_Task.last_heartbeat) for Uart6_Task.last_heartbeat
    uart6_app.o(.text.Uart6_Task) refers to usart.o(.bss.huart1) for huart1
    uart6_app.o(.text.Uart6_Task) refers to usart.o(.text.my_printf) for my_printf
    uart6_app.o(.ARM.exidx.text.Uart6_Task) refers to uart6_app.o(.text.Uart6_Task) for [Anonymous Symbol]
    uart6_app.o(.text.Uart6_RxCallback) refers to uart6_driver.o(.bss.uart6_rx_dma_buffer) for uart6_rx_dma_buffer
    uart6_app.o(.text.Uart6_RxCallback) refers to memcpya.o(.text) for __aeabi_memcpy
    uart6_app.o(.text.Uart6_RxCallback) refers to memcmp.o(.text) for memcmp
    uart6_app.o(.text.Uart6_RxCallback) refers to usart.o(.bss.huart6) for huart6
    uart6_app.o(.text.Uart6_RxCallback) refers to usart.o(.text.my_printf) for my_printf
    uart6_app.o(.text.Uart6_RxCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart6_app.o(.ARM.exidx.text.Uart6_RxCallback) refers to uart6_app.o(.text.Uart6_RxCallback) for [Anonymous Symbol]
    tft_app.o(.text.tft_BasicTest) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.tft_BasicTest) refers to tft_app.o(.rodata.str1.1) for .L.str
    tft_app.o(.text.tft_BasicTest) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.text.tft_BasicTest) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tft_app.o(.text.tft_BasicTest) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    tft_app.o(.text.tft_BasicTest) refers to lcd_display_hal.o(.text.LCD_Fill) for LCD_Fill
    tft_app.o(.ARM.exidx.text.tft_BasicTest) refers to tft_app.o(.text.tft_BasicTest) for [Anonymous Symbol]
    tft_app.o(.text.tft_HardwareDiagnose) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.tft_HardwareDiagnose) refers to tft_app.o(.rodata.str1.1) for .L.str.11
    tft_app.o(.text.tft_HardwareDiagnose) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.text.tft_HardwareDiagnose) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tft_app.o(.text.tft_HardwareDiagnose) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    tft_app.o(.text.tft_HardwareDiagnose) refers to lcd_init_hal.o(.text.LCD_WR_REG) for LCD_WR_REG
    tft_app.o(.text.tft_HardwareDiagnose) refers to lcd_init_hal.o(.text.LCD_WR_DATA8) for LCD_WR_DATA8
    tft_app.o(.ARM.exidx.text.tft_HardwareDiagnose) refers to tft_app.o(.text.tft_HardwareDiagnose) for [Anonymous Symbol]
    tft_app.o(.text.tft_DeepDiagnose) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.tft_DeepDiagnose) refers to tft_app.o(.rodata.str1.1) for .L.str.19
    tft_app.o(.text.tft_DeepDiagnose) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.text.tft_DeepDiagnose) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    tft_app.o(.text.tft_DeepDiagnose) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    tft_app.o(.text.tft_DeepDiagnose) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    tft_app.o(.text.tft_DeepDiagnose) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    tft_app.o(.text.tft_DeepDiagnose) refers to lcd_init_hal.o(.text.LCD_WR_REG) for LCD_WR_REG
    tft_app.o(.ARM.exidx.text.tft_DeepDiagnose) refers to tft_app.o(.text.tft_DeepDiagnose) for [Anonymous Symbol]
    tft_app.o(.text.tft_HardwareConnectionTest) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.tft_HardwareConnectionTest) refers to tft_app.o(.rodata.str1.1) for .L.str.29
    tft_app.o(.text.tft_HardwareConnectionTest) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.text.tft_HardwareConnectionTest) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tft_app.o(.text.tft_HardwareConnectionTest) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    tft_app.o(.ARM.exidx.text.tft_HardwareConnectionTest) refers to tft_app.o(.text.tft_HardwareConnectionTest) for [Anonymous Symbol]
    tft_app.o(.text.tft_SimpleTest) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.tft_SimpleTest) refers to tft_app.o(.rodata.str1.1) for .L.str.44
    tft_app.o(.text.tft_SimpleTest) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.text.tft_SimpleTest) refers to tft_app.o(.text.tft_HardwareConnectionTest) for tft_HardwareConnectionTest
    tft_app.o(.text.tft_SimpleTest) refers to lcd_init_hal.o(.text.LCD_Init) for LCD_Init
    tft_app.o(.text.tft_SimpleTest) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    tft_app.o(.text.tft_SimpleTest) refers to lcd_display_hal.o(.text.LCD_Fill) for LCD_Fill
    tft_app.o(.ARM.exidx.text.tft_SimpleTest) refers to tft_app.o(.text.tft_SimpleTest) for [Anonymous Symbol]
    tft_app.o(.text.tft_ExtremeDiagnose) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.tft_ExtremeDiagnose) refers to tft_app.o(.rodata.str1.1) for .L.str.50
    tft_app.o(.text.tft_ExtremeDiagnose) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.text.tft_ExtremeDiagnose) refers to lcd_init_hal.o(.text.LCD_GPIO_Init) for LCD_GPIO_Init
    tft_app.o(.text.tft_ExtremeDiagnose) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tft_app.o(.text.tft_ExtremeDiagnose) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    tft_app.o(.text.tft_ExtremeDiagnose) refers to tft_app.o(.text.tft_TryST7735Init) for tft_TryST7735Init
    tft_app.o(.text.tft_ExtremeDiagnose) refers to tft_app.o(.text.tft_TryST7789Init) for tft_TryST7789Init
    tft_app.o(.text.tft_ExtremeDiagnose) refers to tft_app.o(.text.tft_TryILI9163Init) for tft_TryILI9163Init
    tft_app.o(.text.tft_ExtremeDiagnose) refers to lcd_init_hal.o(.text.LCD_Init) for LCD_Init
    tft_app.o(.ARM.exidx.text.tft_ExtremeDiagnose) refers to tft_app.o(.text.tft_ExtremeDiagnose) for [Anonymous Symbol]
    tft_app.o(.text.tft_TryST7735Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tft_app.o(.text.tft_TryST7735Init) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    tft_app.o(.text.tft_TryST7735Init) refers to lcd_init_hal.o(.text.LCD_WR_REG) for LCD_WR_REG
    tft_app.o(.text.tft_TryST7735Init) refers to lcd_init_hal.o(.text.LCD_WR_DATA8) for LCD_WR_DATA8
    tft_app.o(.text.tft_TryST7735Init) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    tft_app.o(.text.tft_TryST7735Init) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.tft_TryST7735Init) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.ARM.exidx.text.tft_TryST7735Init) refers to tft_app.o(.text.tft_TryST7735Init) for [Anonymous Symbol]
    tft_app.o(.text.tft_TryST7789Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tft_app.o(.text.tft_TryST7789Init) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    tft_app.o(.text.tft_TryST7789Init) refers to lcd_init_hal.o(.text.LCD_WR_REG) for LCD_WR_REG
    tft_app.o(.text.tft_TryST7789Init) refers to lcd_init_hal.o(.text.LCD_WR_DATA8) for LCD_WR_DATA8
    tft_app.o(.text.tft_TryST7789Init) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    tft_app.o(.text.tft_TryST7789Init) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    tft_app.o(.text.tft_TryST7789Init) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.tft_TryST7789Init) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.ARM.exidx.text.tft_TryST7789Init) refers to tft_app.o(.text.tft_TryST7789Init) for [Anonymous Symbol]
    tft_app.o(.text.tft_TryILI9163Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tft_app.o(.text.tft_TryILI9163Init) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    tft_app.o(.text.tft_TryILI9163Init) refers to lcd_init_hal.o(.text.LCD_WR_REG) for LCD_WR_REG
    tft_app.o(.text.tft_TryILI9163Init) refers to lcd_init_hal.o(.text.LCD_WR_DATA8) for LCD_WR_DATA8
    tft_app.o(.text.tft_TryILI9163Init) refers to lcd_init_hal.o(.text.LCD_Address_Set) for LCD_Address_Set
    tft_app.o(.text.tft_TryILI9163Init) refers to lcd_init_hal.o(.text.LCD_WR_DATA) for LCD_WR_DATA
    tft_app.o(.text.tft_TryILI9163Init) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.tft_TryILI9163Init) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.ARM.exidx.text.tft_TryILI9163Init) refers to tft_app.o(.text.tft_TryILI9163Init) for [Anonymous Symbol]
    tft_app.o(.text.tft_Init) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.tft_Init) refers to tft_app.o(.rodata.str1.1) for .L.str.64
    tft_app.o(.text.tft_Init) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.text.tft_Init) refers to tft_app.o(.text.LCD_QuickPinTest) for LCD_QuickPinTest
    tft_app.o(.text.tft_Init) refers to tft_app.o(.text.LCD_SimpleSPITest) for LCD_SimpleSPITest
    tft_app.o(.text.tft_Init) refers to tft_app.o(.text.tft_HardwareDiagnose) for tft_HardwareDiagnose
    tft_app.o(.text.tft_Init) refers to lcd_init_hal.o(.text.LCD_Init) for LCD_Init
    tft_app.o(.text.tft_Init) refers to tft_app.o(.text.tft_BasicTest) for tft_BasicTest
    tft_app.o(.text.tft_Init) refers to lcd_display_hal.o(.text.LCD_Fill) for LCD_Fill
    tft_app.o(.text.tft_Init) refers to lcd_display_hal.o(.text.LCD_ShowString) for LCD_ShowString
    tft_app.o(.text.tft_Init) refers to lcd_display_hal.o(.text.LCD_ShowIntNum) for LCD_ShowIntNum
    tft_app.o(.text.tft_Init) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    tft_app.o(.text.tft_Init) refers to lcd_map_display.o(.text.LCD_Map_Init) for LCD_Map_Init
    tft_app.o(.text.tft_Init) refers to lcd_map_display.o(.text.LCD_Map_UpdatePosition) for LCD_Map_UpdatePosition
    tft_app.o(.text.tft_Init) refers to lcd_map_display.o(.text.LCD_Map_SetDestination) for LCD_Map_SetDestination
    tft_app.o(.text.tft_Init) refers to lcd_map_display.o(.text.LCD_Map_Task) for LCD_Map_Task
    tft_app.o(.ARM.exidx.text.tft_Init) refers to tft_app.o(.text.tft_Init) for [Anonymous Symbol]
    tft_app.o(.text.LCD_QuickPinTest) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.LCD_QuickPinTest) refers to tft_app.o(.rodata.str1.1) for .L.str.90
    tft_app.o(.text.LCD_QuickPinTest) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.text.LCD_QuickPinTest) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tft_app.o(.text.LCD_QuickPinTest) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tft_app.o(.text.LCD_QuickPinTest) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    tft_app.o(.ARM.exidx.text.LCD_QuickPinTest) refers to tft_app.o(.text.LCD_QuickPinTest) for [Anonymous Symbol]
    tft_app.o(.text.LCD_SimpleSPITest) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.LCD_SimpleSPITest) refers to tft_app.o(.rodata.str1.1) for .L.str.103
    tft_app.o(.text.LCD_SimpleSPITest) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.text.LCD_SimpleSPITest) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tft_app.o(.text.LCD_SimpleSPITest) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    tft_app.o(.ARM.exidx.text.LCD_SimpleSPITest) refers to tft_app.o(.text.LCD_SimpleSPITest) for [Anonymous Symbol]
    tft_app.o(.text.tft_ShowWandaMap) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.tft_ShowWandaMap) refers to tft_app.o(.rodata.str1.1) for .L.str.108
    tft_app.o(.text.tft_ShowWandaMap) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.text.tft_ShowWandaMap) refers to lcd_map_display.o(.text.LCD_Map_Init) for LCD_Map_Init
    tft_app.o(.text.tft_ShowWandaMap) refers to lcd_map_display.o(.text.LCD_Map_UpdatePosition) for LCD_Map_UpdatePosition
    tft_app.o(.text.tft_ShowWandaMap) refers to lcd_map_display.o(.text.LCD_Map_SetDestination) for LCD_Map_SetDestination
    tft_app.o(.text.tft_ShowWandaMap) refers to lcd_map_display.o(.text.LCD_Map_Task) for LCD_Map_Task
    tft_app.o(.ARM.exidx.text.tft_ShowWandaMap) refers to tft_app.o(.text.tft_ShowWandaMap) for [Anonymous Symbol]
    tft_app.o(.text.tft_PinConfigTest) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.tft_PinConfigTest) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.text.tft_PinConfigTest) refers to tft_app.o(.rodata.str1.1) for .L.str.84
    tft_app.o(.text.tft_PinConfigTest) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    tft_app.o(.text.tft_PinConfigTest) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    tft_app.o(.text.tft_PinConfigTest) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    tft_app.o(.ARM.exidx.text.tft_PinConfigTest) refers to tft_app.o(.text.tft_PinConfigTest) for [Anonymous Symbol]
    tft_app.o(.text.tft_UpdateMapPosition) refers to lcd_map_display.o(.text.LCD_Map_UpdatePosition) for LCD_Map_UpdatePosition
    tft_app.o(.text.tft_UpdateMapPosition) refers to f2d.o(.text) for __aeabi_f2d
    tft_app.o(.text.tft_UpdateMapPosition) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.tft_UpdateMapPosition) refers to tft_app.o(.rodata.str1.1) for .L.str.114
    tft_app.o(.text.tft_UpdateMapPosition) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.ARM.exidx.text.tft_UpdateMapPosition) refers to tft_app.o(.text.tft_UpdateMapPosition) for [Anonymous Symbol]
    tft_app.o(.text.tft_SetMapDestination) refers to lcd_map_display.o(.text.LCD_Map_SetDestination) for LCD_Map_SetDestination
    tft_app.o(.text.tft_SetMapDestination) refers to f2d.o(.text) for __aeabi_f2d
    tft_app.o(.text.tft_SetMapDestination) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.tft_SetMapDestination) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.ARM.exidx.text.tft_SetMapDestination) refers to tft_app.o(.text.tft_SetMapDestination) for [Anonymous Symbol]
    tft_app.o(.text.tft_Task) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    tft_app.o(.text.tft_Task) refers to tft_app.o(.bss..L_MergedGlobals.121) for .L_MergedGlobals.121
    tft_app.o(.text.tft_Task) refers to lcd_map_display.o(.text.LCD_Map_Task) for LCD_Map_Task
    tft_app.o(.text.tft_Task) refers to tft_app.o(.data..L_MergedGlobals) for .L_MergedGlobals
    tft_app.o(.text.tft_Task) refers to lcd_map_display.o(.text.LCD_Map_UpdatePosition) for LCD_Map_UpdatePosition
    tft_app.o(.text.tft_Task) refers to f2d.o(.text) for __aeabi_f2d
    tft_app.o(.text.tft_Task) refers to usart.o(.bss.huart1) for huart1
    tft_app.o(.text.tft_Task) refers to tft_app.o(.rodata.str1.1) for .L.str.114
    tft_app.o(.text.tft_Task) refers to usart.o(.text.my_printf) for my_printf
    tft_app.o(.ARM.exidx.text.tft_Task) refers to tft_app.o(.text.tft_Task) for [Anonymous Symbol]
    tft_app.o(.text.tft_DisplaySystemInfo) refers to lcd_display_hal.o(.text.LCD_Fill) for LCD_Fill
    tft_app.o(.text.tft_DisplaySystemInfo) refers to lcd_display_hal.o(.text.LCD_ShowString) for LCD_ShowString
    tft_app.o(.text.tft_DisplaySystemInfo) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    tft_app.o(.text.tft_DisplaySystemInfo) refers to lcd_display_hal.o(.text.LCD_ShowIntNum) for LCD_ShowIntNum
    tft_app.o(.text.tft_DisplaySystemInfo) refers to lcd_display_hal.o(.text.LCD_ShowFloatNum1) for LCD_ShowFloatNum1
    tft_app.o(.text.tft_DisplaySystemInfo) refers to lcd_display_hal.o(.text.LCD_DrawRectangle) for LCD_DrawRectangle
    tft_app.o(.text.tft_DisplaySystemInfo) refers to lcd_display_hal.o(.text.Draw_Circle) for Draw_Circle
    tft_app.o(.ARM.exidx.text.tft_DisplaySystemInfo) refers to tft_app.o(.text.tft_DisplaySystemInfo) for [Anonymous Symbol]
    lcd_map_display.o(.text.LCD_Map_Init) refers to lcd_map_display.o(.bss.g_map_state) for g_map_state
    lcd_map_display.o(.text.LCD_Map_Init) refers to memseta.o(.text) for __aeabi_memclr4
    lcd_map_display.o(.text.LCD_Map_Init) refers to lcd_display_hal.o(.text.LCD_Fill) for LCD_Fill
    lcd_map_display.o(.text.LCD_Map_Init) refers to lcd_map_display.o(.rodata.str1.1) for .L.str
    lcd_map_display.o(.text.LCD_Map_Init) refers to lcd_display_hal.o(.text.LCD_ShowString) for LCD_ShowString
    lcd_map_display.o(.text.LCD_Map_Init) refers to lcd_display_hal.o(.text.LCD_DrawRectangle) for LCD_DrawRectangle
    lcd_map_display.o(.ARM.exidx.text.LCD_Map_Init) refers to lcd_map_display.o(.text.LCD_Map_Init) for [Anonymous Symbol]
    lcd_map_display.o(.text.LCD_Map_Clear) refers to lcd_display_hal.o(.text.LCD_Fill) for LCD_Fill
    lcd_map_display.o(.text.LCD_Map_Clear) refers to lcd_map_display.o(.rodata.str1.1) for .L.str
    lcd_map_display.o(.text.LCD_Map_Clear) refers to lcd_display_hal.o(.text.LCD_ShowString) for LCD_ShowString
    lcd_map_display.o(.text.LCD_Map_Clear) refers to lcd_display_hal.o(.text.LCD_DrawRectangle) for LCD_DrawRectangle
    lcd_map_display.o(.ARM.exidx.text.LCD_Map_Clear) refers to lcd_map_display.o(.text.LCD_Map_Clear) for [Anonymous Symbol]
    lcd_map_display.o(.text.LCD_Map_DrawGrid) refers to lcd_display_hal.o(.text.LCD_DrawPoint) for LCD_DrawPoint
    lcd_map_display.o(.text.LCD_Map_DrawGrid) refers to lcd_display_hal.o(.text.LCD_DrawLine) for LCD_DrawLine
    lcd_map_display.o(.text.LCD_Map_DrawGrid) refers to lcd_display_hal.o(.text.Draw_Circle) for Draw_Circle
    lcd_map_display.o(.text.LCD_Map_DrawGrid) refers to lcd_display_hal.o(.text.LCD_ShowString) for LCD_ShowString
    lcd_map_display.o(.ARM.exidx.text.LCD_Map_DrawGrid) refers to lcd_map_display.o(.text.LCD_Map_DrawGrid) for [Anonymous Symbol]
    lcd_map_display.o(.text.LCD_Map_UpdatePosition) refers to lcd_map_display.o(.bss.g_map_state) for g_map_state
    lcd_map_display.o(.ARM.exidx.text.LCD_Map_UpdatePosition) refers to lcd_map_display.o(.text.LCD_Map_UpdatePosition) for [Anonymous Symbol]
    lcd_map_display.o(.text.LCD_Map_SetDestination) refers to lcd_map_display.o(.bss.g_map_state) for g_map_state
    lcd_map_display.o(.text.LCD_Map_SetDestination) refers to lcd_map_display.o(.text.LCD_Map_CalculateRouteDistance) for LCD_Map_CalculateRouteDistance
    lcd_map_display.o(.text.LCD_Map_SetDestination) refers to lcd_map_display.o(.text.LCD_Map_CalculateBearing) for LCD_Map_CalculateBearing
    lcd_map_display.o(.ARM.exidx.text.LCD_Map_SetDestination) refers to lcd_map_display.o(.text.LCD_Map_SetDestination) for [Anonymous Symbol]
    lcd_map_display.o(.text.LCD_Map_CalculateRouteDistance) refers to f2d.o(.text) for __aeabi_f2d
    lcd_map_display.o(.text.LCD_Map_CalculateRouteDistance) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    lcd_map_display.o(.text.LCD_Map_CalculateRouteDistance) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    lcd_map_display.o(.text.LCD_Map_CalculateRouteDistance) refers to dmul.o(.text) for __aeabi_dmul
    lcd_map_display.o(.text.LCD_Map_CalculateRouteDistance) refers to dadd.o(.text) for __aeabi_dadd
    lcd_map_display.o(.text.LCD_Map_CalculateRouteDistance) refers to d2f.o(.text) for __aeabi_d2f
    lcd_map_display.o(.text.LCD_Map_CalculateRouteDistance) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    lcd_map_display.o(.text.LCD_Map_CalculateRouteDistance) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    lcd_map_display.o(.ARM.exidx.text.LCD_Map_CalculateRouteDistance) refers to lcd_map_display.o(.text.LCD_Map_CalculateRouteDistance) for [Anonymous Symbol]
    lcd_map_display.o(.text.LCD_Map_CalculateBearing) refers to f2d.o(.text) for __aeabi_f2d
    lcd_map_display.o(.text.LCD_Map_CalculateBearing) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    lcd_map_display.o(.text.LCD_Map_CalculateBearing) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    lcd_map_display.o(.text.LCD_Map_CalculateBearing) refers to dmul.o(.text) for __aeabi_dmul
    lcd_map_display.o(.text.LCD_Map_CalculateBearing) refers to d2f.o(.text) for __aeabi_d2f
    lcd_map_display.o(.text.LCD_Map_CalculateBearing) refers to dadd.o(.text) for __aeabi_dsub
    lcd_map_display.o(.text.LCD_Map_CalculateBearing) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    lcd_map_display.o(.text.LCD_Map_CalculateBearing) refers to ddiv.o(.text) for __aeabi_ddiv
    lcd_map_display.o(.text.LCD_Map_CalculateBearing) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    lcd_map_display.o(.ARM.exidx.text.LCD_Map_CalculateBearing) refers to lcd_map_display.o(.text.LCD_Map_CalculateBearing) for [Anonymous Symbol]
    lcd_map_display.o(.text.LCD_Map_GPS_To_Screen) refers to lcd_map_display.o(.bss.g_map_state) for g_map_state
    lcd_map_display.o(.ARM.exidx.text.LCD_Map_GPS_To_Screen) refers to lcd_map_display.o(.text.LCD_Map_GPS_To_Screen) for [Anonymous Symbol]
    lcd_map_display.o(.text.LCD_Map_DrawCurrentPosition) refers to lcd_map_display.o(.bss.g_map_state) for g_map_state
    lcd_map_display.o(.text.LCD_Map_DrawCurrentPosition) refers to lcd_display_hal.o(.text.Draw_Circle) for Draw_Circle
    lcd_map_display.o(.text.LCD_Map_DrawCurrentPosition) refers to lcd_display_hal.o(.text.LCD_Fill) for LCD_Fill
    lcd_map_display.o(.text.LCD_Map_DrawCurrentPosition) refers to f2d.o(.text) for __aeabi_f2d
    lcd_map_display.o(.text.LCD_Map_DrawCurrentPosition) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    lcd_map_display.o(.text.LCD_Map_DrawCurrentPosition) refers to dmul.o(.text) for __aeabi_dmul
    lcd_map_display.o(.text.LCD_Map_DrawCurrentPosition) refers to dfixi.o(.text) for __aeabi_d2iz
    lcd_map_display.o(.text.LCD_Map_DrawCurrentPosition) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    lcd_map_display.o(.text.LCD_Map_DrawCurrentPosition) refers to lcd_display_hal.o(.text.LCD_DrawLine) for LCD_DrawLine
    lcd_map_display.o(.ARM.exidx.text.LCD_Map_DrawCurrentPosition) refers to lcd_map_display.o(.text.LCD_Map_DrawCurrentPosition) for [Anonymous Symbol]
    lcd_map_display.o(.text.LCD_Map_DrawDestination) refers to lcd_map_display.o(.bss.g_map_state) for g_map_state
    lcd_map_display.o(.text.LCD_Map_DrawDestination) refers to lcd_display_hal.o(.text.LCD_Fill) for LCD_Fill
    lcd_map_display.o(.text.LCD_Map_DrawDestination) refers to lcd_display_hal.o(.text.LCD_DrawRectangle) for LCD_DrawRectangle
    lcd_map_display.o(.ARM.exidx.text.LCD_Map_DrawDestination) refers to lcd_map_display.o(.text.LCD_Map_DrawDestination) for [Anonymous Symbol]
    lcd_map_display.o(.text.LCD_Map_DrawRoute) refers to lcd_map_display.o(.bss.g_map_state) for g_map_state
    lcd_map_display.o(.text.LCD_Map_DrawRoute) refers to lcd_display_hal.o(.text.LCD_DrawLine) for LCD_DrawLine
    lcd_map_display.o(.text.LCD_Map_DrawRoute) refers to lcd_display_hal.o(.text.Draw_Circle) for Draw_Circle
    lcd_map_display.o(.ARM.exidx.text.LCD_Map_DrawRoute) refers to lcd_map_display.o(.text.LCD_Map_DrawRoute) for [Anonymous Symbol]
    lcd_map_display.o(.text.LCD_Map_CalculateDistance) refers to f2d.o(.text) for __aeabi_f2d
    lcd_map_display.o(.text.LCD_Map_CalculateDistance) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    lcd_map_display.o(.text.LCD_Map_CalculateDistance) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    lcd_map_display.o(.text.LCD_Map_CalculateDistance) refers to dmul.o(.text) for __aeabi_dmul
    lcd_map_display.o(.text.LCD_Map_CalculateDistance) refers to dadd.o(.text) for __aeabi_dadd
    lcd_map_display.o(.text.LCD_Map_CalculateDistance) refers to d2f.o(.text) for __aeabi_d2f
    lcd_map_display.o(.text.LCD_Map_CalculateDistance) refers to sqrt.o(i.__hardfp_sqrt) for __hardfp_sqrt
    lcd_map_display.o(.text.LCD_Map_CalculateDistance) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    lcd_map_display.o(.ARM.exidx.text.LCD_Map_CalculateDistance) refers to lcd_map_display.o(.text.LCD_Map_CalculateDistance) for [Anonymous Symbol]
    lcd_map_display.o(.text.LCD_Map_DisplayNavigationInstructions) refers to lcd_map_display.o(.bss.g_map_state) for g_map_state
    lcd_map_display.o(.text.LCD_Map_DisplayNavigationInstructions) refers to f2d.o(.text) for __aeabi_f2d
    lcd_map_display.o(.text.LCD_Map_DisplayNavigationInstructions) refers to lcd_map_display.o(.rodata.str1.1) for .L.str.3
    lcd_map_display.o(.text.LCD_Map_DisplayNavigationInstructions) refers to printfa.o(i.__0snprintf) for __2snprintf
    lcd_map_display.o(.text.LCD_Map_DisplayNavigationInstructions) refers to lcd_display_hal.o(.text.LCD_ShowString) for LCD_ShowString
    lcd_map_display.o(.text.LCD_Map_DisplayNavigationInstructions) refers to lcd_display_hal.o(.text.LCD_DrawLine) for LCD_DrawLine
    lcd_map_display.o(.text.LCD_Map_DisplayNavigationInstructions) refers to lcd_map_display.o(.text.LCD_Map_DrawNavigationStep) for LCD_Map_DrawNavigationStep
    lcd_map_display.o(.text.LCD_Map_DisplayNavigationInstructions) refers to lcd_display_hal.o(.text.Draw_Circle) for Draw_Circle
    lcd_map_display.o(.text.LCD_Map_DisplayNavigationInstructions) refers to lcd_map_display.o(.text.LCD_Map_DrawDirectionArrow) for LCD_Map_DrawDirectionArrow
    lcd_map_display.o(.ARM.exidx.text.LCD_Map_DisplayNavigationInstructions) refers to lcd_map_display.o(.text.LCD_Map_DisplayNavigationInstructions) for [Anonymous Symbol]
    lcd_map_display.o(.text.LCD_Map_DrawNavigationStep) refers to lcd_display_hal.o(.text.Draw_Circle) for Draw_Circle
    lcd_map_display.o(.text.LCD_Map_DrawNavigationStep) refers to lcd_map_display.o(.rodata.str1.1) for .L.str.19
    lcd_map_display.o(.text.LCD_Map_DrawNavigationStep) refers to printfa.o(i.__0snprintf) for __2snprintf
    lcd_map_display.o(.text.LCD_Map_DrawNavigationStep) refers to lcd_display_hal.o(.text.LCD_ShowString) for LCD_ShowString
    lcd_map_display.o(.text.LCD_Map_DrawNavigationStep) refers to lcd_display_hal.o(.text.LCD_DrawLine) for LCD_DrawLine
    lcd_map_display.o(.text.LCD_Map_DrawNavigationStep) refers to lcd_map_display.o(.text.LCD_Map_DrawDirectionArrow) for LCD_Map_DrawDirectionArrow
    lcd_map_display.o(.ARM.exidx.text.LCD_Map_DrawNavigationStep) refers to lcd_map_display.o(.text.LCD_Map_DrawNavigationStep) for [Anonymous Symbol]
    lcd_map_display.o(.text.LCD_Map_DrawDirectionArrow) refers to strstr.o(.text) for strstr
    lcd_map_display.o(.text.LCD_Map_DrawDirectionArrow) refers to lcd_display_hal.o(.text.LCD_DrawLine) for LCD_DrawLine
    lcd_map_display.o(.text.LCD_Map_DrawDirectionArrow) refers to lcd_display_hal.o(.text.Draw_Circle) for Draw_Circle
    lcd_map_display.o(.ARM.exidx.text.LCD_Map_DrawDirectionArrow) refers to lcd_map_display.o(.text.LCD_Map_DrawDirectionArrow) for [Anonymous Symbol]
    lcd_map_display.o(.text.LCD_Map_Task) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    lcd_map_display.o(.text.LCD_Map_Task) refers to lcd_map_display.o(.bss.LCD_Map_Task.last_update) for LCD_Map_Task.last_update
    lcd_map_display.o(.text.LCD_Map_Task) refers to lcd_display_hal.o(.text.LCD_Fill) for LCD_Fill
    lcd_map_display.o(.text.LCD_Map_Task) refers to lcd_display_hal.o(.text.LCD_ShowString) for LCD_ShowString
    lcd_map_display.o(.text.LCD_Map_Task) refers to lcd_map_display.o(.text.LCD_Map_DisplayNavigationInstructions) for LCD_Map_DisplayNavigationInstructions
    lcd_map_display.o(.ARM.exidx.text.LCD_Map_Task) refers to lcd_map_display.o(.text.LCD_Map_Task) for [Anonymous Symbol]
    lcd_test.o(.text.LCD_Test_Numbers) refers to lcd_display_hal.o(.text.LCD_Fill) for LCD_Fill
    lcd_test.o(.text.LCD_Test_Numbers) refers to lcd_display_hal.o(.text.LCD_ShowString) for LCD_ShowString
    lcd_test.o(.text.LCD_Test_Numbers) refers to lcd_display_hal.o(.text.LCD_ShowFloatNum1) for LCD_ShowFloatNum1
    lcd_test.o(.ARM.exidx.text.LCD_Test_Numbers) refers to lcd_test.o(.text.LCD_Test_Numbers) for [Anonymous Symbol]
    lcd_test.o(.text.LCD_Test_Navigation) refers to lcd_display_hal.o(.text.LCD_Fill) for LCD_Fill
    lcd_test.o(.text.LCD_Test_Navigation) refers to lcd_display_hal.o(.text.LCD_ShowString) for LCD_ShowString
    lcd_test.o(.text.LCD_Test_Navigation) refers to lcd_display_hal.o(.text.LCD_DrawLine) for LCD_DrawLine
    lcd_test.o(.text.LCD_Test_Navigation) refers to lcd_display_hal.o(.text.Draw_Circle) for Draw_Circle
    lcd_test.o(.ARM.exidx.text.LCD_Test_Navigation) refers to lcd_test.o(.text.LCD_Test_Navigation) for [Anonymous Symbol]
    lcd_test.o(.text.LCD_Test_Chinese) refers to lcd_display_hal.o(.text.LCD_Fill) for LCD_Fill
    lcd_test.o(.text.LCD_Test_Chinese) refers to lcd_display_hal.o(.text.LCD_ShowString) for LCD_ShowString
    lcd_test.o(.text.LCD_Test_Chinese) refers to lcd_test.o(.rodata..L__const.LCD_Test_Chinese.chinese_chars) for .L__const.LCD_Test_Chinese.chinese_chars
    lcd_test.o(.text.LCD_Test_Chinese) refers to lcd_display_hal.o(.text.LCD_ShowChinese) for LCD_ShowChinese
    lcd_test.o(.ARM.exidx.text.LCD_Test_Chinese) refers to lcd_test.o(.text.LCD_Test_Chinese) for [Anonymous Symbol]
    lcd_test.o(.text.LCD_Test_All) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    lcd_test.o(.text.LCD_Test_All) refers to lcd_test.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    lcd_test.o(.text.LCD_Test_All) refers to lcd_test.o(.text.LCD_Test_Navigation) for LCD_Test_Navigation
    lcd_test.o(.text.LCD_Test_All) refers to lcd_test.o(.text.LCD_Test_Chinese) for LCD_Test_Chinese
    lcd_test.o(.text.LCD_Test_All) refers to lcd_test.o(.text.LCD_Test_Numbers) for LCD_Test_Numbers
    lcd_test.o(.ARM.exidx.text.LCD_Test_All) refers to lcd_test.o(.text.LCD_Test_All) for [Anonymous Symbol]
    lcd_test.o(.rodata..L__const.LCD_Test_Chinese.chinese_chars) refers to lcd_test.o(.rodata.str1.1) for [Anonymous Symbol]
    atan2.o(i.__hardfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2.o(i.__hardfp_atan2) refers to errno.o(i.__set_errno) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atof.o(i.__hardfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__hardfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.__softfp_atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.__softfp_atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers to errno.o(i.__set_errno) for __set_errno
    atof.o(i.atof) refers (Special) to iusefp.o(.text) for __I$use$fp
    atof.o(i.atof) refers to errno.o(i.__read_errno) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers to errno.o(i.__set_errno) for __set_errno
    cos.o(i.__hardfp_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.__hardfp_cos) refers to errno.o(i.__set_errno) for __set_errno
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos.o(i.__hardfp_cos) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos.o(i.__hardfp_cos) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos.o(i.__hardfp_cos) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos.o(i.__softfp_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.__softfp_cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos.o(i.cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    fmod.o(i.__hardfp_fmod) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod.o(i.__hardfp_fmod) refers to drem.o(.text) for _drem
    fmod.o(i.__hardfp_fmod) refers to dadd.o(.text) for __aeabi_drsub
    fmod.o(i.__hardfp_fmod) refers to errno.o(i.__set_errno) for __set_errno
    fmod.o(i.__hardfp_fmod) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    fmod.o(i.__softfp_fmod) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod.o(i.__softfp_fmod) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    fmod.o(i.fmod) refers (Special) to iusefp.o(.text) for __I$use$fp
    fmod.o(i.fmod) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    sin.o(i.__hardfp_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.__hardfp_sin) refers to errno.o(i.__set_errno) for __set_errno
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.__hardfp_sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.__hardfp_sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.__hardfp_sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin.o(i.__softfp_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin.o(i.sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    atan.o(i.__hardfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfixi.o(.text) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(.text) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to dadd.o(.text) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to dadd.o(.text) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(.text) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfixi.o(.text) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflti.o(.text) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to dfltui.o(.text) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(.text) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to dadd.o(.text) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    fputc_h.o(i._fputc$hlt) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc_h.o(i._fputc$hlt) refers (Special) to semi.o(.text) for __semihosting_library_function
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    strtod.o(.text) refers to scanf_fp.o(.text) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace_c.o(.text) for isspace
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dneg.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dsqrt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round
    drem.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    drem.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    scanf_fp.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    scanf_fp.o(.text) refers to dfltul.o(.text) for __aeabi_ul2d
    scanf_fp.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    scanf_fp.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    scanf_fp.o(.text) refers to scanf_fp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata
    dfltul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    scanf_fp.o(i._is_digit) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f407xx.o(HEAP), (1536 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.SystemClock_Config), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing gpio.o(.text), (0 bytes).
    Removing gpio.o(.ARM.exidx.text.MX_GPIO_Init), (8 bytes).
    Removing dma.o(.text), (0 bytes).
    Removing dma.o(.ARM.exidx.text.MX_DMA_Init), (8 bytes).
    Removing i2c.o(.text), (0 bytes).
    Removing i2c.o(.ARM.exidx.text.MX_I2C2_Init), (8 bytes).
    Removing i2c.o(.ARM.exidx.text.HAL_I2C_MspInit), (8 bytes).
    Removing i2c.o(.text.HAL_I2C_MspDeInit), (62 bytes).
    Removing i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit), (8 bytes).
    Removing tim.o(.text), (0 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM1_Init), (8 bytes).
    Removing tim.o(.text.HAL_TIM_MspPostInit), (92 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_MspPostInit), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM3_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM4_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_Base_MspDeInit), (32 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_Encoder_MspDeInit), (96 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.ARM.exidx.text.MX_UART4_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART1_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART2_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART3_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART6_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing usart.o(.text.HAL_UART_MspDeInit), (226 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing usart.o(.ARM.exidx.text.my_printf), (8 bytes).
    Removing stm32f4xx_it.o(.text), (0 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream1_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA1_Stream5_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART2_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART3_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UART4_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream1_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DMA2_Stream2_IRQHandler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.USART6_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_msp.o(.text), (0 bytes).
    Removing stm32f4xx_hal_msp.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text), (0 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit), (52 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit), (476 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout), (458 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive), (796 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit), (378 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive), (444 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT), (376 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA), (472 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAError), (76 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA), (472 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA), (276 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA), (276 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite), (200 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read), (600 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT), (260 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT), (272 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA), (518 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA), (580 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady), (532 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT), (324 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA), (496 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA), (598 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA), (444 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort), (290 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA), (448 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT), (54 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT), (88 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_ITError), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler), (1532 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE), (178 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF), (190 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE), (326 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF), (248 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler), (336 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout), (214 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (168 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (150 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (62 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig), (330 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (50 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq), (88 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S), (106 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit), (296 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program), (218 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (250 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (240 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (312 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock), (46 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (42 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.bss.pFlash), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (394 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector), (70 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (176 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (184 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (56 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (462 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (44 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (22 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart), (114 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT), (1236 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory), (20 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit), (136 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start), (114 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (454 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (46 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (156 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (130 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (84 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling), (102 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (88 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f4xx_hal.o(.text), (0 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DeInit), (68 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SetTickFreq), (38 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetHalVersion), (10 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetREVID), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_EnableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DisableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text), (0 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine), (186 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine), (148 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine), (140 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_IRQHandler), (42 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GetPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending), (8 bytes).
    Removing stm32f4xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text), (0 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start), (162 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop), (52 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT), (170 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT), (60 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA), (256 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt), (20 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMAError), (94 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA), (72 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start), (270 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_CCxChannelCmd), (36 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop), (170 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT), (330 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA), (634 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseCplt), (106 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start), (270 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop), (170 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT), (330 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA), (634 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA), (186 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start), (294 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop), (126 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT), (354 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT), (150 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA), (594 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMACaptureCplt), (118 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA), (162 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init), (92 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start), (124 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop), (140 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT), (140 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT), (156 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start), (188 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop), (130 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT), (230 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT), (214 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA), (552 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA), (230 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IRQHandler), (334 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel), (436 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_OC2_SetConfig), (104 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel), (454 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_TI1_SetConfig), (174 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel), (578 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart), (644 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMATriggerCplt), (20 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop), (118 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart), (28 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart), (628 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop), (118 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GenerateEvent), (36 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear), (212 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_ETR_SetConfig), (22 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro), (88 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig), (250 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT), (88 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue), (22 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.text.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(.rodata.cst16), (48 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init), (200 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit), (80 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start), (222 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop), (66 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT), (230 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA), (282 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start), (254 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop), (146 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT), (274 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT), (234 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA), (530 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt), (82 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA), (176 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start), (254 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop), (146 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT), (274 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT), (234 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA), (530 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA), (176 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start), (126 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop), (128 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT), (142 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA), (154 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt), (12 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt), (12 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text), (0 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init), (104 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_Init), (120 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init), (128 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive), (558 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT), (84 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA), (212 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt), (176 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA), (384 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA), (336 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause), (342 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume), (348 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle), (478 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT), (200 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort), (482 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit), (208 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive), (366 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT), (516 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT), (214 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback), (16 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT), (372 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback), (18 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak), (114 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode), (116 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode), (116 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter), (44 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver), (46 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetState), (14 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt), (8 bytes).
    Removing system_stm32f4xx.o(.text), (0 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f4xx.o(.text.SystemCoreClockUpdate), (134 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing encoder_driver.o(.text), (0 bytes).
    Removing encoder_driver.o(.text.Encoder_Driver_Init), (32 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.Encoder_Driver_Init), (8 bytes).
    Removing encoder_driver.o(.text.Encoder_Driver_Update), (84 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.Encoder_Driver_Update), (8 bytes).
    Removing hardware_iic.o(.text), (0 bytes).
    Removing hardware_iic.o(.text.IIC_ReadByte), (40 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_ReadByte), (8 bytes).
    Removing hardware_iic.o(.text.IIC_ReadBytes), (48 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_ReadBytes), (8 bytes).
    Removing hardware_iic.o(.text.IIC_WriteByte), (52 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_WriteByte), (8 bytes).
    Removing hardware_iic.o(.text.IIC_WriteBytes), (48 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_WriteBytes), (8 bytes).
    Removing hardware_iic.o(.text.Ping), (54 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.Ping), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Digtal), (48 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Digtal), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Anolog), (44 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Anolog), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Single_Anolog), (50 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Single_Anolog), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Anolog_Normalize), (54 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Anolog_Normalize), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Offset), (54 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Offset), (8 bytes).
    Removing ringbuffer.o(.text), (0 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_status), (20 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_status), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_init), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_data_len), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_put_force), (208 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_put_force), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_get), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_peek), (132 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_peek), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_putchar), (100 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_putchar_force), (102 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_putchar_force), (8 bytes).
    Removing ringbuffer.o(.text.rt_ringbuffer_getchar), (116 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_getchar), (8 bytes).
    Removing ringbuffer.o(.ARM.exidx.text.rt_ringbuffer_reset), (8 bytes).
    Removing uart_driver.o(.text), (0 bytes).
    Removing uart_driver.o(.text.Uart_Printf), (62 bytes).
    Removing uart_driver.o(.ARM.exidx.text.Uart_Printf), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing uart2_driver.o(.text), (0 bytes).
    Removing uart2_driver.o(.ARM.exidx.text.Uart2_Printf), (8 bytes).
    Removing uart2_driver.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback_UART2), (8 bytes).
    Removing uart3_driver.o(.text), (0 bytes).
    Removing uart3_driver.o(.text.Uart3_Printf), (62 bytes).
    Removing uart3_driver.o(.ARM.exidx.text.Uart3_Printf), (8 bytes).
    Removing uart3_driver.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback_UART3), (8 bytes).
    Removing uart3_driver.o(.bss.uart3_ring_buffer_input), (128 bytes).
    Removing uart6_driver.o(.text), (0 bytes).
    Removing uart6_driver.o(.text.Uart6_Printf), (62 bytes).
    Removing uart6_driver.o(.ARM.exidx.text.Uart6_Printf), (8 bytes).
    Removing uart6_driver.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback_UART6), (8 bytes).
    Removing uart6_driver.o(.bss.uart6_data_buffer), (512 bytes).
    Removing lcd_init_hal.o(.text), (0 bytes).
    Removing lcd_init_hal.o(.text.LCD_GPIO_Init), (146 bytes).
    Removing lcd_init_hal.o(.ARM.exidx.text.LCD_GPIO_Init), (8 bytes).
    Removing lcd_init_hal.o(.ARM.exidx.text.LCD_Writ_Bus), (8 bytes).
    Removing lcd_init_hal.o(.ARM.exidx.text.LCD_WR_DATA8), (8 bytes).
    Removing lcd_init_hal.o(.ARM.exidx.text.LCD_WR_DATA), (8 bytes).
    Removing lcd_init_hal.o(.ARM.exidx.text.LCD_WR_REG), (8 bytes).
    Removing lcd_init_hal.o(.ARM.exidx.text.LCD_Address_Set), (8 bytes).
    Removing lcd_init_hal.o(.ARM.exidx.text.LCD_Init), (8 bytes).
    Removing lcd_display_hal.o(.text), (0 bytes).
    Removing lcd_display_hal.o(.text.LCD_GetDigitPixel), (90 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_GetDigitPixel), (8 bytes).
    Removing lcd_display_hal.o(.text.LCD_GetLetterPixel), (86 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_GetLetterPixel), (8 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_Fill), (8 bytes).
    Removing lcd_display_hal.o(.text.LCD_DrawPoint), (22 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_DrawPoint), (8 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_DrawLine), (8 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_DrawRectangle), (8 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.Draw_Circle), (8 bytes).
    Removing lcd_display_hal.o(.text.mypow), (92 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.mypow), (8 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_ShowIntNum), (8 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_ShowChar), (8 bytes).
    Removing lcd_display_hal.o(.text.LCD_ShowFloatNum1), (328 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_ShowFloatNum1), (8 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_ShowString), (8 bytes).
    Removing lcd_display_hal.o(.text.LCD_GetChinesePattern), (1432 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_GetChinesePattern), (8 bytes).
    Removing lcd_display_hal.o(.text.LCD_ShowChinese), (96 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_ShowChinese), (8 bytes).
    Removing lcd_display_hal.o(.text.LCD_ShowChinese12x12), (396 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_ShowChinese12x12), (8 bytes).
    Removing lcd_display_hal.o(.text.LCD_ShowChinese16x16), (478 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_ShowChinese16x16), (8 bytes).
    Removing lcd_display_hal.o(.text.LCD_ShowChinese24x24), (686 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_ShowChinese24x24), (8 bytes).
    Removing lcd_display_hal.o(.text.LCD_ShowChinese32x32), (104 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_ShowChinese32x32), (8 bytes).
    Removing lcd_display_hal.o(.text.LCD_ShowMixedString), (318 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_ShowMixedString), (8 bytes).
    Removing lcd_display_hal.o(.text.LCD_ShowPicture), (74 bytes).
    Removing lcd_display_hal.o(.ARM.exidx.text.LCD_ShowPicture), (8 bytes).
    Removing scheduler.o(.text), (0 bytes).
    Removing scheduler.o(.ARM.exidx.text.GPS_AutoUpload_Task), (8 bytes).
    Removing scheduler.o(.text.System_Init), (30 bytes).
    Removing scheduler.o(.ARM.exidx.text.System_Init), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.Scheduler_Init), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.Scheduler_Run), (8 bytes).
    Removing uart_app.o(.text), (0 bytes).
    Removing uart_app.o(.ARM.exidx.text.Uart_Init), (8 bytes).
    Removing uart_app.o(.ARM.exidx.text.Uart_Task), (8 bytes).
    Removing uart2_app.o(.text), (0 bytes).
    Removing uart2_app.o(.ARM.exidx.text.Uart2_Init), (8 bytes).
    Removing uart2_app.o(.ARM.exidx.text.Uart2_Task), (8 bytes).
    Removing uart3_app.o(.text), (0 bytes).
    Removing uart3_app.o(.text.Uart3_Init), (14 bytes).
    Removing uart3_app.o(.ARM.exidx.text.Uart3_Init), (8 bytes).
    Removing uart3_app.o(.ARM.exidx.text.Uart3_Task), (8 bytes).
    Removing esp01_app.o(.text), (0 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_Init), (8 bytes).
    Removing esp01_app.o(.text.esp01_CheckAndReinit), (148 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_CheckAndReinit), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_StartInit), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_GetState), (8 bytes).
    Removing esp01_app.o(.text.esp01_SetSimulationLocation), (116 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_SetSimulationLocation), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_GetRealLocation), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_UploadGPSData), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_UploadNavigationCommand), (8 bytes).
    Removing esp01_app.o(.text.esp01_SendNavigationData), (100 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_SendNavigationData), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_Task), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_Reset), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_ReconnectWiFi), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_CheckConnection), (8 bytes).
    Removing esp01_app.o(.text.esp01_SendLocationData), (4 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_SendLocationData), (8 bytes).
    Removing esp01_app.o(.text.esp01_EstablishTCPConnection), (12 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_EstablishTCPConnection), (8 bytes).
    Removing esp01_app.o(.text.esp01_TryTCPWithIP), (12 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_TryTCPWithIP), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_SetConnected), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_SetTCPConnected), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_ResetTCPState), (8 bytes).
    Removing esp01_app.o(.text.esp01_ForceReset), (72 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_ForceReset), (8 bytes).
    Removing esp01_app.o(.text.esp01_NetworkDiagnostic), (186 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_NetworkDiagnostic), (8 bytes).
    Removing esp01_app.o(.text.esp01_NetworkDiagnostics), (186 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_NetworkDiagnostics), (8 bytes).
    Removing esp01_app.o(.text.esp01_QuickTest), (52 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_QuickTest), (8 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_SetDataSendReady), (8 bytes).
    Removing esp01_app.o(.text.esp01_GetDestinationCode), (152 bytes).
    Removing esp01_app.o(.ARM.exidx.text.esp01_GetDestinationCode), (8 bytes).
    Removing gps_app.o(.text), (0 bytes).
    Removing gps_app.o(.text.GPS_Init), (82 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_Init), (8 bytes).
    Removing gps_app.o(.text.clrStruct), (20 bytes).
    Removing gps_app.o(.ARM.exidx.text.clrStruct), (8 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_Task), (8 bytes).
    Removing gps_app.o(.ARM.exidx.text.parseGpsBuffer), (8 bytes).
    Removing gps_app.o(.text.printGpsBuffer), (148 bytes).
    Removing gps_app.o(.ARM.exidx.text.printGpsBuffer), (8 bytes).
    Removing gps_app.o(.text.GPS_LockPosition), (224 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_LockPosition), (8 bytes).
    Removing gps_app.o(.text.GPS_UnlockPosition), (104 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_UnlockPosition), (8 bytes).
    Removing gps_app.o(.text.GPS_SetLockedPosition), (136 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_SetLockedPosition), (8 bytes).
    Removing gps_app.o(.text.GPS_ManualUpload), (276 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_ManualUpload), (8 bytes).
    Removing gps_app.o(.text.GPS_SetCustomLocationAndUpload), (134 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_SetCustomLocationAndUpload), (8 bytes).
    Removing gps_app.o(.text.GPS_PrintUploadStatus), (324 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_PrintUploadStatus), (8 bytes).
    Removing gps_app.o(.text.GPS_Test_SimulateData), (92 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_Test_SimulateData), (8 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_Virtual_Init), (8 bytes).
    Removing gps_app.o(.text.GPS_Virtual_SetLocation), (134 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_Virtual_SetLocation), (8 bytes).
    Removing gps_app.o(.text.GPS_Virtual_EnableMovement), (2 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_Virtual_EnableMovement), (8 bytes).
    Removing gps_app.o(.text.GPS_Virtual_GenerateData), (2 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_Virtual_GenerateData), (8 bytes).
    Removing gps_app.o(.text.GPS_UploadToAMap), (4 bytes).
    Removing gps_app.o(.ARM.exidx.text.GPS_UploadToAMap), (8 bytes).
    Removing gps_app.o(.bss.point1), (2 bytes).
    Removing gps_app.o(.bss.longitude), (4 bytes).
    Removing gps_app.o(.bss.latitude), (4 bytes).
    Removing gps_app.o(.bss.USART_RX_BUF), (200 bytes).
    Removing gps_app.o(.bss.uart_GPS_RX_Buff), (1 bytes).
    Removing navigation_app.o(.text), (0 bytes).
    Removing navigation_app.o(.text.Navigation_Init), (108 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_Init), (8 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_Task), (8 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_StartNavigation), (8 bytes).
    Removing navigation_app.o(.text.Navigation_StopNavigation), (46 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_StopNavigation), (8 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_FindDestination), (8 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_PlanRoute), (8 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_ProcessCommand), (8 bytes).
    Removing navigation_app.o(.text.Navigation_PrintDestinations), (2 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_PrintDestinations), (8 bytes).
    Removing navigation_app.o(.text.Navigation_PrintStatus), (2 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_PrintStatus), (8 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_CalculateDistance), (8 bytes).
    Removing navigation_app.o(.ARM.exidx.text.Navigation_CalculateBearing), (8 bytes).
    Removing navigation_app.o(.rodata.str1.4), (16 bytes).
    Removing uart6_app.o(.text), (0 bytes).
    Removing uart6_app.o(.ARM.exidx.text.Uart6_Init), (8 bytes).
    Removing uart6_app.o(.ARM.exidx.text.Uart6_Task), (8 bytes).
    Removing uart6_app.o(.text.Uart6_RxCallback), (128 bytes).
    Removing uart6_app.o(.ARM.exidx.text.Uart6_RxCallback), (8 bytes).
    Removing tft_app.o(.text), (0 bytes).
    Removing tft_app.o(.ARM.exidx.text.tft_BasicTest), (8 bytes).
    Removing tft_app.o(.ARM.exidx.text.tft_HardwareDiagnose), (8 bytes).
    Removing tft_app.o(.text.tft_DeepDiagnose), (416 bytes).
    Removing tft_app.o(.ARM.exidx.text.tft_DeepDiagnose), (8 bytes).
    Removing tft_app.o(.text.tft_HardwareConnectionTest), (1620 bytes).
    Removing tft_app.o(.ARM.exidx.text.tft_HardwareConnectionTest), (8 bytes).
    Removing tft_app.o(.text.tft_SimpleTest), (324 bytes).
    Removing tft_app.o(.ARM.exidx.text.tft_SimpleTest), (8 bytes).
    Removing tft_app.o(.text.tft_ExtremeDiagnose), (572 bytes).
    Removing tft_app.o(.ARM.exidx.text.tft_ExtremeDiagnose), (8 bytes).
    Removing tft_app.o(.text.tft_TryST7735Init), (256 bytes).
    Removing tft_app.o(.ARM.exidx.text.tft_TryST7735Init), (8 bytes).
    Removing tft_app.o(.text.tft_TryST7789Init), (208 bytes).
    Removing tft_app.o(.ARM.exidx.text.tft_TryST7789Init), (8 bytes).
    Removing tft_app.o(.text.tft_TryILI9163Init), (200 bytes).
    Removing tft_app.o(.ARM.exidx.text.tft_TryILI9163Init), (8 bytes).
    Removing tft_app.o(.ARM.exidx.text.tft_Init), (8 bytes).
    Removing tft_app.o(.ARM.exidx.text.LCD_QuickPinTest), (8 bytes).
    Removing tft_app.o(.ARM.exidx.text.LCD_SimpleSPITest), (8 bytes).
    Removing tft_app.o(.text.tft_ShowWandaMap), (208 bytes).
    Removing tft_app.o(.ARM.exidx.text.tft_ShowWandaMap), (8 bytes).
    Removing tft_app.o(.text.tft_PinConfigTest), (380 bytes).
    Removing tft_app.o(.ARM.exidx.text.tft_PinConfigTest), (8 bytes).
    Removing tft_app.o(.ARM.exidx.text.tft_UpdateMapPosition), (8 bytes).
    Removing tft_app.o(.ARM.exidx.text.tft_SetMapDestination), (8 bytes).
    Removing tft_app.o(.ARM.exidx.text.tft_Task), (8 bytes).
    Removing tft_app.o(.text.tft_DisplaySystemInfo), (360 bytes).
    Removing tft_app.o(.ARM.exidx.text.tft_DisplaySystemInfo), (8 bytes).
    Removing lcd_map_display.o(.text), (0 bytes).
    Removing lcd_map_display.o(.ARM.exidx.text.LCD_Map_Init), (8 bytes).
    Removing lcd_map_display.o(.text.LCD_Map_Clear), (86 bytes).
    Removing lcd_map_display.o(.ARM.exidx.text.LCD_Map_Clear), (8 bytes).
    Removing lcd_map_display.o(.text.LCD_Map_DrawGrid), (1516 bytes).
    Removing lcd_map_display.o(.ARM.exidx.text.LCD_Map_DrawGrid), (8 bytes).
    Removing lcd_map_display.o(.ARM.exidx.text.LCD_Map_UpdatePosition), (8 bytes).
    Removing lcd_map_display.o(.ARM.exidx.text.LCD_Map_SetDestination), (8 bytes).
    Removing lcd_map_display.o(.ARM.exidx.text.LCD_Map_CalculateRouteDistance), (8 bytes).
    Removing lcd_map_display.o(.ARM.exidx.text.LCD_Map_CalculateBearing), (8 bytes).
    Removing lcd_map_display.o(.text.LCD_Map_GPS_To_Screen), (164 bytes).
    Removing lcd_map_display.o(.ARM.exidx.text.LCD_Map_GPS_To_Screen), (8 bytes).
    Removing lcd_map_display.o(.text.LCD_Map_DrawCurrentPosition), (388 bytes).
    Removing lcd_map_display.o(.ARM.exidx.text.LCD_Map_DrawCurrentPosition), (8 bytes).
    Removing lcd_map_display.o(.text.LCD_Map_DrawDestination), (244 bytes).
    Removing lcd_map_display.o(.ARM.exidx.text.LCD_Map_DrawDestination), (8 bytes).
    Removing lcd_map_display.o(.text.LCD_Map_DrawRoute), (620 bytes).
    Removing lcd_map_display.o(.ARM.exidx.text.LCD_Map_DrawRoute), (8 bytes).
    Removing lcd_map_display.o(.text.LCD_Map_CalculateDistance), (396 bytes).
    Removing lcd_map_display.o(.ARM.exidx.text.LCD_Map_CalculateDistance), (8 bytes).
    Removing lcd_map_display.o(.ARM.exidx.text.LCD_Map_DisplayNavigationInstructions), (8 bytes).
    Removing lcd_map_display.o(.ARM.exidx.text.LCD_Map_DrawNavigationStep), (8 bytes).
    Removing lcd_map_display.o(.ARM.exidx.text.LCD_Map_DrawDirectionArrow), (8 bytes).
    Removing lcd_map_display.o(.ARM.exidx.text.LCD_Map_Task), (8 bytes).
    Removing lcd_test.o(.text), (0 bytes).
    Removing lcd_test.o(.text.LCD_Test_Numbers), (288 bytes).
    Removing lcd_test.o(.ARM.exidx.text.LCD_Test_Numbers), (8 bytes).
    Removing lcd_test.o(.text.LCD_Test_Navigation), (668 bytes).
    Removing lcd_test.o(.ARM.exidx.text.LCD_Test_Navigation), (8 bytes).
    Removing lcd_test.o(.text.LCD_Test_Chinese), (196 bytes).
    Removing lcd_test.o(.ARM.exidx.text.LCD_Test_Chinese), (8 bytes).
    Removing lcd_test.o(.text.LCD_Test_All), (84 bytes).
    Removing lcd_test.o(.ARM.exidx.text.LCD_Test_All), (8 bytes).
    Removing lcd_test.o(.rodata.str1.1), (96 bytes).
    Removing lcd_test.o(.rodata..L__const.LCD_Test_Chinese.chinese_chars), (96 bytes).
    Removing lcd_test.o(.bss..L_MergedGlobals), (8 bytes).
    Removing dneg.o(.text), (6 bytes).

1146 unused section(s) (total 77773 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_c.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  printfstubs.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc_h.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strchr.c         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/microlib/string/strcmp.c         0x00000000   Number         0  strcmp.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strncmp.c        0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/microlib/string/strncpy.c        0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fprem.c                0x00000000   Number         0  drem.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fmod.c                        0x00000000   Number         0  fmod.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    GPS_app.c                                0x00000000   Number         0  gps_app.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dma.c                                    0x00000000   Number         0  dma.o ABSOLUTE
    encoder_driver.c                         0x00000000   Number         0  encoder_driver.o ABSOLUTE
    esp01_app.c                              0x00000000   Number         0  esp01_app.o ABSOLUTE
    gpio.c                                   0x00000000   Number         0  gpio.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    hardware_iic.c                           0x00000000   Number         0  hardware_iic.o ABSOLUTE
    i2c.c                                    0x00000000   Number         0  i2c.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    lcd_display_hal.c                        0x00000000   Number         0  lcd_display_hal.o ABSOLUTE
    lcd_init_hal.c                           0x00000000   Number         0  lcd_init_hal.o ABSOLUTE
    lcd_map_display.c                        0x00000000   Number         0  lcd_map_display.o ABSOLUTE
    lcd_test.c                               0x00000000   Number         0  lcd_test.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    navigation_app.c                         0x00000000   Number         0  navigation_app.o ABSOLUTE
    ringbuffer.c                             0x00000000   Number         0  ringbuffer.o ABSOLUTE
    scheduler.c                              0x00000000   Number         0  scheduler.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    stm32f4xx_hal.c                          0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    stm32f4xx_hal_cortex.c                   0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    stm32f4xx_hal_dma.c                      0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    stm32f4xx_hal_dma_ex.c                   0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    stm32f4xx_hal_exti.c                     0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    stm32f4xx_hal_flash.c                    0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    stm32f4xx_hal_flash_ex.c                 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    stm32f4xx_hal_flash_ramfunc.c            0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    stm32f4xx_hal_gpio.c                     0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    stm32f4xx_hal_i2c.c                      0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    stm32f4xx_hal_i2c_ex.c                   0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    stm32f4xx_hal_msp.c                      0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    stm32f4xx_hal_pwr.c                      0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    stm32f4xx_hal_pwr_ex.c                   0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    stm32f4xx_hal_rcc.c                      0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    stm32f4xx_hal_rcc_ex.c                   0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    stm32f4xx_hal_tim.c                      0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    stm32f4xx_hal_tim_ex.c                   0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    stm32f4xx_hal_uart.c                     0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    tft_app.c                                0x00000000   Number         0  tft_app.o ABSOLUTE
    tim.c                                    0x00000000   Number         0  tim.o ABSOLUTE
    uart2_app.c                              0x00000000   Number         0  uart2_app.o ABSOLUTE
    uart2_driver.c                           0x00000000   Number         0  uart2_driver.o ABSOLUTE
    uart3_app.c                              0x00000000   Number         0  uart3_app.o ABSOLUTE
    uart3_driver.c                           0x00000000   Number         0  uart3_driver.o ABSOLUTE
    uart6_app.c                              0x00000000   Number         0  uart6_app.o ABSOLUTE
    uart6_driver.c                           0x00000000   Number         0  uart6_driver.o ABSOLUTE
    uart_app.c                               0x00000000   Number         0  uart_app.o ABSOLUTE
    uart_driver.c                            0x00000000   Number         0  uart_driver.o ABSOLUTE
    usart.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x08000198   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x08000198   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000198   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000198   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    $v0                                      0x0800019c   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x0800019c   Section       36  startup_stm32f407xx.o(.text)
    .text                                    0x080001c0   Section        0  uldiv.o(.text)
    .text                                    0x08000222   Section        0  memcpya.o(.text)
    .text                                    0x08000246   Section        0  memseta.o(.text)
    .text                                    0x0800026a   Section        0  strstr.o(.text)
    .text                                    0x0800028e   Section        0  strncpy.o(.text)
    .text                                    0x080002a6   Section        0  strchr.o(.text)
    .text                                    0x080002ba   Section        0  strlen.o(.text)
    .text                                    0x080002c8   Section        0  strcmp.o(.text)
    .text                                    0x080002e4   Section        0  memcmp.o(.text)
    .text                                    0x080002fe   Section        0  strncmp.o(.text)
    .text                                    0x0800031c   Section        0  dadd.o(.text)
    .text                                    0x0800046a   Section        0  dmul.o(.text)
    .text                                    0x0800054e   Section        0  ddiv.o(.text)
    .text                                    0x0800062c   Section        0  dfixi.o(.text)
    .text                                    0x0800066a   Section        0  f2d.o(.text)
    .text                                    0x08000690   Section        0  d2f.o(.text)
    .text                                    0x080006c8   Section        0  uidiv.o(.text)
    .text                                    0x080006f4   Section        0  llshl.o(.text)
    .text                                    0x08000712   Section        0  llushr.o(.text)
    .text                                    0x08000732   Section        0  llsshr.o(.text)
    _local_sscanf                            0x08000759   Thumb Code    62  strtod.o(.text)
    .text                                    0x08000758   Section        0  strtod.o(.text)
    .text                                    0x08000800   Section        0  iusefp.o(.text)
    .text                                    0x08000800   Section        0  fepilogue.o(.text)
    .text                                    0x0800086e   Section        0  depilogue.o(.text)
    .text                                    0x08000928   Section        0  dsqrt.o(.text)
    .text                                    0x080009ca   Section        0  drem.o(.text)
    .text                                    0x08000a50   Section        0  dfixul.o(.text)
    .text                                    0x08000a80   Section       48  cdcmple.o(.text)
    .text                                    0x08000ab0   Section       48  cdrcmple.o(.text)
    .text                                    0x08000ae0   Section       48  init.o(.text)
    .text                                    0x08000b10   Section        0  isspace_c.o(.text)
    _fp_value                                0x08000b1d   Thumb Code   296  scanf_fp.o(.text)
    .text                                    0x08000b1c   Section        0  scanf_fp.o(.text)
    .text                                    0x08000e7c   Section        0  _sgetc.o(.text)
    .text                                    0x08000ebc   Section        0  dflti.o(.text)
    .text                                    0x08000ede   Section        0  dfltui.o(.text)
    .text                                    0x08000ef8   Section        0  ctype_c.o(.text)
    .text                                    0x08000f20   Section        0  dfltul.o(.text)
    [Anonymous Symbol]                       0x08000f38   Section        0  stm32f4xx_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x08000f3c   Section        0  stm32f4xx_it.o(.text.DMA1_Stream1_IRQHandler)
    [Anonymous Symbol]                       0x08000f48   Section        0  stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler)
    [Anonymous Symbol]                       0x08000f54   Section        0  stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler)
    [Anonymous Symbol]                       0x08000f60   Section        0  stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler)
    [Anonymous Symbol]                       0x08000f6c   Section        0  stm32f4xx_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x08000f70   Section        0  lcd_display_hal.o(.text.Draw_Circle)
    [Anonymous Symbol]                       0x08001088   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x08001090   Section        0  scheduler.o(.text.GPS_AutoUpload_Task)
    [Anonymous Symbol]                       0x080010ec   Section        0  gps_app.o(.text.GPS_Task)
    [Anonymous Symbol]                       0x08001144   Section        0  gps_app.o(.text.GPS_Virtual_Init)
    [Anonymous Symbol]                       0x08001148   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    [Anonymous Symbol]                       0x080011d8   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    [Anonymous Symbol]                       0x080011fc   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    [Anonymous Symbol]                       0x080013c0   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Init)
    [Anonymous Symbol]                       0x08001524   Section        0  stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    [Anonymous Symbol]                       0x080015c8   Section        0  stm32f4xx_hal.o(.text.HAL_Delay)
    [Anonymous Symbol]                       0x080015f0   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x08001790   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    [Anonymous Symbol]                       0x0800179c   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin)
    [Anonymous Symbol]                       0x080017ac   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    [Anonymous Symbol]                       0x080017b8   Section        0  stm32f4xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x080017c4   Section        0  stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init)
    [Anonymous Symbol]                       0x08001928   Section        0  i2c.o(.text.HAL_I2C_MspInit)
    [Anonymous Symbol]                       0x080019b0   Section        0  stm32f4xx_hal.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x080019cc   Section        0  stm32f4xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x08001a04   Section        0  stm32f4xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08001a4c   Section        0  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x08001a84   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x08001aa8   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x08001b00   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x08001b20   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x08001c84   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    [Anonymous Symbol]                       0x08001cac   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    [Anonymous Symbol]                       0x08001cd4   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    [Anonymous Symbol]                       0x08001d40   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x080020ec   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x08002118   Section        0  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime)
    [Anonymous Symbol]                       0x08002164   Section        0  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    [Anonymous Symbol]                       0x08002220   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    [Anonymous Symbol]                       0x0800227c   Section        0  tim.o(.text.HAL_TIM_Base_MspInit)
    [Anonymous Symbol]                       0x080022b0   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    [Anonymous Symbol]                       0x08002450   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init)
    [Anonymous Symbol]                       0x08002504   Section        0  tim.o(.text.HAL_TIM_Encoder_MspInit)
    [Anonymous Symbol]                       0x080025c4   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel)
    [Anonymous Symbol]                       0x080027e4   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    [Anonymous Symbol]                       0x08002840   Section        0  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit)
    [Anonymous Symbol]                       0x08002844   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
    [Anonymous Symbol]                       0x08002a34   Section        0  uart_driver.o(.text.HAL_UARTEx_RxEventCallback)
    [Anonymous Symbol]                       0x08002af4   Section        0  uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2)
    [Anonymous Symbol]                       0x08002b68   Section        0  uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3)
    [Anonymous Symbol]                       0x08002bc8   Section        0  uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6)
    [Anonymous Symbol]                       0x08002c2c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop)
    [Anonymous Symbol]                       0x08002e48   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    [Anonymous Symbol]                       0x08002e4c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    [Anonymous Symbol]                       0x080033e0   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    [Anonymous Symbol]                       0x08003440   Section        0  usart.o(.text.HAL_UART_MspInit)
    [Anonymous Symbol]                       0x08003788   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback)
    [Anonymous Symbol]                       0x0800378c   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback)
    [Anonymous Symbol]                       0x08003790   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    [Anonymous Symbol]                       0x08003924   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    [Anonymous Symbol]                       0x08003928   Section        0  stm32f4xx_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x0800392c   Section        0  lcd_init_hal.o(.text.LCD_Address_Set)
    [Anonymous Symbol]                       0x080039e8   Section        0  lcd_display_hal.o(.text.LCD_DrawLine)
    [Anonymous Symbol]                       0x08003a88   Section        0  lcd_display_hal.o(.text.LCD_DrawRectangle)
    [Anonymous Symbol]                       0x08003b9c   Section        0  lcd_display_hal.o(.text.LCD_Fill)
    [Anonymous Symbol]                       0x08003be4   Section        0  lcd_init_hal.o(.text.LCD_Init)
    [Anonymous Symbol]                       0x080042e0   Section        0  lcd_map_display.o(.text.LCD_Map_CalculateBearing)
    [Anonymous Symbol]                       0x0800449c   Section        0  lcd_map_display.o(.text.LCD_Map_CalculateRouteDistance)
    [Anonymous Symbol]                       0x08004634   Section        0  lcd_map_display.o(.text.LCD_Map_DisplayNavigationInstructions)
    [Anonymous Symbol]                       0x08004918   Section        0  lcd_map_display.o(.text.LCD_Map_DrawDirectionArrow)
    [Anonymous Symbol]                       0x08004a9c   Section        0  lcd_map_display.o(.text.LCD_Map_DrawNavigationStep)
    [Anonymous Symbol]                       0x08004b84   Section        0  lcd_map_display.o(.text.LCD_Map_Init)
    [Anonymous Symbol]                       0x08004c0c   Section        0  lcd_map_display.o(.text.LCD_Map_SetDestination)
    [Anonymous Symbol]                       0x08004c60   Section        0  lcd_map_display.o(.text.LCD_Map_Task)
    [Anonymous Symbol]                       0x08004ccc   Section        0  lcd_map_display.o(.text.LCD_Map_UpdatePosition)
    [Anonymous Symbol]                       0x08004cf8   Section        0  tft_app.o(.text.LCD_QuickPinTest)
    [Anonymous Symbol]                       0x08005034   Section        0  lcd_display_hal.o(.text.LCD_ShowChar)
    [Anonymous Symbol]                       0x080053a0   Section        0  lcd_display_hal.o(.text.LCD_ShowIntNum)
    [Anonymous Symbol]                       0x080054bc   Section        0  lcd_display_hal.o(.text.LCD_ShowString)
    [Anonymous Symbol]                       0x08005504   Section        0  tft_app.o(.text.LCD_SimpleSPITest)
    [Anonymous Symbol]                       0x080057a0   Section        0  lcd_init_hal.o(.text.LCD_WR_DATA)
    [Anonymous Symbol]                       0x080057b4   Section        0  lcd_init_hal.o(.text.LCD_WR_DATA8)
    [Anonymous Symbol]                       0x080057b8   Section        0  lcd_init_hal.o(.text.LCD_WR_REG)
    [Anonymous Symbol]                       0x080057e4   Section        0  lcd_init_hal.o(.text.LCD_Writ_Bus)
    [Anonymous Symbol]                       0x08005924   Section        0  dma.o(.text.MX_DMA_Init)
    [Anonymous Symbol]                       0x080059a0   Section        0  gpio.o(.text.MX_GPIO_Init)
    [Anonymous Symbol]                       0x08005b30   Section        0  i2c.o(.text.MX_I2C2_Init)
    [Anonymous Symbol]                       0x08005b74   Section        0  tim.o(.text.MX_TIM1_Init)
    [Anonymous Symbol]                       0x08005cdc   Section        0  tim.o(.text.MX_TIM3_Init)
    [Anonymous Symbol]                       0x08005d44   Section        0  tim.o(.text.MX_TIM4_Init)
    [Anonymous Symbol]                       0x08005dac   Section        0  usart.o(.text.MX_UART4_Init)
    [Anonymous Symbol]                       0x08005de8   Section        0  usart.o(.text.MX_USART1_UART_Init)
    [Anonymous Symbol]                       0x08005e24   Section        0  usart.o(.text.MX_USART2_UART_Init)
    [Anonymous Symbol]                       0x08005e60   Section        0  usart.o(.text.MX_USART3_UART_Init)
    [Anonymous Symbol]                       0x08005e9c   Section        0  usart.o(.text.MX_USART6_UART_Init)
    [Anonymous Symbol]                       0x08005ed8   Section        0  stm32f4xx_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x08005edc   Section        0  stm32f4xx_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x08005ee0   Section        0  navigation_app.o(.text.Navigation_CalculateBearing)
    [Anonymous Symbol]                       0x0800608c   Section        0  navigation_app.o(.text.Navigation_CalculateDistance)
    [Anonymous Symbol]                       0x08006210   Section        0  navigation_app.o(.text.Navigation_FindDestination)
    [Anonymous Symbol]                       0x0800634c   Section        0  navigation_app.o(.text.Navigation_PlanRoute)
    [Anonymous Symbol]                       0x08006584   Section        0  navigation_app.o(.text.Navigation_ProcessCommand)
    [Anonymous Symbol]                       0x08006b14   Section        0  navigation_app.o(.text.Navigation_StartNavigation)
    [Anonymous Symbol]                       0x08006e34   Section        0  navigation_app.o(.text.Navigation_Task)
    [Anonymous Symbol]                       0x08006e84   Section        0  stm32f4xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x08006e88   Section        0  stm32f4xx_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x08006e8c   Section        0  scheduler.o(.text.Scheduler_Init)
    [Anonymous Symbol]                       0x08006eb4   Section        0  scheduler.o(.text.Scheduler_Run)
    [Anonymous Symbol]                       0x08006f00   Section        0  stm32f4xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x08006f04   Section        0  main.o(.text.SystemClock_Config)
    [Anonymous Symbol]                       0x08006fac   Section        0  system_stm32f4xx.o(.text.SystemInit)
    [Anonymous Symbol]                       0x08006fc0   Section        0  stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig)
    [Anonymous Symbol]                       0x080070fc   Section        0  stm32f4xx_it.o(.text.UART4_IRQHandler)
    UART_DMAAbortOnError                     0x08007109   Thumb Code    10  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    [Anonymous Symbol]                       0x08007108   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError)
    UART_DMAError                            0x08007115   Thumb Code   380  stm32f4xx_hal_uart.o(.text.UART_DMAError)
    [Anonymous Symbol]                       0x08007114   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAError)
    UART_DMAReceiveCplt                      0x08007291   Thumb Code   350  stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt)
    [Anonymous Symbol]                       0x08007290   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt)
    UART_DMARxHalfCplt                       0x080073f1   Thumb Code    24  stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt)
    [Anonymous Symbol]                       0x080073f0   Section        0  stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt)
    UART_Receive_IT                          0x08007409   Thumb Code   254  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    [Anonymous Symbol]                       0x08007408   Section        0  stm32f4xx_hal_uart.o(.text.UART_Receive_IT)
    UART_SetConfig                           0x08007509   Thumb Code   230  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08007508   Section        0  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x080075f0   Section        0  stm32f4xx_it.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x080075fc   Section        0  stm32f4xx_it.o(.text.USART2_IRQHandler)
    [Anonymous Symbol]                       0x08007608   Section        0  stm32f4xx_it.o(.text.USART3_IRQHandler)
    [Anonymous Symbol]                       0x08007614   Section        0  stm32f4xx_it.o(.text.USART6_IRQHandler)
    [Anonymous Symbol]                       0x08007620   Section        0  uart2_app.o(.text.Uart2_Init)
    [Anonymous Symbol]                       0x08007668   Section        0  uart2_driver.o(.text.Uart2_Printf)
    [Anonymous Symbol]                       0x080076d4   Section        0  uart2_app.o(.text.Uart2_Task)
    [Anonymous Symbol]                       0x08007cf4   Section        0  uart3_app.o(.text.Uart3_Task)
    [Anonymous Symbol]                       0x08007d04   Section        0  uart6_app.o(.text.Uart6_Init)
    [Anonymous Symbol]                       0x08007d84   Section        0  uart6_app.o(.text.Uart6_Task)
    [Anonymous Symbol]                       0x08007dd4   Section        0  uart_app.o(.text.Uart_Init)
    [Anonymous Symbol]                       0x08007e6c   Section        0  uart_app.o(.text.Uart_Task)
    [Anonymous Symbol]                       0x080084fc   Section        0  stm32f4xx_it.o(.text.UsageFault_Handler)
    [Anonymous Symbol]                       0x08008500   Section        0  esp01_app.o(.text.esp01_CheckConnection)
    [Anonymous Symbol]                       0x08008664   Section        0  esp01_app.o(.text.esp01_GetRealLocation)
    [Anonymous Symbol]                       0x08008690   Section        0  esp01_app.o(.text.esp01_GetState)
    [Anonymous Symbol]                       0x0800869c   Section        0  esp01_app.o(.text.esp01_Init)
    [Anonymous Symbol]                       0x080087f0   Section        0  esp01_app.o(.text.esp01_ReconnectWiFi)
    [Anonymous Symbol]                       0x080088e8   Section        0  esp01_app.o(.text.esp01_Reset)
    [Anonymous Symbol]                       0x08008930   Section        0  esp01_app.o(.text.esp01_ResetTCPState)
    [Anonymous Symbol]                       0x08008940   Section        0  esp01_app.o(.text.esp01_SetConnected)
    [Anonymous Symbol]                       0x08008950   Section        0  esp01_app.o(.text.esp01_SetDataSendReady)
    [Anonymous Symbol]                       0x08008978   Section        0  esp01_app.o(.text.esp01_SetTCPConnected)
    [Anonymous Symbol]                       0x08008988   Section        0  esp01_app.o(.text.esp01_StartInit)
    [Anonymous Symbol]                       0x080089c4   Section        0  esp01_app.o(.text.esp01_Task)
    [Anonymous Symbol]                       0x08008acc   Section        0  esp01_app.o(.text.esp01_UploadGPSData)
    [Anonymous Symbol]                       0x08008c0c   Section        0  esp01_app.o(.text.esp01_UploadNavigationCommand)
    [Anonymous Symbol]                       0x08008d98   Section        0  main.o(.text.main)
    [Anonymous Symbol]                       0x08008dd8   Section        0  usart.o(.text.my_printf)
    [Anonymous Symbol]                       0x08008e18   Section        0  gps_app.o(.text.parseGpsBuffer)
    [Anonymous Symbol]                       0x08009018   Section        0  ringbuffer.o(.text.rt_ringbuffer_data_len)
    [Anonymous Symbol]                       0x0800905c   Section        0  ringbuffer.o(.text.rt_ringbuffer_get)
    [Anonymous Symbol]                       0x08009114   Section        0  ringbuffer.o(.text.rt_ringbuffer_init)
    [Anonymous Symbol]                       0x08009124   Section        0  ringbuffer.o(.text.rt_ringbuffer_put)
    [Anonymous Symbol]                       0x080091d8   Section        0  ringbuffer.o(.text.rt_ringbuffer_reset)
    [Anonymous Symbol]                       0x080091e0   Section        0  tft_app.o(.text.tft_BasicTest)
    [Anonymous Symbol]                       0x080093c8   Section        0  tft_app.o(.text.tft_HardwareDiagnose)
    [Anonymous Symbol]                       0x080098b8   Section        0  tft_app.o(.text.tft_Init)
    [Anonymous Symbol]                       0x08009b94   Section        0  tft_app.o(.text.tft_SetMapDestination)
    [Anonymous Symbol]                       0x08009c1c   Section        0  tft_app.o(.text.tft_Task)
    [Anonymous Symbol]                       0x08009cd8   Section        0  tft_app.o(.text.tft_UpdateMapPosition)
    i.__0snprintf                            0x08009d44   Section        0  printfa.o(i.__0snprintf)
    i.__0vsnprintf                           0x08009d78   Section        0  printfa.o(i.__0vsnprintf)
    i.__ARM_fpclassify                       0x08009dac   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_atan                          0x08009de0   Section        0  atan.o(i.__hardfp_atan)
    i.__hardfp_atan2                         0x0800a0b8   Section        0  atan2.o(i.__hardfp_atan2)
    i.__hardfp_atof                          0x0800a2b8   Section        0  atof.o(i.__hardfp_atof)
    i.__hardfp_cos                           0x0800a2f0   Section        0  cos.o(i.__hardfp_cos)
    i.__hardfp_fmod                          0x0800a3b8   Section        0  fmod.o(i.__hardfp_fmod)
    i.__hardfp_sin                           0x0800a4c0   Section        0  sin.o(i.__hardfp_sin)
    i.__hardfp_sqrt                          0x0800a588   Section        0  sqrt.o(i.__hardfp_sqrt)
    i.__ieee754_rem_pio2                     0x0800a608   Section        0  rred.o(i.__ieee754_rem_pio2)
    i.__kernel_cos                           0x0800aa40   Section        0  cos_i.o(i.__kernel_cos)
    i.__kernel_poly                          0x0800abb0   Section        0  poly.o(i.__kernel_poly)
    i.__kernel_sin                           0x0800aca8   Section        0  sin_i.o(i.__kernel_sin)
    i.__mathlib_dbl_infnan                   0x0800add8   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x0800adec   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x0800ae00   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x0800ae20   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__read_errno                           0x0800ae40   Section        0  errno.o(i.__read_errno)
    i.__scatterload_copy                     0x0800ae4c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800ae5a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x0800ae5c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x0800ae6c   Section        0  errno.o(i.__set_errno)
    _fp_digits                               0x0800ae79   Thumb Code   366  printfa.o(i._fp_digits)
    i._fp_digits                             0x0800ae78   Section        0  printfa.o(i._fp_digits)
    i._is_digit                              0x0800affc   Section        0  scanf_fp.o(i._is_digit)
    _printf_core                             0x0800b00d   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_core                           0x0800b00c   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x0800b6e9   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x0800b6e8   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x0800b70d   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x0800b70c   Section        0  printfa.o(i._printf_pre_padding)
    _snputc                                  0x0800b73b   Thumb Code    22  printfa.o(i._snputc)
    i._snputc                                0x0800b73a   Section        0  printfa.o(i._snputc)
    i.atan                                   0x0800b750   Section        0  atan.o(i.atan)
    i.fabs                                   0x0800b760   Section        0  fabs.o(i.fabs)
    atanhi                                   0x0800b778   Data          32  atan.o(.constdata)
    .constdata                               0x0800b778   Section      152  atan.o(.constdata)
    atanlo                                   0x0800b798   Data          32  atan.o(.constdata)
    aTodd                                    0x0800b7b8   Data          40  atan.o(.constdata)
    aTeven                                   0x0800b7e0   Data          48  atan.o(.constdata)
    C                                        0x0800b810   Data          48  cos_i.o(.constdata)
    .constdata                               0x0800b810   Section       48  cos_i.o(.constdata)
    .constdata                               0x0800b840   Section        8  qnan.o(.constdata)
    pio2s                                    0x0800b848   Data          48  rred.o(.constdata)
    .constdata                               0x0800b848   Section      204  rred.o(.constdata)
    twooverpi                                0x0800b878   Data         156  rred.o(.constdata)
    S                                        0x0800b918   Data          40  sin_i.o(.constdata)
    .constdata                               0x0800b918   Section       40  sin_i.o(.constdata)
    .constdata                               0x0800b940   Section       64  ctype_c.o(.constdata)
    .L__const.LCD_GetDigitPixel.digit_patterns 0x0800b980   Data          70  lcd_display_hal.o(.rodata..L__const.LCD_GetDigitPixel.digit_patterns)
    .L__const.LCD_GetLetterPixel.letter_patterns 0x0800b9c6   Data         182  lcd_display_hal.o(.rodata..L__const.LCD_GetLetterPixel.letter_patterns)
    DMA_CalcBaseAndBitshift.flagBitshiftOffset 0x0800ba94   Data           8  stm32f4xx_hal_dma.o(.rodata.cst8)
    [Anonymous Symbol]                       0x0800ba94   Section        0  stm32f4xx_hal_dma.o(.rodata.cst8)
    destinations                             0x0800ba9c   Data        2080  navigation_app.o(.rodata.destinations)
    [Anonymous Symbol]                       0x0800ba9c   Section        0  navigation_app.o(.rodata.destinations)
    .L.str.6                                 0x0800c2bc   Data          52  lcd_init_hal.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800c2bc   Section        0  lcd_init_hal.o(.rodata.str1.1)
    .L.str.8                                 0x0800c2f0   Data          38  lcd_init_hal.o(.rodata.str1.1)
    .L.str.3                                 0x0800c316   Data          26  lcd_init_hal.o(.rodata.str1.1)
    .L.str.2                                 0x0800c330   Data          32  lcd_init_hal.o(.rodata.str1.1)
    .L.str.9                                 0x0800c350   Data          29  lcd_init_hal.o(.rodata.str1.1)
    .L.str                                   0x0800c36d   Data          29  lcd_init_hal.o(.rodata.str1.1)
    .L.str.5                                 0x0800c38a   Data          35  lcd_init_hal.o(.rodata.str1.1)
    .L.str.7                                 0x0800c3ad   Data          41  lcd_init_hal.o(.rodata.str1.1)
    .L.str.4                                 0x0800c3d6   Data          35  lcd_init_hal.o(.rodata.str1.1)
    .L.str.16                                0x0800c3f9   Data          44  lcd_init_hal.o(.rodata.str1.1)
    .L.str.15                                0x0800c425   Data          21  lcd_init_hal.o(.rodata.str1.1)
    .L.str.1                                 0x0800c43a   Data          30  lcd_init_hal.o(.rodata.str1.1)
    .L.str.14                                0x0800c458   Data          23  lcd_init_hal.o(.rodata.str1.1)
    .L.str.34                                0x0800c46f   Data          33  uart_app.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800c46f   Section        0  uart_app.o(.rodata.str1.1)
    .L.str.37                                0x0800c490   Data          29  uart_app.o(.rodata.str1.1)
    .L.str.48                                0x0800c4ad   Data          29  uart_app.o(.rodata.str1.1)
    .L.str.32                                0x0800c4ca   Data          33  uart_app.o(.rodata.str1.1)
    .L.str.22                                0x0800c4eb   Data          20  uart_app.o(.rodata.str1.1)
    .L.str.52                                0x0800c4ff   Data          43  uart_app.o(.rodata.str1.1)
    .L.str.51                                0x0800c52a   Data          23  uart_app.o(.rodata.str1.1)
    .L.str.40                                0x0800c541   Data          30  uart_app.o(.rodata.str1.1)
    .L.str.27                                0x0800c55f   Data          19  uart_app.o(.rodata.str1.1)
    .L.str.29                                0x0800c572   Data          20  uart_app.o(.rodata.str1.1)
    .L.str.28                                0x0800c586   Data          20  uart_app.o(.rodata.str1.1)
    .L.str.2                                 0x0800c59a   Data           5  uart_app.o(.rodata.str1.1)
    .L.str.25                                0x0800c59a   Data           5  uart_app.o(.rodata.str1.1)
    .L.str.55                                0x0800c59f   Data          40  uart_app.o(.rodata.str1.1)
    .L.str.53                                0x0800c5c7   Data          39  uart_app.o(.rodata.str1.1)
    .L.str.57                                0x0800c5ee   Data          21  uart_app.o(.rodata.str1.1)
    .L.str.46                                0x0800c603   Data          19  uart_app.o(.rodata.str1.1)
    .L.str.47                                0x0800c616   Data          19  uart_app.o(.rodata.str1.1)
    .L.str.45                                0x0800c629   Data          19  uart_app.o(.rodata.str1.1)
    .L.str.44                                0x0800c63c   Data          19  uart_app.o(.rodata.str1.1)
    .L.str.41                                0x0800c64f   Data          19  uart_app.o(.rodata.str1.1)
    .L.str.54                                0x0800c662   Data          43  uart_app.o(.rodata.str1.1)
    .L.str.56                                0x0800c68d   Data          26  uart_app.o(.rodata.str1.1)
    .L.str.38                                0x0800c6a7   Data          22  uart_app.o(.rodata.str1.1)
    .L.str.49                                0x0800c6bd   Data          25  uart_app.o(.rodata.str1.1)
    .L.str.24                                0x0800c6d6   Data          23  uart_app.o(.rodata.str1.1)
    .L.str.15                                0x0800c6ed   Data          19  uart_app.o(.rodata.str1.1)
    .L.str.6                                 0x0800c700   Data          21  uart_app.o(.rodata.str1.1)
    .L.str.16                                0x0800c715   Data          19  uart_app.o(.rodata.str1.1)
    .L.str.20                                0x0800c728   Data          24  uart_app.o(.rodata.str1.1)
    .L.str.17                                0x0800c740   Data          16  uart_app.o(.rodata.str1.1)
    .L.str.18                                0x0800c750   Data          16  uart_app.o(.rodata.str1.1)
    .L.str.30                                0x0800c760   Data          20  uart_app.o(.rodata.str1.1)
    .L.str.13                                0x0800c774   Data          22  uart_app.o(.rodata.str1.1)
    .L.str.42                                0x0800c78a   Data           5  uart_app.o(.rodata.str1.1)
    .L.str.43                                0x0800c78f   Data           4  uart_app.o(.rodata.str1.1)
    .L.str.5                                 0x0800c793   Data           6  uart_app.o(.rodata.str1.1)
    .L.str.33                                0x0800c799   Data          11  uart_app.o(.rodata.str1.1)
    .L.str.26                                0x0800c7a4   Data          13  uart_app.o(.rodata.str1.1)
    .L.str.50                                0x0800c7b1   Data           5  uart_app.o(.rodata.str1.1)
    .L.str.31                                0x0800c7b6   Data          15  uart_app.o(.rodata.str1.1)
    .L.str.36                                0x0800c7c5   Data           9  uart_app.o(.rodata.str1.1)
    .L.str.39                                0x0800c7ce   Data          13  uart_app.o(.rodata.str1.1)
    .L.str.23                                0x0800c7db   Data           9  uart_app.o(.rodata.str1.1)
    .L.str.13                                0x0800c7e4   Data          55  uart2_app.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800c7e4   Section        0  uart2_app.o(.rodata.str1.1)
    .L.str.21                                0x0800c81b   Data          54  uart2_app.o(.rodata.str1.1)
    .L.str.45                                0x0800c851   Data          45  uart2_app.o(.rodata.str1.1)
    .L.str.18                                0x0800c87e   Data          42  uart2_app.o(.rodata.str1.1)
    .L.str.39                                0x0800c8a8   Data          39  uart2_app.o(.rodata.str1.1)
    .L.str.42                                0x0800c8cf   Data          31  uart2_app.o(.rodata.str1.1)
    .L.str.37                                0x0800c8ee   Data          42  uart2_app.o(.rodata.str1.1)
    .L.str.25                                0x0800c918   Data          33  uart2_app.o(.rodata.str1.1)
    .L.str.35                                0x0800c939   Data          43  uart2_app.o(.rodata.str1.1)
    .L.str.16                                0x0800c964   Data          44  uart2_app.o(.rodata.str1.1)
    .L.str.26                                0x0800c990   Data          39  uart2_app.o(.rodata.str1.1)
    .L.str.28                                0x0800c9b7   Data          46  uart2_app.o(.rodata.str1.1)
    .L.str.32                                0x0800c9e5   Data          57  uart2_app.o(.rodata.str1.1)
    .L.str.30                                0x0800ca1e   Data          52  uart2_app.o(.rodata.str1.1)
    .L.str.23                                0x0800ca52   Data          44  uart2_app.o(.rodata.str1.1)
    .L.str.8                                 0x0800ca7e   Data          53  uart2_app.o(.rodata.str1.1)
    .L.str.19                                0x0800cab3   Data          38  uart2_app.o(.rodata.str1.1)
    .L.str.40                                0x0800cad9   Data          28  uart2_app.o(.rodata.str1.1)
    .L.str.51                                0x0800caf5   Data          33  uart2_app.o(.rodata.str1.1)
    .L.str.55                                0x0800cb16   Data          24  uart2_app.o(.rodata.str1.1)
    .L.str.56                                0x0800cb2e   Data          33  uart2_app.o(.rodata.str1.1)
    .L.str.11                                0x0800cb4f   Data          43  uart2_app.o(.rodata.str1.1)
    .L.str.2                                 0x0800cb7a   Data          44  uart2_app.o(.rodata.str1.1)
    .L.str.57                                0x0800cba6   Data          28  uart2_app.o(.rodata.str1.1)
    .L.str.46                                0x0800cbc2   Data          29  uart2_app.o(.rodata.str1.1)
    .L.str.54                                0x0800cbdf   Data          41  uart2_app.o(.rodata.str1.1)
    .L.str.50                                0x0800cc08   Data          47  uart2_app.o(.rodata.str1.1)
    .L.str.48                                0x0800cc37   Data          55  uart2_app.o(.rodata.str1.1)
    .L.str.43                                0x0800cc6e   Data           9  uart2_app.o(.rodata.str1.1)
    .L.str.17                                0x0800cc77   Data           9  uart2_app.o(.rodata.str1.1)
    .L.str.15                                0x0800cc80   Data           9  uart2_app.o(.rodata.str1.1)
    .L.str.27                                0x0800cc89   Data          12  uart2_app.o(.rodata.str1.1)
    .L.str.14                                0x0800cc95   Data           8  uart2_app.o(.rodata.str1.1)
    .L.str.24                                0x0800cc9d   Data           7  uart2_app.o(.rodata.str1.1)
    .L.str.10                                0x0800cca4   Data          18  uart2_app.o(.rodata.str1.1)
    .L.str.38                                0x0800ccb6   Data          11  uart2_app.o(.rodata.str1.1)
    .L.str.34                                0x0800ccc1   Data          12  uart2_app.o(.rodata.str1.1)
    .L.str.44                                0x0800cccd   Data           7  uart2_app.o(.rodata.str1.1)
    .L.str.22                                0x0800ccd4   Data           8  uart2_app.o(.rodata.str1.1)
    .L.str.31                                0x0800ccdc   Data          13  uart2_app.o(.rodata.str1.1)
    .L.str.41                                0x0800cce4   Data           5  uart2_app.o(.rodata.str1.1)
    .L.str.33                                0x0800cce9   Data           6  uart2_app.o(.rodata.str1.1)
    .L.str.12                                0x0800ccef   Data          11  uart2_app.o(.rodata.str1.1)
    .L.str.9                                 0x0800ccf2   Data           8  uart2_app.o(.rodata.str1.1)
    .L.str.36                                0x0800ccfa   Data          12  uart2_app.o(.rodata.str1.1)
    .L.str.53                                0x0800cd06   Data           9  uart2_app.o(.rodata.str1.1)
    .L.str.52                                0x0800cd0f   Data          11  uart2_app.o(.rodata.str1.1)
    .L.str.29                                0x0800cd1a   Data           9  uart2_app.o(.rodata.str1.1)
    .L.str.7                                 0x0800cd23   Data           6  uart2_app.o(.rodata.str1.1)
    .L.str.19                                0x0800cd29   Data         114  esp01_app.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800cd29   Section        0  esp01_app.o(.rodata.str1.1)
    .L.str.27                                0x0800cd9b   Data         115  esp01_app.o(.rodata.str1.1)
    .L.str.9                                 0x0800ce0e   Data          21  esp01_app.o(.rodata.str1.1)
    .L.str                                   0x0800ce23   Data          37  esp01_app.o(.rodata.str1.1)
    .L.str.21                                0x0800ce48   Data          44  esp01_app.o(.rodata.str1.1)
    .L.str.3                                 0x0800ce74   Data          17  esp01_app.o(.rodata.str1.1)
    .L.str.37                                0x0800ceb6   Data          12  esp01_app.o(.rodata.str1.1)
    .L.str.24                                0x0800cec2   Data          14  esp01_app.o(.rodata.str1.1)
    .L.str.38                                0x0800cee8   Data          11  esp01_app.o(.rodata.str1.1)
    .L.str.4                                 0x0800cef3   Data           9  esp01_app.o(.rodata.str1.1)
    .L.str.22                                0x0800cf09   Data          16  esp01_app.o(.rodata.str1.1)
    .L.str.45                                0x0800cf2e   Data          28  esp01_app.o(.rodata.str1.1)
    .L.str.34                                0x0800cf4a   Data          25  esp01_app.o(.rodata.str1.1)
    .L.str.1                                 0x0800cf63   Data          20  esp01_app.o(.rodata.str1.1)
    .L.str.48                                0x0800cf77   Data          21  esp01_app.o(.rodata.str1.1)
    .L.str.49                                0x0800cf80   Data          28  esp01_app.o(.rodata.str1.1)
    .L.str.47                                0x0800cf8c   Data          21  esp01_app.o(.rodata.str1.1)
    .L.str.50                                0x0800cf9c   Data          13  esp01_app.o(.rodata.str1.1)
    .L.str.14                                0x0800cfa1   Data          25  esp01_app.o(.rodata.str1.1)
    .L.str.51                                0x0800cfa9   Data          11  esp01_app.o(.rodata.str1.1)
    .L.str.52                                0x0800cfb4   Data          10  esp01_app.o(.rodata.str1.1)
    .L.str.20                                0x0800cfba   Data          17  esp01_app.o(.rodata.str1.1)
    .L.str.53                                0x0800cfbe   Data          10  esp01_app.o(.rodata.str1.1)
    .L.str.54                                0x0800cfc8   Data          10  esp01_app.o(.rodata.str1.1)
    .L.str.8                                 0x0800cfcb   Data          12  esp01_app.o(.rodata.str1.1)
    .L.str.55                                0x0800cfd2   Data          24  esp01_app.o(.rodata.str1.1)
    .L.str.10                                0x0800cfd7   Data          12  esp01_app.o(.rodata.str1.1)
    .L.str.23                                0x0800cfe3   Data           3  esp01_app.o(.rodata.str1.1)
    .L.str.56                                0x0800cfea   Data          21  esp01_app.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800cffa   Section        0  gps_app.o(.rodata.str1.1)
    .L.str.13                                0x0800d001   Data          49  gps_app.o(.rodata.str1.1)
    .L.str                                   0x0800d02b   Data           7  gps_app.o(.rodata.str1.1)
    .L.str.16                                0x0800d032   Data           4  gps_app.o(.rodata.str1.1)
    .L.str.17                                0x0800d036   Data           4  gps_app.o(.rodata.str1.1)
    .L.str.21                                0x0800d03a   Data          18  gps_app.o(.rodata.str1.1)
    .L.str.45                                0x0800d03a   Data          33  navigation_app.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800d03a   Section        0  navigation_app.o(.rodata.str1.1)
    .L.str.33                                0x0800d05b   Data          41  navigation_app.o(.rodata.str1.1)
    .L.str.47                                0x0800d084   Data          33  navigation_app.o(.rodata.str1.1)
    .L.str.35                                0x0800d0a5   Data          41  navigation_app.o(.rodata.str1.1)
    .L.str.49                                0x0800d0ce   Data          33  navigation_app.o(.rodata.str1.1)
    .L.str.37                                0x0800d0ef   Data          41  navigation_app.o(.rodata.str1.1)
    .L.str.51                                0x0800d118   Data          30  navigation_app.o(.rodata.str1.1)
    .L.str.39                                0x0800d136   Data          38  navigation_app.o(.rodata.str1.1)
    .L.str.41                                0x0800d15c   Data          35  navigation_app.o(.rodata.str1.1)
    .L.str.43                                0x0800d17f   Data          47  navigation_app.o(.rodata.str1.1)
    .L.str.18                                0x0800d1ae   Data          43  navigation_app.o(.rodata.str1.1)
    .L.str.22                                0x0800d1d9   Data          22  navigation_app.o(.rodata.str1.1)
    .L.str.8                                 0x0800d1ef   Data          22  navigation_app.o(.rodata.str1.1)
    .L.str.4                                 0x0800d205   Data          25  navigation_app.o(.rodata.str1.1)
    .L.str.25                                0x0800d21e   Data          34  navigation_app.o(.rodata.str1.1)
    .L.str.28                                0x0800d240   Data          30  navigation_app.o(.rodata.str1.1)
    .L.str.19                                0x0800d25e   Data          18  navigation_app.o(.rodata.str1.1)
    .L.str.24                                0x0800d270   Data          35  navigation_app.o(.rodata.str1.1)
    .L.str.23                                0x0800d293   Data          26  navigation_app.o(.rodata.str1.1)
    .L.str.7                                 0x0800d2ad   Data          39  navigation_app.o(.rodata.str1.1)
    .L.str.26                                0x0800d2d4   Data          26  navigation_app.o(.rodata.str1.1)
    .L.str.9                                 0x0800d2ee   Data          21  navigation_app.o(.rodata.str1.1)
    .L.str.27                                0x0800d303   Data          33  navigation_app.o(.rodata.str1.1)
    .L.str.32                                0x0800d324   Data           6  navigation_app.o(.rodata.str1.1)
    .L.str.34                                0x0800d32a   Data           6  navigation_app.o(.rodata.str1.1)
    .L.str.36                                0x0800d330   Data           6  navigation_app.o(.rodata.str1.1)
    .L.str.38                                0x0800d336   Data           6  navigation_app.o(.rodata.str1.1)
    .L.str.40                                0x0800d33c   Data           6  navigation_app.o(.rodata.str1.1)
    .L.str.42                                0x0800d342   Data           6  navigation_app.o(.rodata.str1.1)
    .L.str.10                                0x0800d348   Data           7  navigation_app.o(.rodata.str1.1)
    .L.str.16                                0x0800d34f   Data           6  navigation_app.o(.rodata.str1.1)
    .L.str.14                                0x0800d355   Data          11  navigation_app.o(.rodata.str1.1)
    .L.str.15                                0x0800d360   Data           7  navigation_app.o(.rodata.str1.1)
    .L.str.12                                0x0800d367   Data           8  navigation_app.o(.rodata.str1.1)
    .L.str.13                                0x0800d36f   Data          12  navigation_app.o(.rodata.str1.1)
    .L.str.113                               0x0800d37b   Data          50  tft_app.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800d37b   Section        0  tft_app.o(.rodata.str1.1)
    .L.str.7                                 0x0800d415   Data          37  tft_app.o(.rodata.str1.1)
    .L.str.6                                 0x0800d43a   Data          41  tft_app.o(.rodata.str1.1)
    .L.str.4                                 0x0800d463   Data          41  tft_app.o(.rodata.str1.1)
    .L.str.5                                 0x0800d48c   Data          41  tft_app.o(.rodata.str1.1)
    .L.str.13                                0x0800d4b5   Data          33  tft_app.o(.rodata.str1.1)
    .L.str.14                                0x0800d4d6   Data          33  tft_app.o(.rodata.str1.1)
    .L.str.19                                0x0800d500   Data          32  tft_app.o(.rodata.str1.1)
    .L.str.20                                0x0800d520   Data          27  tft_app.o(.rodata.str1.1)
    .L.str.21                                0x0800d53b   Data          16  tft_app.o(.rodata.str1.1)
    .L.str.22                                0x0800d54b   Data          17  tft_app.o(.rodata.str1.1)
    .L.str.90                                0x0800d558   Data          37  tft_app.o(.rodata.str1.1)
    .L.str.23                                0x0800d55c   Data          17  tft_app.o(.rodata.str1.1)
    .L.str.29                                0x0800d56d   Data          35  tft_app.o(.rodata.str1.1)
    .L.str.30                                0x0800d590   Data          29  tft_app.o(.rodata.str1.1)
    .L.str.31                                0x0800d5ad   Data          15  tft_app.o(.rodata.str1.1)
    .L.str.32                                0x0800d5bc   Data          15  tft_app.o(.rodata.str1.1)
    .L.str.70                                0x0800d5c0   Data          32  tft_app.o(.rodata.str1.1)
    .L.str.33                                0x0800d5cb   Data          29  tft_app.o(.rodata.str1.1)
    .L.str.34                                0x0800d5e8   Data          15  tft_app.o(.rodata.str1.1)
    .L.str.35                                0x0800d5f7   Data          15  tft_app.o(.rodata.str1.1)
    .L.str                                   0x0800d5fa   Data          32  tft_app.o(.rodata.str1.1)
    .L.str.36                                0x0800d606   Data          26  tft_app.o(.rodata.str1.1)
    .L.str.95                                0x0800d61a   Data          24  tft_app.o(.rodata.str1.1)
    .L.str.37                                0x0800d620   Data          43  tft_app.o(.rodata.str1.1)
    .L.str.69                                0x0800d632   Data          35  tft_app.o(.rodata.str1.1)
    .L.str.38                                0x0800d64b   Data          27  tft_app.o(.rodata.str1.1)
    .L.str.103                               0x0800d655   Data          34  tft_app.o(.rodata.str1.1)
    .L.str.39                                0x0800d666   Data          19  tft_app.o(.rodata.str1.1)
    .L.str.64                                0x0800d677   Data          29  tft_app.o(.rodata.str1.1)
    .L.str.40                                0x0800d679   Data          19  tft_app.o(.rodata.str1.1)
    .L.str.44                                0x0800d68c   Data          32  tft_app.o(.rodata.str1.1)
    .L.str.45                                0x0800d6ac   Data          34  tft_app.o(.rodata.str1.1)
    .L.str.50                                0x0800d6ce   Data          35  tft_app.o(.rodata.str1.1)
    .L.str.51                                0x0800d6f1   Data          62  tft_app.o(.rodata.str1.1)
    .L.str.12                                0x0800d724   Data          29  tft_app.o(.rodata.str1.1)
    .L.str.52                                0x0800d72f   Data          20  tft_app.o(.rodata.str1.1)
    .L.str.53                                0x0800d743   Data          20  tft_app.o(.rodata.str1.1)
    .L.str.54                                0x0800d757   Data          42  tft_app.o(.rodata.str1.1)
    .L.str.11                                0x0800d761   Data          32  tft_app.o(.rodata.str1.1)
    .L.str.109                               0x0800d781   Data          32  tft_app.o(.rodata.str1.1)
    .L.str.55                                0x0800d781   Data          38  tft_app.o(.rodata.str1.1)
    .L.str.72                                0x0800d7a1   Data          29  tft_app.o(.rodata.str1.1)
    .L.str.56                                0x0800d7a7   Data          38  tft_app.o(.rodata.str1.1)
    .L.str.71                                0x0800d7be   Data          32  tft_app.o(.rodata.str1.1)
    .L.str.57                                0x0800d7cd   Data          39  tft_app.o(.rodata.str1.1)
    .L.str.108                               0x0800d7fb   Data          37  tft_app.o(.rodata.str1.1)
    .L.str.92                                0x0800d8a1   Data          31  tft_app.o(.rodata.str1.1)
    .L.str.84                                0x0800d8ea   Data          60  tft_app.o(.rodata.str1.1)
    .L.str.112                               0x0800d8fa   Data          38  tft_app.o(.rodata.str1.1)
    .L.str.114                               0x0800d920   Data          50  tft_app.o(.rodata.str1.1)
    .L.str.85                                0x0800d926   Data          58  tft_app.o(.rodata.str1.1)
    .L.str.86                                0x0800d960   Data          87  tft_app.o(.rodata.str1.1)
    .L.str.96                                0x0800d984   Data          12  tft_app.o(.rodata.str1.1)
    .L.str.3                                 0x0800d990   Data          35  tft_app.o(.rodata.str1.1)
    .L.str.2                                 0x0800d9b3   Data          35  tft_app.o(.rodata.str1.1)
    .L.str.87                                0x0800d9b7   Data          62  tft_app.o(.rodata.str1.1)
    .L.str.68                                0x0800d9e5   Data          32  tft_app.o(.rodata.str1.1)
    .L.str.91                                0x0800da05   Data          51  tft_app.o(.rodata.str1.1)
    .L.str.93                                0x0800da38   Data          42  tft_app.o(.rodata.str1.1)
    .L.str.99                                0x0800da71   Data          42  tft_app.o(.rodata.str1.1)
    .L.str.111                               0x0800daf2   Data          33  tft_app.o(.rodata.str1.1)
    .L.str.98                                0x0800db22   Data          54  tft_app.o(.rodata.str1.1)
    .L.str.94                                0x0800db58   Data          42  tft_app.o(.rodata.str1.1)
    .L.str.73                                0x0800dc1d   Data          14  tft_app.o(.rodata.str1.1)
    .L.str.75                                0x0800dc2b   Data          14  tft_app.o(.rodata.str1.1)
    .L.str.78                                0x0800dc39   Data          17  tft_app.o(.rodata.str1.1)
    .L.str.74                                0x0800dc4a   Data          12  tft_app.o(.rodata.str1.1)
    .L.str.110                               0x0800dc56   Data          13  tft_app.o(.rodata.str1.1)
    .L.str.19                                0x0800dc63   Data           3  lcd_map_display.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800dc63   Section        0  lcd_map_display.o(.rodata.str1.1)
    .L.str.6                                 0x0800dc66   Data          22  lcd_map_display.o(.rodata.str1.1)
    .L.str.8                                 0x0800dc7c   Data          26  lcd_map_display.o(.rodata.str1.1)
    .L.str.3                                 0x0800dc96   Data           7  lcd_map_display.o(.rodata.str1.1)
    .L.str.4                                 0x0800dc9d   Data           6  lcd_map_display.o(.rodata.str1.1)
    .L.str.14                                0x0800dca3   Data          22  lcd_map_display.o(.rodata.str1.1)
    .L.str                                   0x0800dcb9   Data          21  lcd_map_display.o(.rodata.str1.1)
    _errno                                   0x20000000   Data           4  errno.o(.data)
    .data                                    0x20000000   Section        4  errno.o(.data)
    .L_MergedGlobals                         0x20000004   Data           8  stm32f4xx_hal.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000004   Section        0  stm32f4xx_hal.o(.data..L_MergedGlobals)
    .L_MergedGlobals                         0x2000000c   Data           8  esp01_app.o(.data..L_MergedGlobals)
    sim_lat                                  0x2000000c   Data           4  esp01_app.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x2000000c   Section        0  esp01_app.o(.data..L_MergedGlobals)
    sim_lon                                  0x20000010   Data           4  esp01_app.o(.data..L_MergedGlobals)
    .L_MergedGlobals                         0x20000014   Data           8  tft_app.o(.data..L_MergedGlobals)
    tft_Task.demo_lat                        0x20000014   Data           4  tft_app.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000014   Section        0  tft_app.o(.data..L_MergedGlobals)
    tft_Task.demo_lon                        0x20000018   Data           4  tft_app.o(.data..L_MergedGlobals)
    scheduler_task                           0x20000020   Data          96  scheduler.o(.data.scheduler_task)
    [Anonymous Symbol]                       0x20000020   Section        0  scheduler.o(.data.scheduler_task)
    .L_MergedGlobals                         0x20000080   Data           8  uart_app.o(.bss..L_MergedGlobals)
    Uart_Task.last_heartbeat                 0x20000080   Data           4  uart_app.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000080   Section        0  uart_app.o(.bss..L_MergedGlobals)
    Uart_Task.heartbeat_count                0x20000084   Data           4  uart_app.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x20000088   Data          36  gps_app.o(.bss..L_MergedGlobals)
    gps_filter.2                             0x20000088   Data           1  gps_app.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000088   Section        0  gps_app.o(.bss..L_MergedGlobals)
    gps_filter.3                             0x2000008c   Data           1  gps_app.o(.bss..L_MergedGlobals)
    gps_filter.0                             0x20000090   Data           4  gps_app.o(.bss..L_MergedGlobals)
    gps_filter.1                             0x20000094   Data           4  gps_app.o(.bss..L_MergedGlobals)
    gps_filter.4                             0x20000098   Data           4  gps_app.o(.bss..L_MergedGlobals)
    gps_filter.5                             0x2000009c   Data           4  gps_app.o(.bss..L_MergedGlobals)
    .L_MergedGlobals.121                     0x200000ac   Data           8  tft_app.o(.bss..L_MergedGlobals.121)
    tft_Task.last_update                     0x200000ac   Data           4  tft_app.o(.bss..L_MergedGlobals.121)
    [Anonymous Symbol]                       0x200000ac   Section        0  tft_app.o(.bss..L_MergedGlobals.121)
    tft_Task.step                            0x200000b0   Data           4  tft_app.o(.bss..L_MergedGlobals.121)
    .L_MergedGlobals.64                      0x200000b4   Data          16  esp01_app.o(.bss..L_MergedGlobals.64)
    esp01_state                              0x200000b4   Data           1  esp01_app.o(.bss..L_MergedGlobals.64)
    [Anonymous Symbol]                       0x200000b4   Section        0  esp01_app.o(.bss..L_MergedGlobals.64)
    wifi_connected                           0x200000b5   Data           1  esp01_app.o(.bss..L_MergedGlobals.64)
    tcp_connected                            0x200000b6   Data           1  esp01_app.o(.bss..L_MergedGlobals.64)
    esp01_Task.first_run                     0x200000b7   Data           1  esp01_app.o(.bss..L_MergedGlobals.64)
    esp01_CheckAndReinit.last_check_time     0x200000b8   Data           4  esp01_app.o(.bss..L_MergedGlobals.64)
    esp01_Task.last_check_time               0x200000bc   Data           4  esp01_app.o(.bss..L_MergedGlobals.64)
    esp01_Task.init_start_time               0x200000c0   Data           4  esp01_app.o(.bss..L_MergedGlobals.64)
    GPS_AutoUpload_Task.upload_count         0x200000c4   Data           4  scheduler.o(.bss.GPS_AutoUpload_Task.upload_count)
    [Anonymous Symbol]                       0x200000c4   Section        0  scheduler.o(.bss.GPS_AutoUpload_Task.upload_count)
    LCD_Map_Task.last_update                 0x200000c8   Data           4  lcd_map_display.o(.bss.LCD_Map_Task.last_update)
    [Anonymous Symbol]                       0x200000c8   Section        0  lcd_map_display.o(.bss.LCD_Map_Task.last_update)
    Uart2_Task.last_receive_time             0x200001c0   Data           4  uart2_app.o(.bss.Uart2_Task.last_receive_time)
    [Anonymous Symbol]                       0x200001c0   Section        0  uart2_app.o(.bss.Uart2_Task.last_receive_time)
    Uart6_Task.last_heartbeat                0x200001c4   Data           4  uart6_app.o(.bss.Uart6_Task.last_heartbeat)
    [Anonymous Symbol]                       0x200001c4   Section        0  uart6_app.o(.bss.Uart6_Task.last_heartbeat)
    STACK                                    0x20002f60   Section     4096  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printfstubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  printfstubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000199   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000199   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x0800019d   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x080001c1   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x08000223   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000223   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000223   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08000247   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000247   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000247   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000255   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000255   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000255   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000259   Thumb Code    18  memseta.o(.text)
    strstr                                   0x0800026b   Thumb Code    36  strstr.o(.text)
    strncpy                                  0x0800028f   Thumb Code    24  strncpy.o(.text)
    strchr                                   0x080002a7   Thumb Code    20  strchr.o(.text)
    strlen                                   0x080002bb   Thumb Code    14  strlen.o(.text)
    strcmp                                   0x080002c9   Thumb Code    28  strcmp.o(.text)
    memcmp                                   0x080002e5   Thumb Code    26  memcmp.o(.text)
    strncmp                                  0x080002ff   Thumb Code    30  strncmp.o(.text)
    __aeabi_dadd                             0x0800031d   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x0800045f   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000465   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x0800046b   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x0800054f   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2iz                             0x0800062d   Thumb Code    62  dfixi.o(.text)
    __aeabi_f2d                              0x0800066b   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x08000691   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x080006c9   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080006c9   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x080006f5   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080006f5   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000713   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000713   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x08000733   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000733   Thumb Code     0  llsshr.o(.text)
    __strtod_int                             0x08000797   Thumb Code    94  strtod.o(.text)
    __I$use$fp                               0x08000801   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08000801   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08000813   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x0800086f   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x0800088d   Thumb Code   156  depilogue.o(.text)
    _dsqrt                                   0x08000929   Thumb Code   162  dsqrt.o(.text)
    _drem                                    0x080009cb   Thumb Code   134  drem.o(.text)
    __aeabi_d2ulz                            0x08000a51   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdcmpeq                          0x08000a81   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x08000a81   Thumb Code    48  cdcmple.o(.text)
    __aeabi_cdrcmple                         0x08000ab1   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000ae1   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x08000ae1   Thumb Code     0  init.o(.text)
    isspace                                  0x08000b11   Thumb Code    10  isspace_c.o(.text)
    _scanf_real                              0x08000c45   Thumb Code     0  scanf_fp.o(.text)
    _scanf_really_real                       0x08000c45   Thumb Code   556  scanf_fp.o(.text)
    _sgetc                                   0x08000e7d   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08000e9b   Thumb Code    34  _sgetc.o(.text)
    __aeabi_i2d                              0x08000ebd   Thumb Code    34  dflti.o(.text)
    __aeabi_ui2d                             0x08000edf   Thumb Code    26  dfltui.o(.text)
    __ctype_lookup                           0x08000ef9   Thumb Code    34  ctype_c.o(.text)
    __aeabi_ul2d                             0x08000f21   Thumb Code    24  dfltul.o(.text)
    BusFault_Handler                         0x08000f39   Thumb Code     2  stm32f4xx_it.o(.text.BusFault_Handler)
    DMA1_Stream1_IRQHandler                  0x08000f3d   Thumb Code    12  stm32f4xx_it.o(.text.DMA1_Stream1_IRQHandler)
    DMA1_Stream5_IRQHandler                  0x08000f49   Thumb Code    12  stm32f4xx_it.o(.text.DMA1_Stream5_IRQHandler)
    DMA2_Stream1_IRQHandler                  0x08000f55   Thumb Code    12  stm32f4xx_it.o(.text.DMA2_Stream1_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x08000f61   Thumb Code    12  stm32f4xx_it.o(.text.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x08000f6d   Thumb Code     2  stm32f4xx_it.o(.text.DebugMon_Handler)
    Draw_Circle                              0x08000f71   Thumb Code   280  lcd_display_hal.o(.text.Draw_Circle)
    Error_Handler                            0x08001089   Thumb Code     6  main.o(.text.Error_Handler)
    GPS_AutoUpload_Task                      0x08001091   Thumb Code    48  scheduler.o(.text.GPS_AutoUpload_Task)
    GPS_Task                                 0x080010ed   Thumb Code    88  gps_app.o(.text.GPS_Task)
    GPS_Virtual_Init                         0x08001145   Thumb Code     2  gps_app.o(.text.GPS_Virtual_Init)
    HAL_DMA_Abort                            0x08001149   Thumb Code   142  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080011d9   Thumb Code    36  stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x080011fd   Thumb Code   452  stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x080013c1   Thumb Code   354  stm32f4xx_hal_dma.o(.text.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08001525   Thumb Code   162  stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT)
    HAL_Delay                                0x080015c9   Thumb Code    40  stm32f4xx_hal.o(.text.HAL_Delay)
    HAL_GPIO_Init                            0x080015f1   Thumb Code   414  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08001791   Thumb Code    10  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin)
    HAL_GPIO_TogglePin                       0x0800179d   Thumb Code    16  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x080017ad   Thumb Code    10  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080017b9   Thumb Code    12  stm32f4xx_hal.o(.text.HAL_GetTick)
    HAL_I2C_Init                             0x080017c5   Thumb Code   356  stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init)
    HAL_I2C_MspInit                          0x08001929   Thumb Code   134  i2c.o(.text.HAL_I2C_MspInit)
    HAL_IncTick                              0x080019b1   Thumb Code    26  stm32f4xx_hal.o(.text.HAL_IncTick)
    HAL_Init                                 0x080019cd   Thumb Code    54  stm32f4xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x08001a05   Thumb Code    72  stm32f4xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08001a4d   Thumb Code    56  stm32f4xx_hal_msp.o(.text.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001a85   Thumb Code    34  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001aa9   Thumb Code    86  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08001b01   Thumb Code    32  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001b21   Thumb Code   356  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08001c85   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08001cad   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08001cd5   Thumb Code   108  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001d41   Thumb Code   940  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x080020ed   Thumb Code    44  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HAL_TIMEx_ConfigBreakDeadTime            0x08002119   Thumb Code    76  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x08002165   Thumb Code   186  stm32f4xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08002221   Thumb Code    90  stm32f4xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x0800227d   Thumb Code    50  tim.o(.text.HAL_TIM_Base_MspInit)
    HAL_TIM_ConfigClockSource                0x080022b1   Thumb Code   416  stm32f4xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x08002451   Thumb Code   178  stm32f4xx_hal_tim.o(.text.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08002505   Thumb Code   192  tim.o(.text.HAL_TIM_Encoder_MspInit)
    HAL_TIM_PWM_ConfigChannel                0x080025c5   Thumb Code   544  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x080027e5   Thumb Code    90  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08002841   Thumb Code     2  stm32f4xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08002845   Thumb Code   494  stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08002a35   Thumb Code   192  uart_driver.o(.text.HAL_UARTEx_RxEventCallback)
    HAL_UARTEx_RxEventCallback_UART2         0x08002af5   Thumb Code   116  uart2_driver.o(.text.HAL_UARTEx_RxEventCallback_UART2)
    HAL_UARTEx_RxEventCallback_UART3         0x08002b69   Thumb Code    96  uart3_driver.o(.text.HAL_UARTEx_RxEventCallback_UART3)
    HAL_UARTEx_RxEventCallback_UART6         0x08002bc9   Thumb Code   100  uart6_driver.o(.text.HAL_UARTEx_RxEventCallback_UART6)
    HAL_UART_DMAStop                         0x08002c2d   Thumb Code   540  stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x08002e49   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08002e4d   Thumb Code  1428  stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080033e1   Thumb Code    96  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    HAL_UART_MspInit                         0x08003441   Thumb Code   840  usart.o(.text.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08003789   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x0800378d   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08003791   Thumb Code   402  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08003925   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08003929   Thumb Code     2  stm32f4xx_it.o(.text.HardFault_Handler)
    LCD_Address_Set                          0x0800392d   Thumb Code   186  lcd_init_hal.o(.text.LCD_Address_Set)
    LCD_DrawLine                             0x080039e9   Thumb Code   158  lcd_display_hal.o(.text.LCD_DrawLine)
    LCD_DrawRectangle                        0x08003a89   Thumb Code   276  lcd_display_hal.o(.text.LCD_DrawRectangle)
    LCD_Fill                                 0x08003b9d   Thumb Code    72  lcd_display_hal.o(.text.LCD_Fill)
    LCD_Init                                 0x08003be5   Thumb Code  1648  lcd_init_hal.o(.text.LCD_Init)
    LCD_Map_CalculateBearing                 0x080042e1   Thumb Code   408  lcd_map_display.o(.text.LCD_Map_CalculateBearing)
    LCD_Map_CalculateRouteDistance           0x0800449d   Thumb Code   392  lcd_map_display.o(.text.LCD_Map_CalculateRouteDistance)
    LCD_Map_DisplayNavigationInstructions    0x08004635   Thumb Code   560  lcd_map_display.o(.text.LCD_Map_DisplayNavigationInstructions)
    LCD_Map_DrawDirectionArrow               0x08004919   Thumb Code   328  lcd_map_display.o(.text.LCD_Map_DrawDirectionArrow)
    LCD_Map_DrawNavigationStep               0x08004a9d   Thumb Code   232  lcd_map_display.o(.text.LCD_Map_DrawNavigationStep)
    LCD_Map_Init                             0x08004b85   Thumb Code   134  lcd_map_display.o(.text.LCD_Map_Init)
    LCD_Map_SetDestination                   0x08004c0d   Thumb Code    84  lcd_map_display.o(.text.LCD_Map_SetDestination)
    LCD_Map_Task                             0x08004c61   Thumb Code    88  lcd_map_display.o(.text.LCD_Map_Task)
    LCD_Map_UpdatePosition                   0x08004ccd   Thumb Code    42  lcd_map_display.o(.text.LCD_Map_UpdatePosition)
    LCD_QuickPinTest                         0x08004cf9   Thumb Code   692  tft_app.o(.text.LCD_QuickPinTest)
    LCD_ShowChar                             0x08005035   Thumb Code   876  lcd_display_hal.o(.text.LCD_ShowChar)
    LCD_ShowIntNum                           0x080053a1   Thumb Code   282  lcd_display_hal.o(.text.LCD_ShowIntNum)
    LCD_ShowString                           0x080054bd   Thumb Code    70  lcd_display_hal.o(.text.LCD_ShowString)
    LCD_SimpleSPITest                        0x08005505   Thumb Code   532  tft_app.o(.text.LCD_SimpleSPITest)
    LCD_WR_DATA                              0x080057a1   Thumb Code    20  lcd_init_hal.o(.text.LCD_WR_DATA)
    LCD_WR_DATA8                             0x080057b5   Thumb Code     4  lcd_init_hal.o(.text.LCD_WR_DATA8)
    LCD_WR_REG                               0x080057b9   Thumb Code    42  lcd_init_hal.o(.text.LCD_WR_REG)
    LCD_Writ_Bus                             0x080057e5   Thumb Code   318  lcd_init_hal.o(.text.LCD_Writ_Bus)
    MX_DMA_Init                              0x08005925   Thumb Code   124  dma.o(.text.MX_DMA_Init)
    MX_GPIO_Init                             0x080059a1   Thumb Code   400  gpio.o(.text.MX_GPIO_Init)
    MX_I2C2_Init                             0x08005b31   Thumb Code    66  i2c.o(.text.MX_I2C2_Init)
    MX_TIM1_Init                             0x08005b75   Thumb Code   358  tim.o(.text.MX_TIM1_Init)
    MX_TIM3_Init                             0x08005cdd   Thumb Code   104  tim.o(.text.MX_TIM3_Init)
    MX_TIM4_Init                             0x08005d45   Thumb Code   104  tim.o(.text.MX_TIM4_Init)
    MX_UART4_Init                            0x08005dad   Thumb Code    60  usart.o(.text.MX_UART4_Init)
    MX_USART1_UART_Init                      0x08005de9   Thumb Code    60  usart.o(.text.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08005e25   Thumb Code    60  usart.o(.text.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x08005e61   Thumb Code    60  usart.o(.text.MX_USART3_UART_Init)
    MX_USART6_UART_Init                      0x08005e9d   Thumb Code    60  usart.o(.text.MX_USART6_UART_Init)
    MemManage_Handler                        0x08005ed9   Thumb Code     2  stm32f4xx_it.o(.text.MemManage_Handler)
    NMI_Handler                              0x08005edd   Thumb Code     2  stm32f4xx_it.o(.text.NMI_Handler)
    Navigation_CalculateBearing              0x08005ee1   Thumb Code   392  navigation_app.o(.text.Navigation_CalculateBearing)
    Navigation_CalculateDistance             0x0800608d   Thumb Code   376  navigation_app.o(.text.Navigation_CalculateDistance)
    Navigation_FindDestination               0x08006211   Thumb Code   314  navigation_app.o(.text.Navigation_FindDestination)
    Navigation_PlanRoute                     0x0800634d   Thumb Code   436  navigation_app.o(.text.Navigation_PlanRoute)
    Navigation_ProcessCommand                0x08006585   Thumb Code  1240  navigation_app.o(.text.Navigation_ProcessCommand)
    Navigation_StartNavigation               0x08006b15   Thumb Code   656  navigation_app.o(.text.Navigation_StartNavigation)
    Navigation_Task                          0x08006e35   Thumb Code    52  navigation_app.o(.text.Navigation_Task)
    PendSV_Handler                           0x08006e85   Thumb Code     2  stm32f4xx_it.o(.text.PendSV_Handler)
    SVC_Handler                              0x08006e89   Thumb Code     2  stm32f4xx_it.o(.text.SVC_Handler)
    Scheduler_Init                           0x08006e8d   Thumb Code    40  scheduler.o(.text.Scheduler_Init)
    Scheduler_Run                            0x08006eb5   Thumb Code    74  scheduler.o(.text.Scheduler_Run)
    SysTick_Handler                          0x08006f01   Thumb Code     4  stm32f4xx_it.o(.text.SysTick_Handler)
    SystemClock_Config                       0x08006f05   Thumb Code   168  main.o(.text.SystemClock_Config)
    SystemInit                               0x08006fad   Thumb Code    18  system_stm32f4xx.o(.text.SystemInit)
    TIM_Base_SetConfig                       0x08006fc1   Thumb Code   314  stm32f4xx_hal_tim.o(.text.TIM_Base_SetConfig)
    UART4_IRQHandler                         0x080070fd   Thumb Code    12  stm32f4xx_it.o(.text.UART4_IRQHandler)
    USART1_IRQHandler                        0x080075f1   Thumb Code    12  stm32f4xx_it.o(.text.USART1_IRQHandler)
    USART2_IRQHandler                        0x080075fd   Thumb Code    12  stm32f4xx_it.o(.text.USART2_IRQHandler)
    USART3_IRQHandler                        0x08007609   Thumb Code    12  stm32f4xx_it.o(.text.USART3_IRQHandler)
    USART6_IRQHandler                        0x08007615   Thumb Code    12  stm32f4xx_it.o(.text.USART6_IRQHandler)
    Uart2_Init                               0x08007621   Thumb Code    70  uart2_app.o(.text.Uart2_Init)
    Uart2_Printf                             0x08007669   Thumb Code    80  uart2_driver.o(.text.Uart2_Printf)
    Uart2_Task                               0x080076d5   Thumb Code  1412  uart2_app.o(.text.Uart2_Task)
    Uart3_Task                               0x08007cf5   Thumb Code    14  uart3_app.o(.text.Uart3_Task)
    Uart6_Init                               0x08007d05   Thumb Code    88  uart6_app.o(.text.Uart6_Init)
    Uart6_Task                               0x08007d85   Thumb Code    56  uart6_app.o(.text.Uart6_Task)
    Uart_Init                                0x08007dd5   Thumb Code    88  uart_app.o(.text.Uart_Init)
    Uart_Task                                0x08007e6d   Thumb Code  1508  uart_app.o(.text.Uart_Task)
    UsageFault_Handler                       0x080084fd   Thumb Code     2  stm32f4xx_it.o(.text.UsageFault_Handler)
    esp01_CheckConnection                    0x08008501   Thumb Code   212  esp01_app.o(.text.esp01_CheckConnection)
    esp01_GetRealLocation                    0x08008665   Thumb Code    42  esp01_app.o(.text.esp01_GetRealLocation)
    esp01_GetState                           0x08008691   Thumb Code    12  esp01_app.o(.text.esp01_GetState)
    esp01_Init                               0x0800869d   Thumb Code   208  esp01_app.o(.text.esp01_Init)
    esp01_ReconnectWiFi                      0x080087f1   Thumb Code   168  esp01_app.o(.text.esp01_ReconnectWiFi)
    esp01_Reset                              0x080088e9   Thumb Code    72  esp01_app.o(.text.esp01_Reset)
    esp01_ResetTCPState                      0x08008931   Thumb Code    14  esp01_app.o(.text.esp01_ResetTCPState)
    esp01_SetConnected                       0x08008941   Thumb Code    16  esp01_app.o(.text.esp01_SetConnected)
    esp01_SetDataSendReady                   0x08008951   Thumb Code    16  esp01_app.o(.text.esp01_SetDataSendReady)
    esp01_SetTCPConnected                    0x08008979   Thumb Code    14  esp01_app.o(.text.esp01_SetTCPConnected)
    esp01_StartInit                          0x08008989   Thumb Code    24  esp01_app.o(.text.esp01_StartInit)
    esp01_Task                               0x080089c5   Thumb Code   188  esp01_app.o(.text.esp01_Task)
    esp01_UploadGPSData                      0x08008acd   Thumb Code   268  esp01_app.o(.text.esp01_UploadGPSData)
    esp01_UploadNavigationCommand            0x08008c0d   Thumb Code   260  esp01_app.o(.text.esp01_UploadNavigationCommand)
    main                                     0x08008d99   Thumb Code    62  main.o(.text.main)
    my_printf                                0x08008dd9   Thumb Code    62  usart.o(.text.my_printf)
    parseGpsBuffer                           0x08008e19   Thumb Code   496  gps_app.o(.text.parseGpsBuffer)
    rt_ringbuffer_data_len                   0x08009019   Thumb Code    68  ringbuffer.o(.text.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x0800905d   Thumb Code   184  ringbuffer.o(.text.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x08009115   Thumb Code    14  ringbuffer.o(.text.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x08009125   Thumb Code   178  ringbuffer.o(.text.rt_ringbuffer_put)
    rt_ringbuffer_reset                      0x080091d9   Thumb Code     6  ringbuffer.o(.text.rt_ringbuffer_reset)
    tft_BasicTest                            0x080091e1   Thumb Code   348  tft_app.o(.text.tft_BasicTest)
    tft_HardwareDiagnose                     0x080093c9   Thumb Code  1120  tft_app.o(.text.tft_HardwareDiagnose)
    tft_Init                                 0x080098b9   Thumb Code   552  tft_app.o(.text.tft_Init)
    tft_SetMapDestination                    0x08009b95   Thumb Code    80  tft_app.o(.text.tft_SetMapDestination)
    tft_Task                                 0x08009c1d   Thumb Code   184  tft_app.o(.text.tft_Task)
    tft_UpdateMapPosition                    0x08009cd9   Thumb Code   106  tft_app.o(.text.tft_UpdateMapPosition)
    __0snprintf                              0x08009d45   Thumb Code    48  printfa.o(i.__0snprintf)
    __1snprintf                              0x08009d45   Thumb Code     0  printfa.o(i.__0snprintf)
    __2snprintf                              0x08009d45   Thumb Code     0  printfa.o(i.__0snprintf)
    __c89snprintf                            0x08009d45   Thumb Code     0  printfa.o(i.__0snprintf)
    snprintf                                 0x08009d45   Thumb Code     0  printfa.o(i.__0snprintf)
    __0vsnprintf                             0x08009d79   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x08009d79   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x08009d79   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x08009d79   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x08009d79   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __ARM_fpclassify                         0x08009dad   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_atan                            0x08009de1   Thumb Code   622  atan.o(i.__hardfp_atan)
    __hardfp_atan2                           0x0800a0b9   Thumb Code   448  atan2.o(i.__hardfp_atan2)
    __hardfp_atof                            0x0800a2b9   Thumb Code    46  atof.o(i.__hardfp_atof)
    __hardfp_cos                             0x0800a2f1   Thumb Code   180  cos.o(i.__hardfp_cos)
    __hardfp_fmod                            0x0800a3b9   Thumb Code   254  fmod.o(i.__hardfp_fmod)
    __hardfp_sin                             0x0800a4c1   Thumb Code   180  sin.o(i.__hardfp_sin)
    __hardfp_sqrt                            0x0800a589   Thumb Code   122  sqrt.o(i.__hardfp_sqrt)
    __ieee754_rem_pio2                       0x0800a609   Thumb Code   938  rred.o(i.__ieee754_rem_pio2)
    __kernel_cos                             0x0800aa41   Thumb Code   322  cos_i.o(i.__kernel_cos)
    __kernel_poly                            0x0800abb1   Thumb Code   248  poly.o(i.__kernel_poly)
    __kernel_sin                             0x0800aca9   Thumb Code   280  sin_i.o(i.__kernel_sin)
    __mathlib_dbl_infnan                     0x0800add9   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x0800aded   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x0800ae01   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x0800ae21   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    __read_errno                             0x0800ae41   Thumb Code     6  errno.o(i.__read_errno)
    __scatterload_copy                       0x0800ae4d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800ae5b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x0800ae5d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x0800ae6d   Thumb Code     6  errno.o(i.__set_errno)
    _is_digit                                0x0800affd   Thumb Code    14  scanf_fp.o(i._is_digit)
    atan                                     0x0800b751   Thumb Code    16  atan.o(i.atan)
    fabs                                     0x0800b761   Thumb Code    24  fabs.o(i.fabs)
    __mathlib_zero                           0x0800b840   Data           8  qnan.o(.constdata)
    __ctype_categories                       0x0800b940   Data          64  ctype_c.o(.constdata)
    AHBPrescTable                            0x0800ba7c   Data          16  system_stm32f4xx.o(.rodata.AHBPrescTable)
    APBPrescTable                            0x0800ba8c   Data           8  system_stm32f4xx.o(.rodata.APBPrescTable)
    Region$$Table$$Base                      0x0800dcd0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800dcf0   Number         0  anon$$obj.o(Region$$Table)
    uwTickFreq                               0x20000004   Data           1  stm32f4xx_hal.o(.data..L_MergedGlobals)
    uwTickPrio                               0x20000008   Data           4  stm32f4xx_hal.o(.data..L_MergedGlobals)
    SystemCoreClock                          0x2000001c   Data           4  system_stm32f4xx.o(.data.SystemCoreClock)
    g_LatAndLongData                         0x200000a0   Data          12  gps_app.o(.bss..L_MergedGlobals)
    Save_Data                                0x200000cc   Data         241  gps_app.o(.bss.Save_Data)
    current_navigation                       0x200001c8   Data        7320  navigation_app.o(.bss.current_navigation)
    g_map_state                              0x20001e60   Data          48  lcd_map_display.o(.bss.g_map_state)
    hdma_usart1_rx                           0x20001e90   Data          96  usart.o(.bss.hdma_usart1_rx)
    hdma_usart2_rx                           0x20001ef0   Data          96  usart.o(.bss.hdma_usart2_rx)
    hdma_usart3_rx                           0x20001f50   Data          96  usart.o(.bss.hdma_usart3_rx)
    hdma_usart6_rx                           0x20001fb0   Data          96  usart.o(.bss.hdma_usart6_rx)
    hi2c2                                    0x20002010   Data          84  i2c.o(.bss.hi2c2)
    htim1                                    0x20002064   Data          72  tim.o(.bss.htim1)
    htim3                                    0x200020ac   Data          72  tim.o(.bss.htim3)
    htim4                                    0x200020f4   Data          72  tim.o(.bss.htim4)
    huart1                                   0x2000213c   Data          72  usart.o(.bss.huart1)
    huart2                                   0x20002184   Data          72  usart.o(.bss.huart2)
    huart3                                   0x200021cc   Data          72  usart.o(.bss.huart3)
    huart4                                   0x20002214   Data          72  usart.o(.bss.huart4)
    huart6                                   0x2000225c   Data          72  usart.o(.bss.huart6)
    nav_state                                0x200022a4   Data           1  navigation_app.o(.bss.nav_state)
    ring_buffer                              0x200022a8   Data          12  uart_driver.o(.bss.ring_buffer)
    ring_buffer_input                        0x200022b4   Data         128  uart_driver.o(.bss.ring_buffer_input)
    task_num                                 0x20002334   Data           1  scheduler.o(.bss.task_num)
    uart2_data_buffer                        0x20002335   Data         512  uart2_driver.o(.bss.uart2_data_buffer)
    uart2_ring_buffer                        0x20002538   Data          12  uart2_driver.o(.bss.uart2_ring_buffer)
    uart2_ring_buffer_input                  0x20002544   Data         512  uart2_driver.o(.bss.uart2_ring_buffer_input)
    uart2_rx_dma_buffer                      0x20002744   Data         512  uart2_driver.o(.bss.uart2_rx_dma_buffer)
    uart3_data_buffer                        0x20002944   Data         128  uart3_driver.o(.bss.uart3_data_buffer)
    uart3_ring_buffer                        0x200029c4   Data          12  uart3_driver.o(.bss.uart3_ring_buffer)
    uart3_rx_dma_buffer                      0x200029d0   Data         128  uart3_driver.o(.bss.uart3_rx_dma_buffer)
    uart6_ring_buffer                        0x20002a50   Data          12  uart6_driver.o(.bss.uart6_ring_buffer)
    uart6_ring_buffer_input                  0x20002a5c   Data         512  uart6_driver.o(.bss.uart6_ring_buffer_input)
    uart6_rx_dma_buffer                      0x20002c5c   Data         512  uart6_driver.o(.bss.uart6_rx_dma_buffer)
    uart_data_buffer                         0x20002e5c   Data         128  uart_driver.o(.bss.uart_data_buffer)
    uart_rx_dma_buffer                       0x20002edc   Data         128  uart_driver.o(.bss.uart_rx_dma_buffer)
    uwTick                                   0x20002f5c   Data           4  stm32f4xx_hal.o(.bss.uwTick)
    __initial_sp                             0x20003f60   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000dd70, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000dcf0, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         1719  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         2056    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         2059    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         2061    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         2063    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         2064    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         2066    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000198   0x08000198   0x00000000   Code   RO         2068    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         2057    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x0800019c   0x0800019c   0x00000024   Code   RO            4    .text               startup_stm32f407xx.o
    0x080001c0   0x080001c0   0x00000062   Code   RO         1722    .text               mc_w.l(uldiv.o)
    0x08000222   0x08000222   0x00000024   Code   RO         1724    .text               mc_w.l(memcpya.o)
    0x08000246   0x08000246   0x00000024   Code   RO         1726    .text               mc_w.l(memseta.o)
    0x0800026a   0x0800026a   0x00000024   Code   RO         1728    .text               mc_w.l(strstr.o)
    0x0800028e   0x0800028e   0x00000018   Code   RO         1730    .text               mc_w.l(strncpy.o)
    0x080002a6   0x080002a6   0x00000014   Code   RO         1732    .text               mc_w.l(strchr.o)
    0x080002ba   0x080002ba   0x0000000e   Code   RO         1734    .text               mc_w.l(strlen.o)
    0x080002c8   0x080002c8   0x0000001c   Code   RO         1736    .text               mc_w.l(strcmp.o)
    0x080002e4   0x080002e4   0x0000001a   Code   RO         1738    .text               mc_w.l(memcmp.o)
    0x080002fe   0x080002fe   0x0000001e   Code   RO         1740    .text               mc_w.l(strncmp.o)
    0x0800031c   0x0800031c   0x0000014e   Code   RO         2005    .text               mf_w.l(dadd.o)
    0x0800046a   0x0800046a   0x000000e4   Code   RO         2007    .text               mf_w.l(dmul.o)
    0x0800054e   0x0800054e   0x000000de   Code   RO         2009    .text               mf_w.l(ddiv.o)
    0x0800062c   0x0800062c   0x0000003e   Code   RO         2011    .text               mf_w.l(dfixi.o)
    0x0800066a   0x0800066a   0x00000026   Code   RO         2013    .text               mf_w.l(f2d.o)
    0x08000690   0x08000690   0x00000038   Code   RO         2015    .text               mf_w.l(d2f.o)
    0x080006c8   0x080006c8   0x0000002c   Code   RO         2084    .text               mc_w.l(uidiv.o)
    0x080006f4   0x080006f4   0x0000001e   Code   RO         2086    .text               mc_w.l(llshl.o)
    0x08000712   0x08000712   0x00000020   Code   RO         2088    .text               mc_w.l(llushr.o)
    0x08000732   0x08000732   0x00000024   Code   RO         2090    .text               mc_w.l(llsshr.o)
    0x08000756   0x08000756   0x00000002   PAD
    0x08000758   0x08000758   0x000000a8   Code   RO         2099    .text               mc_w.l(strtod.o)
    0x08000800   0x08000800   0x00000000   Code   RO         2101    .text               mc_w.l(iusefp.o)
    0x08000800   0x08000800   0x0000006e   Code   RO         2102    .text               mf_w.l(fepilogue.o)
    0x0800086e   0x0800086e   0x000000ba   Code   RO         2104    .text               mf_w.l(depilogue.o)
    0x08000928   0x08000928   0x000000a2   Code   RO         2108    .text               mf_w.l(dsqrt.o)
    0x080009ca   0x080009ca   0x00000086   Code   RO         2110    .text               mf_w.l(drem.o)
    0x08000a50   0x08000a50   0x00000030   Code   RO         2112    .text               mf_w.l(dfixul.o)
    0x08000a80   0x08000a80   0x00000030   Code   RO         2114    .text               mf_w.l(cdcmple.o)
    0x08000ab0   0x08000ab0   0x00000030   Code   RO         2116    .text               mf_w.l(cdrcmple.o)
    0x08000ae0   0x08000ae0   0x00000030   Code   RO         2122    .text               mc_w.l(init.o)
    0x08000b10   0x08000b10   0x0000000a   Code   RO         2125    .text               mc_w.l(isspace_c.o)
    0x08000b1a   0x08000b1a   0x00000002   PAD
    0x08000b1c   0x08000b1c   0x00000360   Code   RO         2127    .text               mc_w.l(scanf_fp.o)
    0x08000e7c   0x08000e7c   0x00000040   Code   RO         2131    .text               mc_w.l(_sgetc.o)
    0x08000ebc   0x08000ebc   0x00000022   Code   RO         2134    .text               mf_w.l(dflti.o)
    0x08000ede   0x08000ede   0x0000001a   Code   RO         2136    .text               mf_w.l(dfltui.o)
    0x08000ef8   0x08000ef8   0x00000028   Code   RO         2138    .text               mc_w.l(ctype_c.o)
    0x08000f20   0x08000f20   0x00000018   Code   RO         2141    .text               mf_w.l(dfltul.o)
    0x08000f38   0x08000f38   0x00000002   Code   RO          124    .text.BusFault_Handler  stm32f4xx_it.o
    0x08000f3a   0x08000f3a   0x00000002   PAD
    0x08000f3c   0x08000f3c   0x0000000c   Code   RO          136    .text.DMA1_Stream1_IRQHandler  stm32f4xx_it.o
    0x08000f48   0x08000f48   0x0000000c   Code   RO          138    .text.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x08000f54   0x08000f54   0x0000000c   Code   RO          148    .text.DMA2_Stream1_IRQHandler  stm32f4xx_it.o
    0x08000f60   0x08000f60   0x0000000c   Code   RO          150    .text.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x08000f6c   0x08000f6c   0x00000002   Code   RO          130    .text.DebugMon_Handler  stm32f4xx_it.o
    0x08000f6e   0x08000f6e   0x00000002   PAD
    0x08000f70   0x08000f70   0x00000118   Code   RO         1318    .text.Draw_Circle   lcd_display_hal.o
    0x08001088   0x08001088   0x00000006   Code   RO           15    .text.Error_Handler  main.o
    0x0800108e   0x0800108e   0x00000002   PAD
    0x08001090   0x08001090   0x0000005c   Code   RO         1356    .text.GPS_AutoUpload_Task  scheduler.o
    0x080010ec   0x080010ec   0x00000058   Code   RO         1479    .text.GPS_Task      gps_app.o
    0x08001144   0x08001144   0x00000002   Code   RO         1499    .text.GPS_Virtual_Init  gps_app.o
    0x08001146   0x08001146   0x00000002   PAD
    0x08001148   0x08001148   0x0000008e   Code   RO          464    .text.HAL_DMA_Abort  stm32f4xx_hal_dma.o
    0x080011d6   0x080011d6   0x00000002   PAD
    0x080011d8   0x080011d8   0x00000024   Code   RO          466    .text.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x080011fc   0x080011fc   0x000001c4   Code   RO          470    .text.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x080013c0   0x080013c0   0x00000162   Code   RO          456    .text.HAL_DMA_Init  stm32f4xx_hal_dma.o
    0x08001522   0x08001522   0x00000002   PAD
    0x08001524   0x08001524   0x000000a2   Code   RO          462    .text.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x080015c6   0x080015c6   0x00000002   PAD
    0x080015c8   0x080015c8   0x00000028   Code   RO          623    .text.HAL_Delay     stm32f4xx_hal.o
    0x080015f0   0x080015f0   0x0000019e   Code   RO          418    .text.HAL_GPIO_Init  stm32f4xx_hal_gpio.o
    0x0800178e   0x0800178e   0x00000002   PAD
    0x08001790   0x08001790   0x0000000a   Code   RO          422    .text.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x0800179a   0x0800179a   0x00000002   PAD
    0x0800179c   0x0800179c   0x00000010   Code   RO          426    .text.HAL_GPIO_TogglePin  stm32f4xx_hal_gpio.o
    0x080017ac   0x080017ac   0x0000000a   Code   RO          424    .text.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x080017b6   0x080017b6   0x00000002   PAD
    0x080017b8   0x080017b8   0x0000000c   Code   RO          615    .text.HAL_GetTick   stm32f4xx_hal.o
    0x080017c4   0x080017c4   0x00000164   Code   RO          169    .text.HAL_I2C_Init  stm32f4xx_hal_i2c.o
    0x08001928   0x08001928   0x00000086   Code   RO           45    .text.HAL_I2C_MspInit  i2c.o
    0x080019ae   0x080019ae   0x00000002   PAD
    0x080019b0   0x080019b0   0x0000001a   Code   RO          613    .text.HAL_IncTick   stm32f4xx_hal.o
    0x080019ca   0x080019ca   0x00000002   PAD
    0x080019cc   0x080019cc   0x00000036   Code   RO          603    .text.HAL_Init      stm32f4xx_hal.o
    0x08001a02   0x08001a02   0x00000002   PAD
    0x08001a04   0x08001a04   0x00000048   Code   RO          605    .text.HAL_InitTick  stm32f4xx_hal.o
    0x08001a4c   0x08001a4c   0x00000038   Code   RO          161    .text.HAL_MspInit   stm32f4xx_hal_msp.o
    0x08001a84   0x08001a84   0x00000022   Code   RO          555    .text.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08001aa6   0x08001aa6   0x00000002   PAD
    0x08001aa8   0x08001aa8   0x00000056   Code   RO          553    .text.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08001afe   0x08001afe   0x00000002   PAD
    0x08001b00   0x08001b00   0x00000020   Code   RO          551    .text.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08001b20   0x08001b20   0x00000164   Code   RO          312    .text.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08001c84   0x08001c84   0x00000026   Code   RO          324    .text.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08001caa   0x08001caa   0x00000002   PAD
    0x08001cac   0x08001cac   0x00000026   Code   RO          326    .text.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08001cd2   0x08001cd2   0x00000002   PAD
    0x08001cd4   0x08001cd4   0x0000006c   Code   RO          314    .text.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08001d40   0x08001d40   0x000003ac   Code   RO          310    .text.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x080020ec   0x080020ec   0x0000002c   Code   RO          563    .text.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08002118   0x08002118   0x0000004c   Code   RO          990    .text.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x08002164   0x08002164   0x000000ba   Code   RO          988    .text.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x0800221e   0x0800221e   0x00000002   PAD
    0x08002220   0x08002220   0x0000005a   Code   RO          693    .text.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x0800227a   0x0800227a   0x00000002   PAD
    0x0800227c   0x0800227c   0x00000032   Code   RO           66    .text.HAL_TIM_Base_MspInit  tim.o
    0x080022ae   0x080022ae   0x00000002   PAD
    0x080022b0   0x080022b0   0x000001a0   Code   RO          873    .text.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08002450   0x08002450   0x000000b2   Code   RO          807    .text.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x08002502   0x08002502   0x00000002   PAD
    0x08002504   0x08002504   0x000000c0   Code   RO           68    .text.HAL_TIM_Encoder_MspInit  tim.o
    0x080025c4   0x080025c4   0x00000220   Code   RO          847    .text.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x080027e4   0x080027e4   0x0000005a   Code   RO          747    .text.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x0800283e   0x0800283e   0x00000002   PAD
    0x08002840   0x08002840   0x00000002   Code   RO          749    .text.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x08002842   0x08002842   0x00000002   PAD
    0x08002844   0x08002844   0x000001ee   Code   RO         1060    .text.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x08002a32   0x08002a32   0x00000002   PAD
    0x08002a34   0x08002a34   0x000000c0   Code   RO         1221    .text.HAL_UARTEx_RxEventCallback  uart_driver.o
    0x08002af4   0x08002af4   0x00000074   Code   RO         1237    .text.HAL_UARTEx_RxEventCallback_UART2  uart2_driver.o
    0x08002b68   0x08002b68   0x00000060   Code   RO         1253    .text.HAL_UARTEx_RxEventCallback_UART3  uart3_driver.o
    0x08002bc8   0x08002bc8   0x00000064   Code   RO         1269    .text.HAL_UARTEx_RxEventCallback_UART6  uart6_driver.o
    0x08002c2c   0x08002c2c   0x0000021c   Code   RO         1054    .text.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x08002e48   0x08002e48   0x00000002   Code   RO         1096    .text.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08002e4a   0x08002e4a   0x00000002   PAD
    0x08002e4c   0x08002e4c   0x00000594   Code   RO         1090    .text.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x080033e0   0x080033e0   0x00000060   Code   RO         1012    .text.HAL_UART_Init  stm32f4xx_hal_uart.o
    0x08003440   0x08003440   0x00000348   Code   RO           95    .text.HAL_UART_MspInit  usart.o
    0x08003788   0x08003788   0x00000002   Code   RO         1104    .text.HAL_UART_RxCpltCallback  stm32f4xx_hal_uart.o
    0x0800378a   0x0800378a   0x00000002   PAD
    0x0800378c   0x0800378c   0x00000002   Code   RO         1106    .text.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x0800378e   0x0800378e   0x00000002   PAD
    0x08003790   0x08003790   0x00000192   Code   RO         1028    .text.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08003922   0x08003922   0x00000002   PAD
    0x08003924   0x08003924   0x00000002   Code   RO         1100    .text.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08003926   0x08003926   0x00000002   PAD
    0x08003928   0x08003928   0x00000002   Code   RO          120    .text.HardFault_Handler  stm32f4xx_it.o
    0x0800392a   0x0800392a   0x00000002   PAD
    0x0800392c   0x0800392c   0x000000ba   Code   RO         1293    .text.LCD_Address_Set  lcd_init_hal.o
    0x080039e6   0x080039e6   0x00000002   PAD
    0x080039e8   0x080039e8   0x0000009e   Code   RO         1314    .text.LCD_DrawLine  lcd_display_hal.o
    0x08003a86   0x08003a86   0x00000002   PAD
    0x08003a88   0x08003a88   0x00000114   Code   RO         1316    .text.LCD_DrawRectangle  lcd_display_hal.o
    0x08003b9c   0x08003b9c   0x00000048   Code   RO         1310    .text.LCD_Fill      lcd_display_hal.o
    0x08003be4   0x08003be4   0x000006fc   Code   RO         1295    .text.LCD_Init      lcd_init_hal.o
    0x080042e0   0x080042e0   0x000001bc   Code   RO         1634    .text.LCD_Map_CalculateBearing  lcd_map_display.o
    0x0800449c   0x0800449c   0x00000198   Code   RO         1632    .text.LCD_Map_CalculateRouteDistance  lcd_map_display.o
    0x08004634   0x08004634   0x000002e4   Code   RO         1646    .text.LCD_Map_DisplayNavigationInstructions  lcd_map_display.o
    0x08004918   0x08004918   0x00000184   Code   RO         1650    .text.LCD_Map_DrawDirectionArrow  lcd_map_display.o
    0x08004a9c   0x08004a9c   0x000000e8   Code   RO         1648    .text.LCD_Map_DrawNavigationStep  lcd_map_display.o
    0x08004b84   0x08004b84   0x00000086   Code   RO         1622    .text.LCD_Map_Init  lcd_map_display.o
    0x08004c0a   0x08004c0a   0x00000002   PAD
    0x08004c0c   0x08004c0c   0x00000054   Code   RO         1630    .text.LCD_Map_SetDestination  lcd_map_display.o
    0x08004c60   0x08004c60   0x0000006c   Code   RO         1652    .text.LCD_Map_Task  lcd_map_display.o
    0x08004ccc   0x08004ccc   0x0000002a   Code   RO         1628    .text.LCD_Map_UpdatePosition  lcd_map_display.o
    0x08004cf6   0x08004cf6   0x00000002   PAD
    0x08004cf8   0x08004cf8   0x0000033c   Code   RO         1595    .text.LCD_QuickPinTest  tft_app.o
    0x08005034   0x08005034   0x0000036c   Code   RO         1324    .text.LCD_ShowChar  lcd_display_hal.o
    0x080053a0   0x080053a0   0x0000011a   Code   RO         1322    .text.LCD_ShowIntNum  lcd_display_hal.o
    0x080054ba   0x080054ba   0x00000002   PAD
    0x080054bc   0x080054bc   0x00000046   Code   RO         1328    .text.LCD_ShowString  lcd_display_hal.o
    0x08005502   0x08005502   0x00000002   PAD
    0x08005504   0x08005504   0x0000029c   Code   RO         1597    .text.LCD_SimpleSPITest  tft_app.o
    0x080057a0   0x080057a0   0x00000014   Code   RO         1289    .text.LCD_WR_DATA   lcd_init_hal.o
    0x080057b4   0x080057b4   0x00000004   Code   RO         1287    .text.LCD_WR_DATA8  lcd_init_hal.o
    0x080057b8   0x080057b8   0x0000002a   Code   RO         1291    .text.LCD_WR_REG    lcd_init_hal.o
    0x080057e2   0x080057e2   0x00000002   PAD
    0x080057e4   0x080057e4   0x0000013e   Code   RO         1285    .text.LCD_Writ_Bus  lcd_init_hal.o
    0x08005922   0x08005922   0x00000002   PAD
    0x08005924   0x08005924   0x0000007c   Code   RO           34    .text.MX_DMA_Init   dma.o
    0x080059a0   0x080059a0   0x00000190   Code   RO           25    .text.MX_GPIO_Init  gpio.o
    0x08005b30   0x08005b30   0x00000042   Code   RO           43    .text.MX_I2C2_Init  i2c.o
    0x08005b72   0x08005b72   0x00000002   PAD
    0x08005b74   0x08005b74   0x00000166   Code   RO           58    .text.MX_TIM1_Init  tim.o
    0x08005cda   0x08005cda   0x00000002   PAD
    0x08005cdc   0x08005cdc   0x00000068   Code   RO           62    .text.MX_TIM3_Init  tim.o
    0x08005d44   0x08005d44   0x00000068   Code   RO           64    .text.MX_TIM4_Init  tim.o
    0x08005dac   0x08005dac   0x0000003c   Code   RO           85    .text.MX_UART4_Init  usart.o
    0x08005de8   0x08005de8   0x0000003c   Code   RO           87    .text.MX_USART1_UART_Init  usart.o
    0x08005e24   0x08005e24   0x0000003c   Code   RO           89    .text.MX_USART2_UART_Init  usart.o
    0x08005e60   0x08005e60   0x0000003c   Code   RO           91    .text.MX_USART3_UART_Init  usart.o
    0x08005e9c   0x08005e9c   0x0000003c   Code   RO           93    .text.MX_USART6_UART_Init  usart.o
    0x08005ed8   0x08005ed8   0x00000002   Code   RO          122    .text.MemManage_Handler  stm32f4xx_it.o
    0x08005eda   0x08005eda   0x00000002   PAD
    0x08005edc   0x08005edc   0x00000002   Code   RO          118    .text.NMI_Handler   stm32f4xx_it.o
    0x08005ede   0x08005ede   0x00000002   PAD
    0x08005ee0   0x08005ee0   0x000001ac   Code   RO         1545    .text.Navigation_CalculateBearing  navigation_app.o
    0x0800608c   0x0800608c   0x00000184   Code   RO         1543    .text.Navigation_CalculateDistance  navigation_app.o
    0x08006210   0x08006210   0x0000013a   Code   RO         1533    .text.Navigation_FindDestination  navigation_app.o
    0x0800634a   0x0800634a   0x00000002   PAD
    0x0800634c   0x0800634c   0x00000238   Code   RO         1535    .text.Navigation_PlanRoute  navigation_app.o
    0x08006584   0x08006584   0x00000590   Code   RO         1537    .text.Navigation_ProcessCommand  navigation_app.o
    0x08006b14   0x08006b14   0x00000320   Code   RO         1529    .text.Navigation_StartNavigation  navigation_app.o
    0x08006e34   0x08006e34   0x00000050   Code   RO         1527    .text.Navigation_Task  navigation_app.o
    0x08006e84   0x08006e84   0x00000002   Code   RO          132    .text.PendSV_Handler  stm32f4xx_it.o
    0x08006e86   0x08006e86   0x00000002   PAD
    0x08006e88   0x08006e88   0x00000002   Code   RO          128    .text.SVC_Handler   stm32f4xx_it.o
    0x08006e8a   0x08006e8a   0x00000002   PAD
    0x08006e8c   0x08006e8c   0x00000028   Code   RO         1360    .text.Scheduler_Init  scheduler.o
    0x08006eb4   0x08006eb4   0x0000004a   Code   RO         1362    .text.Scheduler_Run  scheduler.o
    0x08006efe   0x08006efe   0x00000002   PAD
    0x08006f00   0x08006f00   0x00000004   Code   RO          134    .text.SysTick_Handler  stm32f4xx_it.o
    0x08006f04   0x08006f04   0x000000a8   Code   RO           13    .text.SystemClock_Config  main.o
    0x08006fac   0x08006fac   0x00000012   Code   RO         1134    .text.SystemInit    system_stm32f4xx.o
    0x08006fbe   0x08006fbe   0x00000002   PAD
    0x08006fc0   0x08006fc0   0x0000013a   Code   RO          697    .text.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x080070fa   0x080070fa   0x00000002   PAD
    0x080070fc   0x080070fc   0x0000000c   Code   RO          146    .text.UART4_IRQHandler  stm32f4xx_it.o
    0x08007108   0x08007108   0x0000000a   Code   RO         1094    .text.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08007112   0x08007112   0x00000002   PAD
    0x08007114   0x08007114   0x0000017c   Code   RO         1044    .text.UART_DMAError  stm32f4xx_hal_uart.o
    0x08007290   0x08007290   0x0000015e   Code   RO         1122    .text.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x080073ee   0x080073ee   0x00000002   PAD
    0x080073f0   0x080073f0   0x00000018   Code   RO         1124    .text.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x08007408   0x08007408   0x000000fe   Code   RO         1092    .text.UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08007506   0x08007506   0x00000002   PAD
    0x08007508   0x08007508   0x000000e6   Code   RO         1016    .text.UART_SetConfig  stm32f4xx_hal_uart.o
    0x080075ee   0x080075ee   0x00000002   PAD
    0x080075f0   0x080075f0   0x0000000c   Code   RO          140    .text.USART1_IRQHandler  stm32f4xx_it.o
    0x080075fc   0x080075fc   0x0000000c   Code   RO          142    .text.USART2_IRQHandler  stm32f4xx_it.o
    0x08007608   0x08007608   0x0000000c   Code   RO          144    .text.USART3_IRQHandler  stm32f4xx_it.o
    0x08007614   0x08007614   0x0000000c   Code   RO          152    .text.USART6_IRQHandler  stm32f4xx_it.o
    0x08007620   0x08007620   0x00000046   Code   RO         1389    .text.Uart2_Init    uart2_app.o
    0x08007666   0x08007666   0x00000002   PAD
    0x08007668   0x08007668   0x0000006c   Code   RO         1235    .text.Uart2_Printf  uart2_driver.o
    0x080076d4   0x080076d4   0x00000620   Code   RO         1391    .text.Uart2_Task    uart2_app.o
    0x08007cf4   0x08007cf4   0x0000000e   Code   RO         1405    .text.Uart3_Task    uart3_app.o
    0x08007d02   0x08007d02   0x00000002   PAD
    0x08007d04   0x08007d04   0x00000080   Code   RO         1560    .text.Uart6_Init    uart6_app.o
    0x08007d84   0x08007d84   0x00000050   Code   RO         1562    .text.Uart6_Task    uart6_app.o
    0x08007dd4   0x08007dd4   0x00000098   Code   RO         1375    .text.Uart_Init     uart_app.o
    0x08007e6c   0x08007e6c   0x00000690   Code   RO         1377    .text.Uart_Task     uart_app.o
    0x080084fc   0x080084fc   0x00000002   Code   RO          126    .text.UsageFault_Handler  stm32f4xx_it.o
    0x080084fe   0x080084fe   0x00000002   PAD
    0x08008500   0x08008500   0x00000164   Code   RO         1438    .text.esp01_CheckConnection  esp01_app.o
    0x08008664   0x08008664   0x0000002a   Code   RO         1424    .text.esp01_GetRealLocation  esp01_app.o
    0x0800868e   0x0800868e   0x00000002   PAD
    0x08008690   0x08008690   0x0000000c   Code   RO         1420    .text.esp01_GetState  esp01_app.o
    0x0800869c   0x0800869c   0x00000154   Code   RO         1414    .text.esp01_Init    esp01_app.o
    0x080087f0   0x080087f0   0x000000f8   Code   RO         1436    .text.esp01_ReconnectWiFi  esp01_app.o
    0x080088e8   0x080088e8   0x00000048   Code   RO         1434    .text.esp01_Reset   esp01_app.o
    0x08008930   0x08008930   0x0000000e   Code   RO         1450    .text.esp01_ResetTCPState  esp01_app.o
    0x0800893e   0x0800893e   0x00000002   PAD
    0x08008940   0x08008940   0x00000010   Code   RO         1446    .text.esp01_SetConnected  esp01_app.o
    0x08008950   0x08008950   0x00000028   Code   RO         1460    .text.esp01_SetDataSendReady  esp01_app.o
    0x08008978   0x08008978   0x0000000e   Code   RO         1448    .text.esp01_SetTCPConnected  esp01_app.o
    0x08008986   0x08008986   0x00000002   PAD
    0x08008988   0x08008988   0x0000003c   Code   RO         1418    .text.esp01_StartInit  esp01_app.o
    0x080089c4   0x080089c4   0x00000108   Code   RO         1432    .text.esp01_Task    esp01_app.o
    0x08008acc   0x08008acc   0x00000140   Code   RO         1426    .text.esp01_UploadGPSData  esp01_app.o
    0x08008c0c   0x08008c0c   0x0000018c   Code   RO         1428    .text.esp01_UploadNavigationCommand  esp01_app.o
    0x08008d98   0x08008d98   0x0000003e   Code   RO           11    .text.main          main.o
    0x08008dd6   0x08008dd6   0x00000002   PAD
    0x08008dd8   0x08008dd8   0x0000003e   Code   RO           99    .text.my_printf     usart.o
    0x08008e16   0x08008e16   0x00000002   PAD
    0x08008e18   0x08008e18   0x00000200   Code   RO         1481    .text.parseGpsBuffer  gps_app.o
    0x08009018   0x08009018   0x00000044   Code   RO         1195    .text.rt_ringbuffer_data_len  ringbuffer.o
    0x0800905c   0x0800905c   0x000000b8   Code   RO         1199    .text.rt_ringbuffer_get  ringbuffer.o
    0x08009114   0x08009114   0x0000000e   Code   RO         1191    .text.rt_ringbuffer_init  ringbuffer.o
    0x08009122   0x08009122   0x00000002   PAD
    0x08009124   0x08009124   0x000000b2   Code   RO         1193    .text.rt_ringbuffer_put  ringbuffer.o
    0x080091d6   0x080091d6   0x00000002   PAD
    0x080091d8   0x080091d8   0x00000006   Code   RO         1209    .text.rt_ringbuffer_reset  ringbuffer.o
    0x080091de   0x080091de   0x00000002   PAD
    0x080091e0   0x080091e0   0x000001e8   Code   RO         1575    .text.tft_BasicTest  tft_app.o
    0x080093c8   0x080093c8   0x000004ec   Code   RO         1577    .text.tft_HardwareDiagnose  tft_app.o
    0x080098b4   0x080098b4   0x00000004   PAD
    0x080098b8   0x080098b8   0x000002dc   Code   RO         1593    .text.tft_Init      tft_app.o
    0x08009b94   0x08009b94   0x00000088   Code   RO         1605    .text.tft_SetMapDestination  tft_app.o
    0x08009c1c   0x08009c1c   0x000000bc   Code   RO         1607    .text.tft_Task      tft_app.o
    0x08009cd8   0x08009cd8   0x0000006a   Code   RO         1603    .text.tft_UpdateMapPosition  tft_app.o
    0x08009d42   0x08009d42   0x00000002   PAD
    0x08009d44   0x08009d44   0x00000034   Code   RO         1978    i.__0snprintf       mc_w.l(printfa.o)
    0x08009d78   0x08009d78   0x00000034   Code   RO         1982    i.__0vsnprintf      mc_w.l(printfa.o)
    0x08009dac   0x08009dac   0x00000030   Code   RO         2118    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08009ddc   0x08009ddc   0x00000004   PAD
    0x08009de0   0x08009de0   0x000002d8   Code   RO         2017    i.__hardfp_atan     m_wm.l(atan.o)
    0x0800a0b8   0x0800a0b8   0x00000200   Code   RO         1683    i.__hardfp_atan2    m_wm.l(atan2.o)
    0x0800a2b8   0x0800a2b8   0x00000038   Code   RO         1689    i.__hardfp_atof     m_wm.l(atof.o)
    0x0800a2f0   0x0800a2f0   0x000000c8   Code   RO         1695    i.__hardfp_cos      m_wm.l(cos.o)
    0x0800a3b8   0x0800a3b8   0x00000104   Code   RO         1701    i.__hardfp_fmod     m_wm.l(fmod.o)
    0x0800a4bc   0x0800a4bc   0x00000004   PAD
    0x0800a4c0   0x0800a4c0   0x000000c8   Code   RO         1707    i.__hardfp_sin      m_wm.l(sin.o)
    0x0800a588   0x0800a588   0x0000007a   Code   RO         1713    i.__hardfp_sqrt     m_wm.l(sqrt.o)
    0x0800a602   0x0800a602   0x00000006   PAD
    0x0800a608   0x0800a608   0x00000438   Code   RO         2048    i.__ieee754_rem_pio2  m_wm.l(rred.o)
    0x0800aa40   0x0800aa40   0x00000170   Code   RO         2024    i.__kernel_cos      m_wm.l(cos_i.o)
    0x0800abb0   0x0800abb0   0x000000f8   Code   RO         2120    i.__kernel_poly     m_wm.l(poly.o)
    0x0800aca8   0x0800aca8   0x00000130   Code   RO         2053    i.__kernel_sin      m_wm.l(sin_i.o)
    0x0800add8   0x0800add8   0x00000014   Code   RO         2028    i.__mathlib_dbl_infnan  m_wm.l(dunder.o)
    0x0800adec   0x0800adec   0x00000014   Code   RO         2029    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x0800ae00   0x0800ae00   0x00000020   Code   RO         2030    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x0800ae20   0x0800ae20   0x00000020   Code   RO         2033    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x0800ae40   0x0800ae40   0x0000000c   Code   RO         2093    i.__read_errno      mc_w.l(errno.o)
    0x0800ae4c   0x0800ae4c   0x0000000e   Code   RO         2145    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800ae5a   0x0800ae5a   0x00000002   Code   RO         2146    i.__scatterload_null  mc_w.l(handlers.o)
    0x0800ae5c   0x0800ae5c   0x0000000e   Code   RO         2147    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800ae6a   0x0800ae6a   0x00000002   PAD
    0x0800ae6c   0x0800ae6c   0x0000000c   Code   RO         2094    i.__set_errno       mc_w.l(errno.o)
    0x0800ae78   0x0800ae78   0x00000184   Code   RO         1984    i._fp_digits        mc_w.l(printfa.o)
    0x0800affc   0x0800affc   0x0000000e   Code   RO         2129    i._is_digit         mc_w.l(scanf_fp.o)
    0x0800b00a   0x0800b00a   0x00000002   PAD
    0x0800b00c   0x0800b00c   0x000006dc   Code   RO         1985    i._printf_core      mc_w.l(printfa.o)
    0x0800b6e8   0x0800b6e8   0x00000024   Code   RO         1986    i._printf_post_padding  mc_w.l(printfa.o)
    0x0800b70c   0x0800b70c   0x0000002e   Code   RO         1987    i._printf_pre_padding  mc_w.l(printfa.o)
    0x0800b73a   0x0800b73a   0x00000016   Code   RO         1988    i._snputc           mc_w.l(printfa.o)
    0x0800b750   0x0800b750   0x00000010   Code   RO         2019    i.atan              m_wm.l(atan.o)
    0x0800b760   0x0800b760   0x00000018   Code   RO         2043    i.fabs              m_wm.l(fabs.o)
    0x0800b778   0x0800b778   0x00000098   Data   RO         2020    .constdata          m_wm.l(atan.o)
    0x0800b810   0x0800b810   0x00000030   Data   RO         2025    .constdata          m_wm.l(cos_i.o)
    0x0800b840   0x0800b840   0x00000008   Data   RO         2047    .constdata          m_wm.l(qnan.o)
    0x0800b848   0x0800b848   0x000000cc   Data   RO         2050    .constdata          m_wm.l(rred.o)
    0x0800b914   0x0800b914   0x00000004   PAD
    0x0800b918   0x0800b918   0x00000028   Data   RO         2054    .constdata          m_wm.l(sin_i.o)
    0x0800b940   0x0800b940   0x00000040   Data   RO         2139    .constdata          mc_w.l(ctype_c.o)
    0x0800b980   0x0800b980   0x00000046   Data   RO         1346    .rodata..L__const.LCD_GetDigitPixel.digit_patterns  lcd_display_hal.o
    0x0800b9c6   0x0800b9c6   0x000000b6   Data   RO         1347    .rodata..L__const.LCD_GetLetterPixel.letter_patterns  lcd_display_hal.o
    0x0800ba7c   0x0800ba7c   0x00000010   Data   RO         1139    .rodata.AHBPrescTable  system_stm32f4xx.o
    0x0800ba8c   0x0800ba8c   0x00000008   Data   RO         1140    .rodata.APBPrescTable  system_stm32f4xx.o
    0x0800ba94   0x0800ba94   0x00000008   Data   RO          480    .rodata.cst8        stm32f4xx_hal_dma.o
    0x0800ba9c   0x0800ba9c   0x00000820   Data   RO         1550    .rodata.destinations  navigation_app.o
    0x0800c2bc   0x0800c2bc   0x000001b3   Data   RO         1297    .rodata.str1.1      lcd_init_hal.o
    0x0800c46f   0x0800c46f   0x00000375   Data   RO         1379    .rodata.str1.1      uart_app.o
    0x0800c7e4   0x0800c7e4   0x00000545   Data   RO         1394    .rodata.str1.1      uart2_app.o
    0x0800cd29   0x0800cd29   0x000002d1   Data   RO         1464    .rodata.str1.1      esp01_app.o
    0x0800cffa   0x0800cffa   0x00000040   Data   RO         1510    .rodata.str1.1      gps_app.o
    0x0800d03a   0x0800d03a   0x00000341   Data   RO         1549    .rodata.str1.1      navigation_app.o
    0x0800d37b   0x0800d37b   0x000008e8   Data   RO         1611    .rodata.str1.1      tft_app.o
    0x0800dc63   0x0800dc63   0x0000006b   Data   RO         1655    .rodata.str1.1      lcd_map_display.o
    0x0800dcce   0x0800dcce   0x00000002   PAD
    0x0800dcd0   0x0800dcd0   0x00000020   Data   RO         2144    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800dcf0, Size: 0x00003f60, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800dcf0   0x00000004   Data   RW         2095    .data               mc_w.l(errno.o)
    0x20000004   0x0800dcf4   0x00000008   Data   RW          658    .data..L_MergedGlobals  stm32f4xx_hal.o
    0x2000000c   0x0800dcfc   0x00000008   Data   RW         1465    .data..L_MergedGlobals  esp01_app.o
    0x20000014   0x0800dd04   0x00000008   Data   RW         1612    .data..L_MergedGlobals  tft_app.o
    0x2000001c   0x0800dd0c   0x00000004   Data   RW         1138    .data.SystemCoreClock  system_stm32f4xx.o
    0x20000020   0x0800dd10   0x00000060   Data   RW         1366    .data.scheduler_task  scheduler.o
    0x20000080        -       0x00000008   Zero   RW         1380    .bss..L_MergedGlobals  uart_app.o
    0x20000088        -       0x00000024   Zero   RW         1516    .bss..L_MergedGlobals  gps_app.o
    0x200000ac        -       0x00000008   Zero   RW         1613    .bss..L_MergedGlobals.121  tft_app.o
    0x200000b4        -       0x00000010   Zero   RW         1466    .bss..L_MergedGlobals.64  esp01_app.o
    0x200000c4        -       0x00000004   Zero   RW         1364    .bss.GPS_AutoUpload_Task.upload_count  scheduler.o
    0x200000c8        -       0x00000004   Zero   RW         1656    .bss.LCD_Map_Task.last_update  lcd_map_display.o
    0x200000cc        -       0x000000f1   Zero   RW         1511    .bss.Save_Data      gps_app.o
    0x200001bd   0x0800dd70   0x00000003   PAD
    0x200001c0        -       0x00000004   Zero   RW         1393    .bss.Uart2_Task.last_receive_time  uart2_app.o
    0x200001c4        -       0x00000004   Zero   RW         1566    .bss.Uart6_Task.last_heartbeat  uart6_app.o
    0x200001c8        -       0x00001c98   Zero   RW         1548    .bss.current_navigation  navigation_app.o
    0x20001e60        -       0x00000030   Zero   RW         1654    .bss.g_map_state    lcd_map_display.o
    0x20001e90        -       0x00000060   Zero   RW          106    .bss.hdma_usart1_rx  usart.o
    0x20001ef0        -       0x00000060   Zero   RW          107    .bss.hdma_usart2_rx  usart.o
    0x20001f50        -       0x00000060   Zero   RW          108    .bss.hdma_usart3_rx  usart.o
    0x20001fb0        -       0x00000060   Zero   RW          109    .bss.hdma_usart6_rx  usart.o
    0x20002010        -       0x00000054   Zero   RW           49    .bss.hi2c2          i2c.o
    0x20002064        -       0x00000048   Zero   RW           74    .bss.htim1          tim.o
    0x200020ac        -       0x00000048   Zero   RW           75    .bss.htim3          tim.o
    0x200020f4        -       0x00000048   Zero   RW           76    .bss.htim4          tim.o
    0x2000213c        -       0x00000048   Zero   RW          102    .bss.huart1         usart.o
    0x20002184        -       0x00000048   Zero   RW          103    .bss.huart2         usart.o
    0x200021cc        -       0x00000048   Zero   RW          104    .bss.huart3         usart.o
    0x20002214        -       0x00000048   Zero   RW          101    .bss.huart4         usart.o
    0x2000225c        -       0x00000048   Zero   RW          105    .bss.huart6         usart.o
    0x200022a4        -       0x00000001   Zero   RW         1547    .bss.nav_state      navigation_app.o
    0x200022a5   0x0800dd70   0x00000003   PAD
    0x200022a8        -       0x0000000c   Zero   RW         1223    .bss.ring_buffer    uart_driver.o
    0x200022b4        -       0x00000080   Zero   RW         1225    .bss.ring_buffer_input  uart_driver.o
    0x20002334        -       0x00000001   Zero   RW         1365    .bss.task_num       scheduler.o
    0x20002335        -       0x00000200   Zero   RW         1242    .bss.uart2_data_buffer  uart2_driver.o
    0x20002535   0x0800dd70   0x00000003   PAD
    0x20002538        -       0x0000000c   Zero   RW         1239    .bss.uart2_ring_buffer  uart2_driver.o
    0x20002544        -       0x00000200   Zero   RW         1241    .bss.uart2_ring_buffer_input  uart2_driver.o
    0x20002744        -       0x00000200   Zero   RW         1240    .bss.uart2_rx_dma_buffer  uart2_driver.o
    0x20002944        -       0x00000080   Zero   RW         1258    .bss.uart3_data_buffer  uart3_driver.o
    0x200029c4        -       0x0000000c   Zero   RW         1255    .bss.uart3_ring_buffer  uart3_driver.o
    0x200029d0        -       0x00000080   Zero   RW         1256    .bss.uart3_rx_dma_buffer  uart3_driver.o
    0x20002a50        -       0x0000000c   Zero   RW         1271    .bss.uart6_ring_buffer  uart6_driver.o
    0x20002a5c        -       0x00000200   Zero   RW         1273    .bss.uart6_ring_buffer_input  uart6_driver.o
    0x20002c5c        -       0x00000200   Zero   RW         1272    .bss.uart6_rx_dma_buffer  uart6_driver.o
    0x20002e5c        -       0x00000080   Zero   RW         1226    .bss.uart_data_buffer  uart_driver.o
    0x20002edc        -       0x00000080   Zero   RW         1224    .bss.uart_rx_dma_buffer  uart_driver.o
    0x20002f5c        -       0x00000004   Zero   RW          657    .bss.uwTick         stm32f4xx_hal.o
    0x20002f60        -       0x00001000   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x0800dd70, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       124          0          0          0          0       3546   dma.o
      2194        680        721          8         16       9337   esp01_app.o
       400          0          0          0          0       2377   gpio.o
       602         24         64          0        277       8846   gps_app.o
       200          0          0          0         84       5281   i2c.o
      2014         84        252          0          0      23032   lcd_display_hal.o
      2358        140        435          0          0       8199   lcd_init_hal.o
      2580        312        107          0         52      13676   lcd_map_display.o
       236          0          0          0          0       2950   main.o
      4002        536       2913          0       7321      12463   navigation_app.o
       450          0          0          0          0       6568   ringbuffer.o
       206         44          0         96          5       4381   scheduler.o
        36          8        392          0       4096        852   startup_stm32f407xx.o
       204          0          0          8          4       7340   stm32f4xx_hal.o
       196          0          0          0          0      10757   stm32f4xx_hal_cortex.o
      1146          6          8          0          0      10569   stm32f4xx_hal_dma.o
       450          0          0          0          0       5387   stm32f4xx_hal_gpio.o
       356          0          0          0          0      44441   stm32f4xx_hal_i2c.o
        56          0          0          0          0       1495   stm32f4xx_hal_msp.o
      1480          0          0          0          0       7511   stm32f4xx_hal_rcc.o
      1634          6          0          0          0      57465   stm32f4xx_hal_tim.o
       262          0          0          0          0      21522   stm32f4xx_hal_tim_ex.o
      4216          0          0          0          0      32280   stm32f4xx_hal_uart.o
       128          0          0          0          0       4873   stm32f4xx_it.o
        18          0         24          4          0       2624   system_stm32f4xx.o
      4406        792       2280          8          8      13350   tft_app.o
       808          0          0          0        216       9505   tim.o
      1638        156       1349          0          4       3291   uart2_app.o
       224         28          0          0       1548       4718   uart2_driver.o
        14          0          0          0          0        588   uart3_app.o
        96          0          0          0        268       4617   uart3_driver.o
       208         64          0          0          4       4171   uart6_app.o
       100          0          0          0       1036       4610   uart6_driver.o
      1832        236        885          0          8       6350   uart_app.o
       192          0          0          0        396       5159   uart_driver.o
      1202          0          0          0        744       9149   usart.o

    ----------------------------------------------------------------------
     36400       <USER>       <GROUP>        124      16096     373280   Object Totals
         0          0         32          0          0          0   (incl. Generated)
       132          4          2          0          9          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       744        106        152          0          0        352   atan.o
       512         64          0          0          0        208   atan2.o
        56         10          0          0          0        132   atof.o
       200         20          0          0          0        164   cos.o
       368         46         48          0          0        200   cos_i.o
       104         16          0          0          0        496   dunder.o
        24          0          0          0          0        124   fabs.o
       260          6          0          0          0        144   fmod.o
        48          0          0          0          0        124   fpclassify.o
       248          0          0          0          0        152   poly.o
         0          0          8          0          0          0   qnan.o
      1080        142        204          0          0        188   rred.o
       200         20          0          0          0        164   sin.o
       304         24         40          0          0        208   sin_i.o
       122          0          0          0          0        148   sqrt.o
        64          0          0          0          0         84   _sgetc.o
        40          6         64          0          0         68   ctype_c.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        24         12          0          4          0        136   errno.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
        10          0          0          0          0         68   isspace_c.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        26          0          0          0          0         80   memcmp.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
      2352         90          0          0          0        604   printfa.o
       878         12          0          0          0        216   scanf_fp.o
        20          0          0          0          0         68   strchr.o
        28          0          0          0          0         76   strcmp.o
        14          0          0          0          0         68   strlen.o
        30          0          0          0          0         80   strncmp.o
        24          0          0          0          0         76   strncpy.o
        36          0          0          0          0         80   strstr.o
       168         12          0          0          0        124   strtod.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdcmple.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        62          0          0          0          0         80   dfixi.o
        48          0          0          0          0         68   dfixul.o
        34          0          0          0          0         76   dflti.o
        26          0          0          0          0         76   dfltui.o
        24          0          0          0          0         76   dfltul.o
       228          0          0          0          0         96   dmul.o
       134          0          0          0          0         96   drem.o
       162          0          0          0          0        100   dsqrt.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
     10176        <USER>        <GROUP>          4          0       6804   Library Totals
        22          4          4          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      4270        454        452          0          0       2804   m_wm.l
      4124        150         64          4          0       2448   mc_w.l
      1760          0          0          0          0       1552   mf_w.l

    ----------------------------------------------------------------------
     10176        <USER>        <GROUP>          4          0       6804   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     46576       3728       9984        128      16096     375724   Grand Totals
     46576       3728       9984        128      16096     375724   ELF Image Totals
     46576       3728       9984        128          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                56560 (  55.23kB)
    Total RW  Size (RW Data + ZI Data)             16224 (  15.84kB)
    Total ROM Size (Code + RO Data + RW Data)      56688 (  55.36kB)

==============================================================================

