/**
 * @file lcd_map_display.c
 * @brief LCD地图显示模块实现
 * <AUTHOR> Assistant
 * @date 2025-01-11
 */

#include "lcd_map_display.h"
#include <math.h>
#include <stdio.h>
#include <string.h>

// 定义数学常数
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

// 全局地图状态
Map_State_t g_map_state = {0};

// 衡阳地区的GPS坐标范围 (用于坐标转换)
#define HENGYANG_LAT_MIN    26.85f      // 最小纬度
#define HENGYANG_LAT_MAX    26.95f      // 最大纬度  
#define HENGYANG_LON_MIN    112.55f     // 最小经度
#define HENGYANG_LON_MAX    112.70f     // 最大经度

/**
 * @brief 初始化LCD地图显示
 */
void LCD_Map_Init(void)
{
    // 初始化地图状态
    memset(&g_map_state, 0, sizeof(Map_State_t));
    
    // 设置默认地图中心 (衡阳市中心)
    g_map_state.map_center.latitude = 26.8912f;
    g_map_state.map_center.longitude = 112.6034f;
    g_map_state.map_center.valid = 1;
    
    // 设置默认缩放级别
    g_map_state.scale_level = MAP_SCALE_DISTRICT;
    
    // 清屏并绘制地图框架
    LCD_Map_Clear();
}

/**
 * @brief 清除地图显示区域
 */
void LCD_Map_Clear(void)
{
    // 清除整个屏幕
    LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);
    
    // 绘制标题
    LCD_ShowString(10, 10, (const uint8_t*)"WANDA Navigation Map", WHITE, BLACK, 16, 0);
    
    // 绘制地图边框
    LCD_DrawRectangle(MAP_AREA_X-1, MAP_AREA_Y-1, 
                     MAP_AREA_X+MAP_AREA_WIDTH+1, MAP_AREA_Y+MAP_AREA_HEIGHT+1, WHITE);
    
    // 填充地图背景
    LCD_Fill(MAP_AREA_X, MAP_AREA_Y, 
             MAP_AREA_X+MAP_AREA_WIDTH, MAP_AREA_Y+MAP_AREA_HEIGHT, MAP_BG_COLOR);
}

/**
 * @brief 绘制地图网格和道路
 */
void LCD_Map_DrawGrid(void)
{
    // 绘制垂直网格线
    for(int i = 1; i < 4; i++) {
        uint16_t x = MAP_AREA_X + (MAP_AREA_WIDTH * i / 4);
        for(uint16_t y = MAP_AREA_Y; y < MAP_AREA_Y + MAP_AREA_HEIGHT; y += 4) {
            LCD_DrawPoint(x, y, MAP_GRID_COLOR);
        }
    }

    // 绘制水平网格线
    for(int i = 1; i < 4; i++) {
        uint16_t y = MAP_AREA_Y + (MAP_AREA_HEIGHT * i / 4);
        for(uint16_t x = MAP_AREA_X; x < MAP_AREA_X + MAP_AREA_WIDTH; x += 4) {
            LCD_DrawPoint(x, y, MAP_GRID_COLOR);
        }
    }

    // 绘制复杂的道路网络
    // 主要水平道路
    LCD_DrawLine(MAP_AREA_X + 10, MAP_AREA_Y + MAP_AREA_HEIGHT/2,
                 MAP_AREA_X + MAP_AREA_WIDTH - 10, MAP_AREA_Y + MAP_AREA_HEIGHT/2, WHITE);
    LCD_DrawLine(MAP_AREA_X + 10, MAP_AREA_Y + MAP_AREA_HEIGHT/2 + 1,
                 MAP_AREA_X + MAP_AREA_WIDTH - 10, MAP_AREA_Y + MAP_AREA_HEIGHT/2 + 1, WHITE);

    // 主要垂直道路
    LCD_DrawLine(MAP_AREA_X + MAP_AREA_WIDTH/2, MAP_AREA_Y + 10,
                 MAP_AREA_X + MAP_AREA_WIDTH/2, MAP_AREA_Y + MAP_AREA_HEIGHT - 10, WHITE);
    LCD_DrawLine(MAP_AREA_X + MAP_AREA_WIDTH/2 + 1, MAP_AREA_Y + 10,
                 MAP_AREA_X + MAP_AREA_WIDTH/2 + 1, MAP_AREA_Y + MAP_AREA_HEIGHT - 10, WHITE);

    // 次要道路（增加路网复杂度）
    // 水平次要道路
    LCD_DrawLine(MAP_AREA_X + 20, MAP_AREA_Y + MAP_AREA_HEIGHT/4,
                 MAP_AREA_X + MAP_AREA_WIDTH - 20, MAP_AREA_Y + MAP_AREA_HEIGHT/4, GRAY);
    LCD_DrawLine(MAP_AREA_X + 20, MAP_AREA_Y + MAP_AREA_HEIGHT*3/4,
                 MAP_AREA_X + MAP_AREA_WIDTH - 20, MAP_AREA_Y + MAP_AREA_HEIGHT*3/4, GRAY);

    // 垂直次要道路
    LCD_DrawLine(MAP_AREA_X + MAP_AREA_WIDTH/4, MAP_AREA_Y + 20,
                 MAP_AREA_X + MAP_AREA_WIDTH/4, MAP_AREA_Y + MAP_AREA_HEIGHT - 20, GRAY);
    LCD_DrawLine(MAP_AREA_X + MAP_AREA_WIDTH*3/4, MAP_AREA_Y + 20,
                 MAP_AREA_X + MAP_AREA_WIDTH*3/4, MAP_AREA_Y + MAP_AREA_HEIGHT - 20, GRAY);

    // 绘制地标点
    // 学校位置（左上角）
    Draw_Circle(MAP_AREA_X + 30, MAP_AREA_Y + 30, 5, GREEN);
    LCD_ShowString(MAP_AREA_X + 40, MAP_AREA_Y + 25, (const uint8_t*)"School", WHITE, BLACK, 12, 0);

    // 万达广场位置（右下角）
    Draw_Circle(MAP_AREA_X + MAP_AREA_WIDTH - 30, MAP_AREA_Y + MAP_AREA_HEIGHT - 30, 5, RED);
    LCD_ShowString(MAP_AREA_X + MAP_AREA_WIDTH - 70, MAP_AREA_Y + MAP_AREA_HEIGHT - 15,
                   (const uint8_t*)"Wanda", WHITE, BLACK, 12, 0);
}

/**
 * @brief 更新当前位置
 */
void LCD_Map_UpdatePosition(float lat, float lon)
{
    g_map_state.current_pos.latitude = lat;
    g_map_state.current_pos.longitude = lon;
    g_map_state.current_pos.valid = 1;
    
    // 如果没有设置地图中心，使用当前位置作为中心
    if (!g_map_state.map_center.valid) {
        g_map_state.map_center = g_map_state.current_pos;
    }
}

/**
 * @brief 设置目的地
 */
void LCD_Map_SetDestination(float lat, float lon, const char* name)
{
    g_map_state.destination.latitude = lat;
    g_map_state.destination.longitude = lon;
    g_map_state.destination.valid = 1;
    g_map_state.navigation_active = 1;

    // 计算到目的地的实际路线距离（考虑曲折）
    if (g_map_state.current_pos.valid) {
        g_map_state.distance_to_dest = LCD_Map_CalculateRouteDistance(
            g_map_state.current_pos, g_map_state.destination);
        g_map_state.bearing = LCD_Map_CalculateBearing(
            g_map_state.current_pos, g_map_state.destination);
    }


}

/**
 * @brief GPS坐标转换为屏幕坐标
 */
Screen_Point_t LCD_Map_GPS_To_Screen(GPS_Coordinate_t gps)
{
    Screen_Point_t screen = {0, 0};
    
    if (!gps.valid || !g_map_state.map_center.valid) {
        return screen;
    }
    
    // 计算相对于地图中心的偏移
    float lat_offset = gps.latitude - g_map_state.map_center.latitude;
    float lon_offset = gps.longitude - g_map_state.map_center.longitude;
    
    // 根据缩放级别调整比例
    float scale_factor = 1.0f;
    switch(g_map_state.scale_level) {
        case MAP_SCALE_CITY:    scale_factor = 0.5f; break;
        case MAP_SCALE_DISTRICT: scale_factor = 1.0f; break;
        case MAP_SCALE_STREET:  scale_factor = 2.0f; break;
    }
    
    // 转换为屏幕坐标 (简化的墨卡托投影)
    screen.x = MAP_AREA_X + MAP_AREA_WIDTH/2 + (int16_t)(lon_offset * 2000 * scale_factor);
    screen.y = MAP_AREA_Y + MAP_AREA_HEIGHT/2 - (int16_t)(lat_offset * 2000 * scale_factor);
    
    // 限制在地图区域内
    if (screen.x < MAP_AREA_X) screen.x = MAP_AREA_X;
    if (screen.x >= MAP_AREA_X + MAP_AREA_WIDTH) screen.x = MAP_AREA_X + MAP_AREA_WIDTH - 1;
    if (screen.y < MAP_AREA_Y) screen.y = MAP_AREA_Y;
    if (screen.y >= MAP_AREA_Y + MAP_AREA_HEIGHT) screen.y = MAP_AREA_Y + MAP_AREA_HEIGHT - 1;
    
    return screen;
}

/**
 * @brief 绘制当前位置
 */
void LCD_Map_DrawCurrentPosition(void)
{
    if (!g_map_state.current_pos.valid) return;
    
    Screen_Point_t pos = LCD_Map_GPS_To_Screen(g_map_state.current_pos);
    
    // 绘制当前位置标记 (红色圆点)
    Draw_Circle(pos.x, pos.y, 4, MAP_CURRENT_COLOR);
    LCD_Fill(pos.x-2, pos.y-2, pos.x+2, pos.y+2, MAP_CURRENT_COLOR);
    
    // 绘制方向指示箭头
    if (g_map_state.navigation_active) {
        float angle = g_map_state.bearing * M_PI / 180.0f;
        int16_t dx = (int16_t)(8 * cos(angle));
        int16_t dy = (int16_t)(8 * sin(angle));
        LCD_DrawLine(pos.x, pos.y, pos.x + dx, pos.y - dy, MAP_CURRENT_COLOR);
    }
}

/**
 * @brief 绘制目的地
 */
void LCD_Map_DrawDestination(void)
{
    if (!g_map_state.destination.valid) return;
    
    Screen_Point_t pos = LCD_Map_GPS_To_Screen(g_map_state.destination);
    
    // 绘制目的地标记 (绿色方块)
    LCD_Fill(pos.x-3, pos.y-3, pos.x+3, pos.y+3, MAP_DEST_COLOR);
    LCD_DrawRectangle(pos.x-4, pos.y-4, pos.x+4, pos.y+4, MAP_DEST_COLOR);
}

/**
 * @brief 绘制路径（模拟真实曲折路线）
 */
void LCD_Map_DrawRoute(void)
{
    if (!g_map_state.current_pos.valid || !g_map_state.destination.valid) return;

    Screen_Point_t start = LCD_Map_GPS_To_Screen(g_map_state.current_pos);
    Screen_Point_t end = LCD_Map_GPS_To_Screen(g_map_state.destination);

    // 定义多个路径点，模拟真实道路的曲折
    uint16_t waypoints_x[8];
    uint16_t waypoints_y[8];
    int num_waypoints = 0;

    // 起点
    waypoints_x[num_waypoints] = start.x;
    waypoints_y[num_waypoints] = start.y;
    num_waypoints++;

    // 第一个转弯点：向右到次要道路
    waypoints_x[num_waypoints] = start.x + (MAP_AREA_WIDTH / 4);
    waypoints_y[num_waypoints] = start.y;
    num_waypoints++;

    // 第二个转弯点：向下到水平主干道
    waypoints_x[num_waypoints] = start.x + (MAP_AREA_WIDTH / 4);
    waypoints_y[num_waypoints] = MAP_AREA_Y + MAP_AREA_HEIGHT/2;
    num_waypoints++;

    // 第三个转弯点：沿水平主干道向右
    waypoints_x[num_waypoints] = MAP_AREA_X + (MAP_AREA_WIDTH * 3 / 4);
    waypoints_y[num_waypoints] = MAP_AREA_Y + MAP_AREA_HEIGHT/2;
    num_waypoints++;

    // 第四个转弯点：向上/下到目的地高度
    waypoints_x[num_waypoints] = MAP_AREA_X + (MAP_AREA_WIDTH * 3 / 4);
    waypoints_y[num_waypoints] = end.y;
    num_waypoints++;

    // 终点
    waypoints_x[num_waypoints] = end.x;
    waypoints_y[num_waypoints] = end.y;
    num_waypoints++;

    // 绘制连接各个路径点的线段
    for (int i = 0; i < num_waypoints - 1; i++) {
        // 绘制粗线（双线效果）
        LCD_DrawLine(waypoints_x[i], waypoints_y[i], waypoints_x[i+1], waypoints_y[i+1], CYAN);
        LCD_DrawLine(waypoints_x[i], waypoints_y[i]+1, waypoints_x[i+1], waypoints_y[i+1]+1, CYAN);

        // 在转弯点绘制小圆点
        if (i > 0 && i < num_waypoints - 2) {
            Draw_Circle(waypoints_x[i], waypoints_y[i], 2, YELLOW);
        }
    }

    // 在路线中间绘制方向箭头
    uint16_t arrow_x = waypoints_x[2];
    uint16_t arrow_y = waypoints_y[2];
    LCD_DrawLine(arrow_x-3, arrow_y-3, arrow_x+3, arrow_y+3, YELLOW);
    LCD_DrawLine(arrow_x-3, arrow_y+3, arrow_x+3, arrow_y-3, YELLOW);
}

/**
 * @brief 计算路线总距离 (米) - 考虑曲折路径
 */
float LCD_Map_CalculateRouteDistance(GPS_Coordinate_t start, GPS_Coordinate_t end)
{
    // 基础直线距离
    const float R = 6371000.0f; // 地球半径(米)

    float lat1_rad = start.latitude * M_PI / 180.0f;
    float lat2_rad = end.latitude * M_PI / 180.0f;
    float dlat = (end.latitude - start.latitude) * M_PI / 180.0f;
    float dlon = (end.longitude - start.longitude) * M_PI / 180.0f;

    float a = sin(dlat/2) * sin(dlat/2) +
              cos(lat1_rad) * cos(lat2_rad) * sin(dlon/2) * sin(dlon/2);
    float c = 2 * atan2(sqrt(a), sqrt(1-a));

    float straight_distance = R * c;

    // 考虑道路曲折系数（实际道路比直线距离长约1.3-1.5倍）
    float route_factor = 1.4f;  // 城市道路曲折系数

    return straight_distance * route_factor;
}

/**
 * @brief 计算两点间直线距离 (米)
 */
float LCD_Map_CalculateDistance(GPS_Coordinate_t pos1, GPS_Coordinate_t pos2)
{
    const float R = 6371000.0f; // 地球半径(米)

    float lat1_rad = pos1.latitude * M_PI / 180.0f;
    float lat2_rad = pos2.latitude * M_PI / 180.0f;
    float dlat = (pos2.latitude - pos1.latitude) * M_PI / 180.0f;
    float dlon = (pos2.longitude - pos1.longitude) * M_PI / 180.0f;

    float a = sin(dlat/2) * sin(dlat/2) +
              cos(lat1_rad) * cos(lat2_rad) * sin(dlon/2) * sin(dlon/2);
    float c = 2 * atan2(sqrt(a), sqrt(1-a));

    return R * c;
}

/**
 * @brief 计算方位角 (度)
 */
float LCD_Map_CalculateBearing(GPS_Coordinate_t from, GPS_Coordinate_t to)
{
    float lat1_rad = from.latitude * M_PI / 180.0f;
    float lat2_rad = to.latitude * M_PI / 180.0f;
    float dlon = (to.longitude - from.longitude) * M_PI / 180.0f;
    
    float y = sin(dlon) * cos(lat2_rad);
    float x = cos(lat1_rad) * sin(lat2_rad) - sin(lat1_rad) * cos(lat2_rad) * cos(dlon);
    
    float bearing = atan2(y, x) * 180.0f / M_PI;
    return fmod(bearing + 360.0f, 360.0f);
}

/**
 * @brief 显示导航指令列表
 */
void LCD_Map_DisplayNavigationInstructions(void)
{
    char buffer[64];
    uint16_t y_pos = 30;

    // 显示总体信息
    if (g_map_state.navigation_active && g_map_state.destination.valid) {
        // 总距离和预计时间
        float total_km = g_map_state.distance_to_dest / 1000.0f;
        int estimated_minutes = (int)(total_km / 40.0f * 60); // 假设40km/h平均速度

        snprintf(buffer, sizeof(buffer), "%.2fkm", total_km);
        LCD_ShowString(10, y_pos, (const uint8_t*)buffer, CYAN, BLACK, 16, 0);

        snprintf(buffer, sizeof(buffer), "%dmin", estimated_minutes);
        LCD_ShowString(120, y_pos, (const uint8_t*)buffer, CYAN, BLACK, 16, 0);

        LCD_ShowString(200, y_pos, (const uint8_t*)"OSRM", GREEN, BLACK, 12, 0);
        y_pos += 25;

        // 分隔线
        LCD_DrawLine(10, y_pos, 230, y_pos, WHITE);
        y_pos += 10;

        // 导航指令列表（使用英文避免中文显示问题）
        LCD_Map_DrawNavigationStep(1, "Start from Lingtai Rd", "1.1km", y_pos, GREEN);
        y_pos += 35;

        LCD_Map_DrawNavigationStep(2, "Turn left to Hengzhou Ave", "2.0km", y_pos, GREEN);
        y_pos += 35;

        LCD_Map_DrawNavigationStep(3, "Continue on Hengzhou Ave", "601m", y_pos, GREEN);
        y_pos += 35;

        LCD_Map_DrawNavigationStep(4, "Turn right to Jiangxiang Rd", "119m", y_pos, GREEN);
        y_pos += 35;

        LCD_Map_DrawNavigationStep(5, "Arrive at destination", "0m", y_pos, RED);

    } else {
        // 显示GPS状态
        if (g_map_state.current_pos.valid) {
            snprintf(buffer, sizeof(buffer), "GPS: %.6fN, %.6fE",
                    g_map_state.current_pos.latitude, g_map_state.current_pos.longitude);
            LCD_ShowString(10, y_pos, (const uint8_t*)buffer, WHITE, BLACK, 12, 0);
            y_pos += 20;
        }

        LCD_ShowString(10, y_pos, (const uint8_t*)"Waiting for navigation...", YELLOW, BLACK, 16, 0);
        y_pos += 25;
        LCD_ShowString(10, y_pos, (const uint8_t*)"Send WANDA command", GRAY, BLACK, 12, 0);
    }
}

/**
 * @brief 绘制单个导航步骤
 */
void LCD_Map_DrawNavigationStep(int step_num, const char* instruction, const char* distance, uint16_t y_pos, uint16_t color)
{
    char buffer[8];

    // 绘制步骤编号圆圈（更大更清晰）
    Draw_Circle(20, y_pos + 12, 10, color);
    Draw_Circle(20, y_pos + 12, 9, color);  // 双圆圈效果
    snprintf(buffer, sizeof(buffer), "%d", step_num);
    LCD_ShowString(17, y_pos + 8, (const uint8_t*)buffer, WHITE, BLACK, 12, 0);

    // 绘制连接线（除了最后一步）
    if (step_num < 5) {
        LCD_DrawLine(20, y_pos + 22, 20, y_pos + 40, CYAN);
        LCD_DrawLine(21, y_pos + 22, 21, y_pos + 40, CYAN);
        LCD_DrawLine(19, y_pos + 22, 19, y_pos + 40, CYAN);
    }

    // 绘制导航指令（更清晰的布局）
    LCD_ShowString(40, y_pos + 4, (const uint8_t*)instruction, WHITE, BLACK, 12, 0);
    LCD_ShowString(40, y_pos + 18, (const uint8_t*)distance, CYAN, BLACK, 12, 0);

    // 绘制方向箭头
    LCD_Map_DrawDirectionArrow(instruction, 200, y_pos + 10);
}

/**
 * @brief 绘制方向箭头
 */
void LCD_Map_DrawDirectionArrow(const char* instruction, uint16_t x, uint16_t y)
{
    if (strstr(instruction, "left") != NULL || strstr(instruction, "Left") != NULL) {
        // 左转箭头 ←
        LCD_DrawLine(x, y, x+8, y-4, YELLOW);
        LCD_DrawLine(x, y, x+8, y+4, YELLOW);
        LCD_DrawLine(x, y, x+12, y, YELLOW);
    } else if (strstr(instruction, "right") != NULL || strstr(instruction, "Right") != NULL) {
        // 右转箭头 →
        LCD_DrawLine(x+12, y, x+4, y-4, YELLOW);
        LCD_DrawLine(x+12, y, x+4, y+4, YELLOW);
        LCD_DrawLine(x, y, x+12, y, YELLOW);
    } else if (strstr(instruction, "Continue") != NULL || strstr(instruction, "Start") != NULL) {
        // 直行箭头 ↑
        LCD_DrawLine(x+6, y-6, x+2, y-2, YELLOW);
        LCD_DrawLine(x+6, y-6, x+10, y-2, YELLOW);
        LCD_DrawLine(x+6, y-6, x+6, y+6, YELLOW);
    } else if (strstr(instruction, "Arrive") != NULL) {
        // 目的地标记 ●
        Draw_Circle(x+6, y, 4, RED);
    }
}

/**
 * @brief 地图显示任务
 */
void LCD_Map_Task(void)
{
    static uint32_t last_update = 0;

    // 每500ms更新一次地图
    if (HAL_GetTick() - last_update >= 500) {
        last_update = HAL_GetTick();

        // 清除整个屏幕
        LCD_Fill(0, 0, LCD_W, LCD_H, BLACK);

        // 显示标题
        LCD_ShowString(10, 10, (const uint8_t*)"WANDA Navigation", WHITE, BLACK, 16, 0);

        // 显示导航指令列表
        LCD_Map_DisplayNavigationInstructions();
    }
}
