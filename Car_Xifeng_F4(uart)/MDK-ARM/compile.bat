@echo off
echo Starting compilation...
cd /d "%~dp0"
echo Current directory: %CD%

REM Try the path from your compiler output
set KEIL_PATH=C:\Users\<USER>\AppData\Local\Keil_v5\ARM\ARMCLANG\Bin\..\..\..\UV4\UV4.exe

if exist "%KEIL_PATH%" (
    echo Found Keil at %KEIL_PATH%
    "%KEIL_PATH%" -b Car_Xifeng_F4.uvprojx
) else (
    echo Keil not found at %KEIL_PATH%
    echo Please manually compile in Keil IDE
)
echo Compilation finished.
pause
