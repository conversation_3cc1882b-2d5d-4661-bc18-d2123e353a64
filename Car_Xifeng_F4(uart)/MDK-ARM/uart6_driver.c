#include "uart6_driver.h"


uint8_t uart6_rx_dma_buffer[UART6_BUFFER_SIZE];


uint8_t uart6_ring_buffer_input[UART6_BUFFER_SIZE]; 

struct rt_ringbuffer uart6_ring_buffer; 


uint8_t uart6_data_buffer[UART6_BUFFER_SIZE]; 


int Uart6_Printf(UART_HandleTypeDef *huart, const char *format, ...)
{
    char buffer[512]; 
    va_list arg;      
    int len;          

    va_start(arg, format);

    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);


    HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
    return len;
}


void HAL_UARTEx_RxEventCallback_UART6(UART_HandleTypeDef *huart, uint16_t Size)
{

    if (huart->Instance == USART6)
    {

        HAL_UART_DMAStop(huart);


        rt_ringbuffer_put(&uart6_ring_buffer, uart6_rx_dma_buffer, Size);
        

        memset(uart6_rx_dma_buffer, 0, sizeof(uart6_rx_dma_buffer));

        HAL_UARTEx_ReceiveToIdle_DMA(&huart6, uart6_rx_dma_buffer, sizeof(uart6_rx_dma_buffer));


        __HAL_DMA_DISABLE_IT(&hdma_usart6_rx, DMA_IT_HT);
    }
}
