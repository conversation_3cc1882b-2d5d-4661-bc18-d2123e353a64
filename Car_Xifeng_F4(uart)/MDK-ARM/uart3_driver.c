#include "uart3_driver.h"

// ����2 DMA ���ջ�����
uint8_t uart3_rx_dma_buffer[UART3_BUFFER_SIZE]; 

// ����2 ���λ�������Ӧ����������
uint8_t uart3_ring_buffer_input[UART3_BUFFER_SIZE]; 
// ����2 ���λ�����
struct rt_ringbuffer uart3_ring_buffer; 

// ����2 ���ݴ���������
uint8_t uart3_data_buffer[UART3_BUFFER_SIZE]; 

/**
 * @brief ����2��ʽ���������
 * @param huart UART���
 * @param format ��ʽ���ַ���
 * @return ������ַ���
 */
int Uart3_Printf(UART_HandleTypeDef *huart, const char *format, ...)
{
    char buffer[512]; // ��ʱ�洢��ʽ������ַ���
    va_list arg;      // �ɱ�����б�
    int len;          // ����ַ�������

    va_start(arg, format);
    // ��ȫ�ظ�ʽ���ַ����� buffer
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);

    // ͨ�� HAL �ⷢ�� buffer �е�����
    HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
    return len;
}

/**
 * @brief ����2������ɻص�����
 * @param huart UART���
 * @param Size ���յ������ݴ�С
 */
void HAL_UARTEx_RxEventCallback_UART3(UART_HandleTypeDef *huart, uint16_t Size)
{
    // 确认是目标串口 (USART3)
    if (huart->Instance == USART3)
    {
        // UART3 DMA接收完成处理
        // ����ֹͣ��ǰ�� DMA ���� (�����ڴ����б�����)
        HAL_UART_DMAStop(huart);

        // �� DMA ����������Ч���� (Size ���ֽ�) ���Ƶ����λ�����
        rt_ringbuffer_put(&uart3_ring_buffer, uart3_rx_dma_buffer, Size);
        
        // ��� DMA ���ջ�������Ϊ�´ν�����׼��
        memset(uart3_rx_dma_buffer, 0, sizeof(uart3_rx_dma_buffer));

        // �������� DMA ����
        HAL_UARTEx_ReceiveToIdle_DMA(&huart3, uart3_rx_dma_buffer, sizeof(uart3_rx_dma_buffer));

        // 关闭半满中断
        __HAL_DMA_DISABLE_IT(&hdma_usart3_rx, DMA_IT_HT);
    }
}
