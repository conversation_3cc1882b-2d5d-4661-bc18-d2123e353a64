@echo off
echo ========================================
echo    衡阳GPS追踪系统 - 一键部署脚本
echo ========================================
echo.

echo 🚀 准备部署到GitHub Pages...
echo.

echo 📋 部署前检查清单：
echo [1] 已创建GitHub仓库
echo [2] 已配置ThingSpeak API密钥
echo [3] 已安装Git工具
echo.

set /p confirm="确认继续部署？(y/n): "
if /i "%confirm%" neq "y" (
    echo 部署已取消
    pause
    exit /b
)

echo.
echo 🔧 初始化Git仓库...
git init

echo.
echo 📁 添加所有文件...
git add .

echo.
echo 💾 提交更改...
git commit -m "Deploy GPS Tracker for Hengyang - Initial version"

echo.
echo 🌐 设置远程仓库...
set /p repo_url="请输入您的GitHub仓库URL: "
git remote add origin %repo_url%

echo.
echo 🚀 推送到GitHub...
git branch -M main
git push -u origin main

echo.
echo ========================================
echo ✅ 部署完成！
echo ========================================
echo.
echo 📱 访问您的GPS追踪系统：
echo https://您的用户名.github.io/仓库名称/
echo.
echo 🔧 下一步：
echo 1. 在GitHub仓库设置中启用Pages
echo 2. 配置ThingSpeak API密钥
echo 3. 测试STM32数据发送
echo.
pause
