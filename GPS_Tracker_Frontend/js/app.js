// 主应用程序类
class GPSTrackerApp {
    constructor() {
        this.map = null;
        this.updateInterval = null;
        this.isRunning = false;
        this.lastUpdateTime = null;
        this.connectionStatus = 'offline';
        
        this.init();
    }

    // 初始化应用
    async init() {
        try {
            log('GPS追踪应用启动中...', 'info');
            
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initApp());
            } else {
                this.initApp();
            }
            
        } catch (error) {
            log(`应用初始化失败: ${error.message}`, 'error');
            Utils.showNotification('应用初始化失败', 'error');
        }
    }

    // 初始化应用组件
    async initApp() {
        try {
            // 初始化地图
            this.map = new GPSMap('map');
            
            // 设置UI事件监听器
            this.setupUIEventListeners();
            
            // 检查ThingSpeak连接
            await this.checkConnection();
            
            // 加载历史轨迹
            await this.loadHistoryTrack();
            
            // 开始数据更新
            this.startUpdating();
            
            log('应用初始化完成', 'success');
            Utils.showNotification('系统已启动', 'success');
            
        } catch (error) {
            log(`应用组件初始化失败: ${error.message}`, 'error');
            Utils.showNotification('初始化失败，使用模拟数据', 'error');
            
            // 使用模拟数据
            this.startMockMode();
        }
    }

    // 设置UI事件监听器
    setupUIEventListeners() {
        // 居中按钮
        const centerBtn = document.getElementById('center-btn');
        if (centerBtn) {
            centerBtn.addEventListener('click', () => {
                if (this.map.currentMarker) {
                    const position = this.map.currentMarker.getLatLng();
                    this.map.centerOnLocation([position.lat, position.lng]);
                    log('手动居中到当前位置');
                }
            });
        }

        // 刷新按钮
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', async () => {
                refreshBtn.innerHTML = '<div class="loading"></div> 刷新中...';
                await this.updateGPSData();
                refreshBtn.innerHTML = '🔄 刷新数据';
            });
        }

        // 清除轨迹按钮
        const clearTrackBtn = document.getElementById('clear-track-btn');
        if (clearTrackBtn) {
            clearTrackBtn.addEventListener('click', () => {
                this.map.clearTrack();
                Utils.showNotification('轨迹已清除', 'success');
            });
        }

        // 全屏按钮
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => {
                this.map.toggleFullscreen();
            });
        }

        // 自动居中复选框
        const autoCenterCheckbox = document.getElementById('auto-center');
        if (autoCenterCheckbox) {
            autoCenterCheckbox.addEventListener('change', (e) => {
                this.map.setAutoCenter(e.target.checked);
            });
        }

        // 显示轨迹复选框
        const showTrackCheckbox = document.getElementById('show-track');
        if (showTrackCheckbox) {
            showTrackCheckbox.addEventListener('change', (e) => {
                this.map.setShowTrack(e.target.checked);
            });
        }

        // 更新间隔选择
        const updateIntervalSelect = document.getElementById('update-interval');
        if (updateIntervalSelect) {
            updateIntervalSelect.addEventListener('change', (e) => {
                const newInterval = parseInt(e.target.value);
                this.setUpdateInterval(newInterval);
                log(`更新间隔设置为: ${newInterval/1000}秒`);
            });
        }
    }

    // 检查连接状态
    async checkConnection() {
        try {
            const isConnected = await thingSpeakAPI.checkConnection();
            this.connectionStatus = isConnected ? 'online' : 'offline';
            
            if (isConnected) {
                log('ThingSpeak连接正常', 'success');
                Utils.showNotification('已连接到ThingSpeak', 'success');
            } else {
                throw new Error('无法连接到ThingSpeak');
            }
            
        } catch (error) {
            log(`连接检查失败: ${error.message}`, 'error');
            this.connectionStatus = 'offline';
            Utils.showNotification('连接失败，使用模拟数据', 'error');
        }
    }

    // 加载历史轨迹
    async loadHistoryTrack() {
        try {
            let historyData;
            
            if (this.connectionStatus === 'online') {
                historyData = await thingSpeakAPI.getHistoryData(50);
            } else {
                // 使用模拟数据
                historyData = mockDataGenerator.generateHistoryData(20);
            }
            
            if (historyData && historyData.length > 0) {
                this.map.loadHistoryTrack(historyData);
                log(`加载了${historyData.length}个历史轨迹点`, 'success');
            }
            
        } catch (error) {
            log(`加载历史轨迹失败: ${error.message}`, 'error');
        }
    }

    // 更新GPS数据
    async updateGPSData() {
        try {
            let gpsData;
            
            if (this.connectionStatus === 'online') {
                gpsData = await thingSpeakAPI.getLatestData();
            } else {
                // 使用模拟数据
                gpsData = mockDataGenerator.generateMockData();
            }
            
            if (gpsData && gpsData.latitude && gpsData.longitude) {
                // 更新地图
                this.map.updateCurrentLocation(gpsData);
                
                // 更新UI
                this.updateUI(gpsData);
                
                this.lastUpdateTime = new Date();
                log('GPS数据更新成功', 'success');
                
            } else {
                throw new Error('GPS数据无效');
            }
            
        } catch (error) {
            log(`GPS数据更新失败: ${error.message}`, 'error');
            Utils.showNotification('数据更新失败', 'error');
        }
    }

    // 更新UI显示
    updateUI(gpsData) {
        // 更新连接状态
        const statusElement = document.getElementById('connection-status');
        if (statusElement) {
            let statusText = '离线';
            let statusClass = 'offline';

            if (this.connectionStatus === 'online') {
                // 检查数据新鲜度
                if (gpsData && gpsData.created_at) {
                    const dataTime = new Date(gpsData.created_at);
                    const now = new Date();
                    const timeDiff = (now - dataTime) / 1000 / 60; // 分钟差

                    if (timeDiff <= 5) {
                        statusText = '在线';
                        statusClass = 'online';
                    } else {
                        statusText = `离线 (${Math.floor(timeDiff)}分钟前)`;
                        statusClass = 'offline';
                    }
                } else {
                    statusText = '无数据';
                    statusClass = 'offline';
                }
            } else {
                statusText = '模拟';
                statusClass = 'offline';
            }

            statusElement.textContent = statusText;
            statusElement.className = `status ${statusClass}`;
        }

        // 更新最后更新时间
        const lastUpdateElement = document.getElementById('last-update');
        if (lastUpdateElement) {
            lastUpdateElement.textContent = Utils.formatTime(gpsData.timestamp);
        }

        // 更新当前位置
        const locationElement = document.getElementById('current-location');
        if (locationElement) {
            locationElement.textContent = `${Utils.formatCoordinate(gpsData.latitude)}, ${Utils.formatCoordinate(gpsData.longitude)}`;
        }

        // 更新详细信息
        const latElement = document.getElementById('latitude');
        const lonElement = document.getElementById('longitude');
        const altElement = document.getElementById('altitude');
        
        if (latElement) latElement.textContent = Utils.formatCoordinate(gpsData.latitude);
        if (lonElement) lonElement.textContent = Utils.formatCoordinate(gpsData.longitude);
        if (altElement) altElement.textContent = gpsData.altitude ? `${gpsData.altitude.toFixed(1)}m` : '--';
    }

    // 开始数据更新
    startUpdating() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        
        // 立即更新一次
        this.updateGPSData();
        
        // 设置定时更新
        this.updateInterval = setInterval(() => {
            this.updateGPSData();
        }, CONFIG.APP.UPDATE_INTERVAL);
        
        log(`开始数据更新，间隔: ${CONFIG.APP.UPDATE_INTERVAL/1000}秒`, 'success');
    }

    // 停止数据更新
    stopUpdating() {
        if (!this.isRunning) return;
        
        this.isRunning = false;
        
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
        
        log('数据更新已停止', 'info');
    }

    // 设置更新间隔
    setUpdateInterval(interval) {
        CONFIG.APP.UPDATE_INTERVAL = interval;
        
        if (this.isRunning) {
            this.stopUpdating();
            this.startUpdating();
        }
    }

    // 启动模拟模式
    startMockMode() {
        this.connectionStatus = 'offline';
        log('启动模拟模式', 'info');
        
        // 加载模拟历史轨迹
        const mockHistory = mockDataGenerator.generateHistoryData(15);
        this.map.loadHistoryTrack(mockHistory);
        
        // 开始更新
        this.startUpdating();
    }
}

// 应用启动
document.addEventListener('DOMContentLoaded', () => {
    window.gpsApp = new GPSTrackerApp();
});

// 导出应用类
window.GPSTrackerApp = GPSTrackerApp;
